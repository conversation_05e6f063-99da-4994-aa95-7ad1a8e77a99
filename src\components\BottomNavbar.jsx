import React from 'react';
import { <PERSON><PERSON>, Drawer, Nav, Modal } from 'rsuite';
import { Link, useLocation } from 'react-router-dom';
import { useModalState } from '../misc/custom-hooks';
import Dashboard from './dashboard';
import UserCircleIcon from '@rsuite/icons/legacy/UserCircle';

const BottomNavbar = () => {
  const { isOpen, open, close } = useModalState();
  const { isOpen: isSupportOpen, open: openSupport, close: closeSupport } = useModalState();
  const location = useLocation();

  return (
    <div className="bottom-navbar">
      <Nav appearance="subtle" className="bottom-nav-container">
        <Nav.Item
          as={Link}
          to="/support"
          active={location.pathname === '/support'}
          onClick={openSupport}
          className="bottom-nav-item"
        >
          <i className="fa-solid fa-headset" style={{ fontSize: '24px' }}></i>
          <span>الدعم</span>
        </Nav.Item>

        <Nav.Item
          as={Link}
          to="/profile"
          active={location.pathname === '/profile'}
          onClick={open}
          className="bottom-nav-item"
        >
          <UserCircleIcon style={{ fontSize: '24px' }} />
          <span>الملف الشخصي</span>
        </Nav.Item>
      </Nav>

      <Drawer open={isOpen} onClose={close} placement="right">
        <Dashboard onSignOut={close} />
      </Drawer>

      <Modal open={isSupportOpen} onClose={closeSupport}>
        <Modal.Header>
          <Modal.Title>الدعم الفني</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>يمكنك التواصل مع فريق الدعم الفني عبر البريد الإلكتروني:</p>
          <p><strong><EMAIL></strong></p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={closeSupport} appearance="primary">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default BottomNavbar;
