import React, { useState } from "react";
import TimeAgo from "timeago-react";
import ProfileAvatar from "../ProfileAvatar";
import { auth, database } from "../../misc/firebase.config";
import { Modal, Button, Message, toaster } from "rsuite";
import { ref, get, set, remove } from "firebase/database";
import TranslatedText from "../TranslatedText";

const RoomItem = ({ room }) => {
  const { createdAt, name, lastMessage, isPrivate, allowedUsers, admins, createdBy } = room;
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isHideConfirmOpen, setIsHideConfirmOpen] = useState(false);

  // التحقق مما إذا كانت الغرفة خاصة وتم إضافة المستخدم الحالي إليها من قبل مشرف
  const isPrivateAndAdded = isPrivate &&
    allowedUsers &&
    auth.currentUser &&
    allowedUsers[auth.currentUser.uid] &&
    !room.admins[auth.currentUser.uid];

  // التحقق مما إذا كان المستخدم الحالي مشرفًا في الغرفة
  const isAdmin = auth.currentUser && admins && admins[auth.currentUser.uid];

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // معالجة حذف المجموعة
  const handleDeleteRoom = async (isSuccessful) => {
    try {
      console.log('Deleting room from RoomItem:', room);

      // الحصول على معرف الغرفة
      const roomId = room.id;
      console.log('Room ID:', roomId);

      if (!roomId) {
        console.error('Room ID is undefined');
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorRoomIdNotFound" />
          </Message>
        );
        return;
      }

      // الحصول على بيانات الغرفة قبل حذفها
      const roomRef = ref(database, `/rooms/${roomId}`);
      console.log('Room reference:', roomRef);

      const roomSnapshot = await get(roomRef);
      console.log('Room snapshot exists:', roomSnapshot.exists());

      if (roomSnapshot.exists()) {
        const roomData = roomSnapshot.val();
        console.log('Room data:', roomData);

        // تحديث الغرفة لتكون مخفية بدلاً من حذفها
        const updatedRoomData = {
          ...roomData,
          isHidden: true,
          hiddenAt: new Date().toISOString(),
          hiddenBy: auth.currentUser.uid,
          saleSuccessful: isSuccessful
        };

        console.log('Hiding room data:', updatedRoomData);

        // حفظ بيانات الغرفة المحدثة في نفس المكان
        await set(ref(database, `/rooms/${roomId}`), updatedRoomData);
        console.log('Room marked as hidden');

        // إضافة الغرفة إلى سجل الغرف المخفية للمالك
        // استخدام معرف المالك بدلاً من معرف المستخدم الحالي لتجنب مشاكل الصلاحيات
        const ownerUid = 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1';
        await set(ref(database, `/hidden-rooms/${roomId}`), {
          id: roomId,
          name: roomData.name,
          description: roomData.description,
          hiddenAt: new Date().toISOString(),
          hiddenBy: isOwner ? auth.currentUser.uid : ownerUid,
          saleSuccessful: isSuccessful
        });
        console.log('Added to hidden-rooms for owner');

        // إغلاق نافذة التأكيد
        setIsDeleteConfirmOpen(false);

        // عرض رسالة نجاح
        toaster.push(
          <Message type="success" closable duration={4000}>
            <TranslatedText text="groupDeletedSuccessfully" /> {isSuccessful ? '✅' : '❌'}
          </Message>
        );

        // تحديث الصفحة بعد حذف المجموعة
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        console.error('Room not found');
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorRoomNotFound" />
          </Message>
        );
      }
    } catch (error) {
      console.error('Error hiding room:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="errorDeletingGroup" />: {error.message}
        </Message>
      );
    }
  };

  // معالجة إخفاء المجموعة من الصفحة الرئيسية للمالك
  const handleHideRoomForOwner = async () => {
    try {
      // الحصول على معرف الغرفة
      const roomId = room.id;

      if (!roomId) {
        console.error('Room ID is undefined');
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorRoomIdNotFound" />
          </Message>
        );
        return;
      }

      // إضافة المجموعة إلى قائمة المجموعات المخفية للمالك فقط
      await set(ref(database, `/users/${auth.currentUser.uid}/hiddenRooms/${roomId}`), true);

      // إغلاق نافذة التأكيد
      setIsHideConfirmOpen(false);

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          <TranslatedText text="groupHiddenFromHomepage" />
        </Message>
      );

      // تحديث الصفحة بعد إخفاء المجموعة
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error hiding room for owner:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="errorHidingGroup" />: {error.message}
        </Message>
      );
    }
  };

  return (
    <div
      className="room-item"
      style={{
        border: room.borderColor ? `2px solid ${room.borderColor}` : 'none',
        borderRadius: room.borderColor ? '8px' : '0',
        padding: room.borderColor ? '8px' : '0'
      }}
    >
      <div
        className="d-flex justify-content-between align-items-center"
      >
        <div className="room-name-container">
          <h3
            className={`text-disappear room-name ${isPrivateAndAdded ? 'text-danger' : ''}`}
            style={{ color: room.textColor || 'inherit' }}
          >
            {isPrivateAndAdded && <span>🔒 </span>}
            {name}
          </h3>
          {/* تم إزالة أيقونة المعلومات */}
        </div>
        <div className="d-flex align-items-center">
          {/* زر إخفاء المجموعة للمالك فقط */}
          {isOwner && (
            <Button
              appearance="subtle"
              size="xs"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsHideConfirmOpen(true);
              }}
              style={{ marginRight: '5px', padding: '2px 5px' }}
            >
              <TranslatedText text="hide" />
            </Button>
          )}
          <TimeAgo
            datetime={
              lastMessage ? new Date(lastMessage.createdAt) : new Date(createdAt)
            }
            className="room-time mr-2"
          />
        </div>
      </div>

      {/* نافذة تأكيد حذف المجموعة */}
      <Modal open={isDeleteConfirmOpen} onClose={() => setIsDeleteConfirmOpen(false)} size="xs">
        <Modal.Header>
          <Modal.Title><TranslatedText text="confirmGroupDeletion" /></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p><TranslatedText text="wasTheSaleSuccessful" /></p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => handleDeleteRoom(true)} color="green" appearance="primary">
            <TranslatedText text="yes" />
          </Button>
          <Button onClick={() => handleDeleteRoom(false)} color="red" appearance="primary">
            <TranslatedText text="no" />
          </Button>
          <Button onClick={() => setIsDeleteConfirmOpen(false)} appearance="subtle">
            <TranslatedText text="cancel" />
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة تأكيد إخفاء المجموعة */}
      <Modal open={isHideConfirmOpen} onClose={() => setIsHideConfirmOpen(false)} size="xs">
        <Modal.Header>
          <Modal.Title><TranslatedText text="confirmHidingGroup" /></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p><TranslatedText text="areYouSureHideGroup" /> "{name}" <TranslatedText text="fromHomepage" />?</p>
          <p style={{ fontSize: '12px', color: '#888' }}>
            <TranslatedText text="noteShowGroupAgainFromRecords" />
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleHideRoomForOwner} color="blue" appearance="primary">
            <TranslatedText text="confirm" />
          </Button>
          <Button onClick={() => setIsHideConfirmOpen(false)} appearance="subtle">
            <TranslatedText text="cancel" />
          </Button>
        </Modal.Footer>
      </Modal>
      <div className="d-flex align-items-center room-last-message">
        {lastMessage ? (
          <>
            <div className="d-flex align-items-center">
              <ProfileAvatar
                src={lastMessage.author.avatar}
                name={lastMessage.author.name}
                size="xs"
              />
            </div>
            <div className="text-disappear ml-2">
              <span className="last-message-text">
                {lastMessage.text || (lastMessage.file && <TranslatedText text="attachedFile" />)}
              </span>
            </div>
          </>
        ) : (
          <span className="no-messages"><TranslatedText text="noMessagesYet" /></span>
        )}
      </div>
    </div>
  );
};

export default RoomItem;
