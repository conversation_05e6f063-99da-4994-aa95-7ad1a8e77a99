import React, { memo, useCallback, useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>ge, Message, toaster } from "rsuite";
import TimeAgo from "timeago-react";
import { useCurrentRoom } from "../../../context/current-room.context";
import { useReply } from "../../../context/reply.context";
import { useHover, useMediaQuery } from "../../../misc/custom-hooks";
import { auth, database } from "../../../misc/firebase.config";
import PresenceDot from "../../PresenceDot";
import ProfileAvatar from "../../ProfileAvatar";
import IconBtnControl from "./IconBtnControl";
import ImgBtnModal from "./ImgBtnModal";
import ProfileInfoBtnModal from "./ProfileInfoBtnModal";
import { ref, set, runTransaction, get } from "firebase/database";
import { useProfile } from "../../../context/profile.context";
import { FaEye } from "react-icons/fa";

const renderFileMessage = (file) => {
  if (file.contentType.includes("image")) {
    return (
      <div className="message-image-wrapper">
        <ImgBtnModal src={file.url} fileName={file.name} />
      </div>
    );
  }

  if (file.contentType.includes("audio")) {
    return (
      // eslint-disable-next-line jsx-a11y/media-has-caption
      <audio controls>
        <source src={file.url} type="audio/mp3" />
        Your browser does not support the audio element.
      </audio>
    );
  }

  return <a href={file.url}>Download {file.name}</a>;
};

const MessageItem = ({ message, handleAdmin, handleLike, handleDelete, isSameAuthor }) => {
  const { author, createdAt, text, file, likes, likeCount, isSystemMessage, id: messageId, views = {} } = message;
  const { setReply } = useReply();
  const { profile } = useProfile();
  const [isBanning, setIsBanning] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [viewCount, setViewCount] = useState(Object.keys(views || {}).length);

  const [selfRef, isHovered] = useHover();
  const isMobile = useMediaQuery("(max-width: 992px)");

  const isAdmin = useCurrentRoom((v) => v.isAdmin);
  const admins = useCurrentRoom((v) => v.admins);
  const isOwnerOnly = useCurrentRoom((v) => v.isOwnerOnly);
  const isPrivate = useCurrentRoom((v) => v.isPrivate);
  const isCurrentUserSuperAdmin = profile && profile.email === '<EMAIL>';
  const isCurrentUserOwner = auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1';

  const isMsgAuthorAdmin = admins && admins[author.uid] === true;
  const isAuthor = auth.currentUser.uid === author.uid;
  // فقط المالك يمكنه منح صلاحيات المشرف
  const canGrantAdmin = isCurrentUserOwner && !isAuthor;

  const canShowIcons = isMobile || isHovered;
  const isLiked = likes && Object.keys(likes).includes(auth.currentUser.uid);

  const handleReply = useCallback(() => {
    setIsLoading(true);
    try {
      setReply({
        author,
        text,
        file,
        messageId: message.id
      });

      // التركيز على مربع الكتابة
      const inputElement = document.querySelector('.rs-input');
      if (inputElement) {
        inputElement.focus();
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Error setting reply:', error);
      setIsLoading(false);
    }
  }, [author, text, file, message.id, setReply]);

  // تسجيل مشاهدة الرسالة
  useEffect(() => {
    // فقط في المجموعات التي ينشئها المالك فقط
    if (isOwnerOnly && messageId && !isAuthor) {
      const recordView = async () => {
        try {
          const messageRef = ref(database, `/messages/${messageId}`);

          // التحقق من وجود الرسالة
          const snapshot = await get(messageRef);
          if (snapshot.exists()) {
            // تحديث عدد المشاهدات
            await runTransaction(messageRef, (msg) => {
              if (msg) {
                if (!msg.views) {
                  msg.views = {};
                }

                // إضافة المستخدم الحالي إلى قائمة المشاهدين إذا لم يكن موجودًا بالفعل
                if (!msg.views[auth.currentUser.uid]) {
                  msg.views[auth.currentUser.uid] = {
                    timestamp: Date.now(),
                    name: profile.name || auth.currentUser.displayName
                  };

                  // تحديث عدد المشاهدات في الواجهة
                  setViewCount(Object.keys(msg.views || {}).length + 1);
                }
              }
              return msg;
            });
          }
        } catch (error) {
          console.error('Error recording view:', error);
        }
      };

      recordView();
    }
  }, [isOwnerOnly, messageId, isAuthor, profile]);

  const isCurrentUser = auth.currentUser.uid === author.uid;

  // وظيفة حظر المستخدم
  const handleBanUser = async () => {
    // التحقق من أن المستخدم الحالي هو المالك أو مشرف
    if (!isAdmin && auth.currentUser.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1') {
      toaster.push(
        <Message type="error" closable duration={4000}>
          فقط مالك الموقع والمشرفين يمكنهم حظر المستخدمين
        </Message>
      );
      return;
    }

    if (author.uid === auth.currentUser.uid) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          لا يمكنك حظر نفسك
        </Message>
      );
      return;
    }

    try {
      setIsBanning(true);

      // إضافة المستخدم إلى قائمة المحظورين
      await set(ref(database, `/banned-users/${author.uid}`), {
        name: author.name,
        email: author.email || '',
        avatar: author.avatar || '',
        bannedAt: new Date().toISOString(),
        bannedBy: {
          uid: auth.currentUser.uid,
          name: profile.name,
          email: profile.email || '',
        }
      });

      // إضافة علامة لتسجيل خروج المستخدم المحظور فوراً
      await set(ref(database, `/force-logout/${author.uid}`), {
        timestamp: new Date().toISOString(),
        reason: 'banned',
        bannedBy: auth.currentUser.uid,
        bannedByName: profile.name
      });

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حظر المستخدم {author.name} بنجاح
        </Message>
      );

      setIsBanning(false);
    } catch (error) {
      console.error('Error banning user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في حظر المستخدم: {error.message}
        </Message>
      );
      setIsBanning(false);
    }
  };

  // عرض الرد على الرسالة إذا كان موجودًا
  const renderReplyPart = () => {
    if (!message.replyTo) return null;

    return (
      <div className="replied-message">
        <div className="replied-message-author">
          <i className="fa-solid fa-reply"></i>
          <span>{message.replyTo.author.name}</span>
        </div>
        <div className="replied-message-text">
          {message.replyTo.text || (message.replyTo.file ? 'صورة' : 'ملف مرفق')}
        </div>
      </div>
    );
  };

  // إذا كانت رسالة نظام، عرضها بشكل مختلف
  if (isSystemMessage) {
    return (
      <li className="msg-system">
        <div className="system-message">
          <div className="system-message-header">
            <span className="system-message-author">{author.name}</span>
            <span className="system-message-time">
              <TimeAgo datetime={createdAt} className="font-normal" />
            </span>
          </div>
          <div className="system-message-content">
            {text && <pre className="system-message-text">{text}</pre>}
          </div>
        </div>
      </li>
    );
  }

  return (
    <li
      className={`${isCurrentUser ? 'msg-self' : 'msg-other'} ${isSameAuthor ? 'same-author' : ''} ${isHovered ? "bg-hover" : ""}`}
      ref={selfRef}
    >
      {/* صورة المستخدم للرسائل المرسلة من المستخدم الحالي (تم تبديلها مع أزرار التفاعل) */}
      {isCurrentUser && canShowIcons && (
        <div className="msg-actions msg-actions-self">
          <ProfileAvatar
            src={author.avatar}
            name={author.name}
            uid={author.uid}
            showUid={false}
            size="xs"
            circle
            className="self-avatar" // إضافة كلاس خاص لصورة المستخدم الحالي
          />
        </div>
      )}

      {/* أزرار حذف الرسالة وزر الإعجاب للمستخدم الحالي */}
      {isCurrentUser && (
        <div className="msg-avatar-container msg-avatar-self">
          {/* عرض زر الإعجاب لصاحب الرسالة (غير قابل للنقر) */}
          {canShowIcons && (
            <IconBtnControl
              {...(isLiked ? { color: "red" } : {})}
              isVisible={true}
              iconName="heart"
              tooltip="عدد الإعجابات"
              onClick={() => {}} // لا يمكن الضغط عليه
              badgeContent={likeCount}
              disabled={true} // تعطيل الزر
              className="like-button-self" // إضافة كلاس خاص
            />
          )}

          {/* زر حذف الرسالة */}
          {(isAuthor || isAdmin) && (
            <IconBtnControl
              isVisible={true}
              iconName="close"
              tooltip="حذف الرسالة"
              onClick={() => handleDelete(message.id, file)}
            />
          )}
        </div>
      )}

      <div className="msg-bubble">
        {/* معلومات المؤلف لجميع الرسائل */}
        <div className="msg-author">
          <div className="author-info">
            <span className={
              author.email === '<EMAIL>' || author.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1'
                ? 'owner-name'
                : isMsgAuthorAdmin
                  ? 'admin-name'
                  : ''
            }>
              {author.name}
            </span>
            {/* إظهار شارة المشرف لجميع المشرفين وليس للمالك */}
            {isMsgAuthorAdmin && author.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && author.email !== '<EMAIL>' && (
              <Badge content="مشرف" className="admin-badge" />
            )}
            <div className="author-uid">{author.uid}</div>
          </div>
        </div>

        {/* عرض الرد على الرسالة إذا كان موجودًا */}
        {message.replyTo && (
          <div className="replied-message">
            <div className="replied-message-text">
              <i className="fa-solid fa-reply reply-icon"></i>
              {message.replyTo.text || (message.replyTo.file ? 'صورة' : 'ملف مرفق')}
            </div>
          </div>
        )}

        <div className="msg-text">
          {text && <span className="word-break-all">{text}</span>}
          {file && renderFileMessage(file)}
        </div>

        <div className="msg-time">
          <TimeAgo
            datetime={createdAt}
            className="font-normal"
          />

          {/* عرض عداد المشاهدات فقط في المجموعات التي ينشئها المالك فقط وللرسائل التي كتبها المالك */}
          {isOwnerOnly && (author.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || author.email === '<EMAIL>') && (
            <div className="msg-views">
              <FaEye style={{ marginLeft: '5px', fontSize: '12px' }} /> {viewCount}
            </div>
          )}
        </div>
      </div>

      {/* صورة المستخدم للرسائل المستلمة من الآخرين (على اليسار) */}
      {!isCurrentUser && (
        <div className="msg-avatar-container msg-avatar-other">
          <ProfileInfoBtnModal
            profile={author}
            appearance="link"
            className="p-0 avatar-profile-btn"
            roomId={message.roomId}
          >
            {canGrantAdmin && (
              <Button
                block
                onClick={() => handleAdmin(author.uid)}
                color="blue"
                appearance="primary"
              >
                {isMsgAuthorAdmin
                  ? "إلغاء صلاحيات المشرف"
                  : "منح صلاحيات المشرف"}
              </Button>
            )}
          </ProfileInfoBtnModal>
        </div>
      )}

      {/* أزرار التفاعل للمستخدم الحالي (صاحب الرسالة) - تم نقلها إلى جانب زر الحذف */}

      {/* أزرار التفاعل لرسائل الآخرين (في المنتصف) */}
      {!isCurrentUser && canShowIcons && (
        <div className="msg-actions msg-actions-other">
          {/* إظهار زر الرد فقط إذا كانت المجموعة غير مقيدة أو إذا كان المستخدم هو المالك */}
          {(!isOwnerOnly || isCurrentUserOwner) && (
            <IconBtnControl
              isVisible={true}
              iconName="reply"
              tooltip="الرد على الرسالة"
              onClick={handleReply}
              disabled={isLoading}
            />
          )}

          <IconBtnControl
            {...(isLiked ? { color: "red" } : {})}
            isVisible={true}
            iconName="heart"
            tooltip="الإعجاب بالرسالة"
            onClick={() => handleLike(message.id)}
            badgeContent={likeCount}
            className="like-button" // إضافة كلاس خاص
          />

          {/* شروط عرض زر الحذف:
              1. إذا كان المستخدم هو صاحب الرسالة وكانت المجموعة عامة
              2. إذا كان المستخدم هو المالك (يمكنه حذف أي رسالة)
              3. إذا كان المستخدم مشرف والرسالة ليست لمشرف آخر
           */}
          {(
            // المستخدم هو صاحب الرسالة وليست مجموعة خاصة
            (isAuthor && !isPrivate) ||
            // المستخدم هو المالك
            isCurrentUserOwner ||
            // المستخدم مشرف والرسالة ليست لمشرف آخر
            (isAdmin && !isMsgAuthorAdmin)
          ) && (
            <IconBtnControl
              isVisible={true}
              iconName="close"
              tooltip="حذف الرسالة"
              onClick={() => handleDelete(message.id, file)}
            />
          )}
        </div>
      )}
    </li>
  );
};

export default memo(MessageItem);
