import firebase_admin
from firebase_admin import credentials
from firebase_admin import db
import json

# تهيئة Firebase Admin SDK
cred = credentials.Certificate('serviceAccountKey.json')
firebase_admin.initialize_app(cred, {
    'databaseURL': 'https://toika-369-default-rtdb.firebaseio.com'
})

# معرف المستخدم المراد تعيينه كمشرف
uid = 'HdgvqlLyeagYNofjTtYGefH2wjD3'

def make_admin():
    """تعيين المستخدم كمشرف"""
    try:
        print(f"بدء العملية: تعيين المستخدم {uid} كمشرف...")
        
        # التحقق من وجود المستخدم
        print(f"التحقق من وجود المستخدم {uid}...")
        user_ref = db.reference(f'users/{uid}')
        user_data = user_ref.get()
        
        if user_data:
            user_name = user_data.get('name', uid)
            print(f"تم العثور على المستخدم: {user_name}")
            
            # التحقق من حالة المشرف الحالية
            is_admin = user_data.get('isAdmin', False)
            print(f"حالة المشرف الحالية: {'مشرف' if is_admin else 'مستخدم عادي'}")
            
            if is_admin:
                print("المستخدم مشرف بالفعل")
            else:
                # تعيين المستخدم كمشرف
                print("تعيين المستخدم كمشرف...")
                user_ref.update({'isAdmin': True})
                
                # التحقق من نجاح العملية
                updated_user_data = user_ref.get()
                updated_is_admin = updated_user_data.get('isAdmin', False)
                
                if updated_is_admin:
                    print("تم تعيين المستخدم كمشرف بنجاح")
                else:
                    print("فشل في تعيين المستخدم كمشرف")
        else:
            print(f"لم يتم العثور على المستخدم {uid}")
    except Exception as e:
        print(f"خطأ: {str(e)}")

if __name__ == "__main__":
    make_admin()
