// أنماط الإشعارات داخل التطبيق

.in-app-notification-container {
  position: fixed;
  top: 10px;
  left: 10px;
  right: 10px;
  z-index: 1050;
  width: auto;
  max-width: 100%;
  direction: rtl;
}

.in-app-notification {
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(33, 33, 33, 0.95) !important;
  transition: opacity 1s ease, transform 1s ease;

  &.show {
    opacity: 1;
    transform: translateY(0);
  }

  &.hide {
    opacity: 0;
    transform: translateY(-10px);
  }

  &.rs-message {
    padding: 10px;
    margin: 0;

    .rs-message-container {
      display: flex;
      align-items: flex-start;
    }

    .rs-message-icon-wrapper {
      margin-left: 8px;
      margin-right: 0;
      margin-top: 2px;
    }

    .rs-message-content {
      flex: 1;
    }

    .rs-message-close {
      position: absolute;
      top: 8px;
      left: 8px;
      color: rgba(255, 255, 255, 0.7);

      &:hover {
        color: rgba(255, 255, 255, 1);
      }
    }
  }
}

.notification-content {
  display: flex;
  align-items: flex-start;

  .notification-icon {
    margin-left: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
  }

  .notification-text {
    flex: 1;
    overflow: hidden;

    .notification-title {
      font-weight: bold;
      font-size: 13px;
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #ffffff;
    }

    .notification-body {
      font-size: 11px;
      color: #ffffff;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.3;
    }
  }
}

// أنماط لأنواع الإشعارات المختلفة
.rs-message-info .notification-icon {
  color: #2196f3;
}

.rs-message-success .notification-icon {
  color: #4caf50;
}

.rs-message-warning .notification-icon {
  color: #ff9800;
}

.rs-message-error .notification-icon {
  color: #f44336;
}

// أنماط أزرار اختبار الإشعارات
.notification-test-buttons {
  margin-top: 15px;

  .rs-btn-toolbar {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .notification-test-btn {
    width: 100%;
    padding: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      margin-left: 8px;
      font-size: 16px;
    }
  }
}

// أنماط صفحة الإشعارات
.notifications-page {
  padding: 10px;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;

    i {
      font-size: 30px;
      margin-bottom: 10px;
      color: #ffffff;
    }

    p {
      color: #ffffff;
    }
  }

  .empty-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;

    i {
      font-size: 40px;
      margin-bottom: 15px;
      color: #ffffff;
    }

    p {
      color: #ffffff;
    }
  }

  .notifications-list {
    .notification-item {
      cursor: pointer;
      padding: 12px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      &.unread {
        background-color: rgba(33, 150, 243, 0.1);
      }

      .notification-item-content {
        display: flex;
        align-items: flex-start;
        position: relative;

        .notification-icon-container {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 10px;
          flex-shrink: 0;

          &.reply {
            color: #2196f3;
          }

          &.privateMessage {
            color: #4caf50;
          }

          &.ownerMessage {
            color: #ff9800;
          }

          &.mention {
            color: #e91e63;
          }

          .notification-list-icon {
            font-size: 16px;
          }
        }

        .notification-details {
          flex: 1;
          overflow: hidden;

          .notification-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 4px;
            color: #ffffff;
          }

          .notification-body {
            font-size: 13px;
            color: #ffffff;
            margin-bottom: 6px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .notification-time {
            font-size: 11px;
            color: #ffffff;
          }
        }

        .unread-badge {
          position: absolute;
          top: 0;
          left: 0;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #2196f3;
        }
      }
    }
  }
}

// أنماط أيقونة الإشعارات في الصفحة الرئيسية
.notifications-icon-container {
  position: relative;

  .notifications-count {
    position: absolute;
    top: -5px;
    left: -5px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background-color: #f44336;
    color: white;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
  }

  .notification-dot {
    position: absolute;
    top: -3px;
    right: -3px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #f44336;
    animation: blink 1.5s infinite ease-in-out;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}
