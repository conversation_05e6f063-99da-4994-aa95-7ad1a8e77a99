import { database } from './firebase.config';
import { ref, get, set, onValue, off } from 'firebase/database';

// دالة للتحقق من جميع الغرف وتطبيق الجدولة التلقائية
export const checkAndApplyChatSchedule = async () => {
  try {
    // الحصول على جميع الغرف
    const roomsRef = ref(database, 'rooms');
    const snapshot = await get(roomsRef);

    if (!snapshot.exists()) {
      return;
    }

    const rooms = snapshot.val();
    const currentHour = new Date().getHours();

    // التحقق من كل غرفة
    for (const roomId in rooms) {
      const room = rooms[roomId];

      // تجاهل الدردشات الخاصة
      if (room.isDirectMessage) {
        continue;
      }

      // التحقق مما إذا كانت الجدولة التلقائية مفعلة
      if (room.isAutoChatScheduleEnabled) {
        const openTime = room.chatOpenTime || 9; // الافتراضي: 9 صباحًا
        const closeTime = room.chatCloseTime || 24; // الافتراضي: 12 منتصف الليل

        // تحديد ما إذا كان يجب فتح أو إغلاق الدردشة
        let shouldBeClosed = false;

        // إذا كان وقت الفتح أقل من وقت الإغلاق (مثال: 9 صباحًا إلى 5 مساءً)
        if (openTime < closeTime) {
          shouldBeClosed = currentHour < openTime || currentHour >= closeTime;
        }
        // إذا كان وقت الفتح أكبر من وقت الإغلاق (مثال: 22 مساءً إلى 6 صباحًا)
        else {
          shouldBeClosed = currentHour < openTime && currentHour >= closeTime;
        }

        // تحديث حالة الدردشة إذا كانت مختلفة عن الحالة الحالية
        if (room.isChatClosed !== shouldBeClosed) {
          await set(ref(database, `rooms/${roomId}/isChatClosed`), shouldBeClosed);
          console.log(`تم ${shouldBeClosed ? 'إغلاق' : 'فتح'} الدردشة في الغرفة ${roomId} تلقائيًا`);
        }
      }
    }
  } catch (error) {
    console.error('خطأ في تطبيق الجدولة التلقائية للدردشة:', error);
  }
};

// دالة لبدء المؤقت للتحقق من الجدولة كل ساعة
let schedulerInterval = null;

export const startChatScheduler = () => {
  if (schedulerInterval) {
    clearInterval(schedulerInterval);
  }

  // تنفيذ الجدولة فورًا عند بدء التطبيق
  checkAndApplyChatSchedule();

  // ثم تنفيذها كل ساعة
  schedulerInterval = setInterval(checkAndApplyChatSchedule, 60 * 60 * 1000);
  console.log('تم بدء جدولة الدردشة التلقائية');
};

export const stopChatScheduler = () => {
  if (schedulerInterval) {
    clearInterval(schedulerInterval);
    schedulerInterval = null;
    console.log('تم إيقاف جدولة الدردشة التلقائية');
  }
};
