import React, { useEffect, useState } from 'react';
import { ref, get, remove } from 'firebase/database';
import { database, auth } from '../misc/firebase.config';
import { Container, Content, FlexboxGrid, Panel, List, Button, Message, toaster, Loader, InputGroup, Input } from 'rsuite';
import TimeAgo from 'timeago-react';
import ProfileAvatar from '../components/ProfileAvatar';
import SearchIcon from '@rsuite/icons/Search';
import { useProfile } from '../context/profile.context';

const BannedUsersPage = () => {
  const [bannedUsers, setBannedUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // التحقق مما إذا كان المستخدم مشرف
  const { profile } = useProfile();
  const isAdmin = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2' ||
    auth.currentUser.email === '<EMAIL>' ||
    // التحقق من وجود المستخدم في قائمة المشرفين
    localStorage.getItem('isAdmin') === 'true' ||
    (profile && profile.isAdmin === true)
  );

  // جلب قائمة المستخدمين المحظورين
  useEffect(() => {
    const fetchBannedUsers = async () => {
      try {
        // التحقق من وجود مستخدم مسجل الدخول
        if (!auth.currentUser) {
          setLoading(false);
          return;
        }

        // التحقق مما إذا كان المستخدم مشرف
        if (!isAdmin) {
          setLoading(false);
          return;
        }

        // جلب قائمة المستخدمين المحظورين
        const bannedUsersRef = ref(database, '/banned-users');
        const snapshot = await get(bannedUsersRef);

        if (snapshot.exists()) {
          const usersData = snapshot.val();

          // تحويل البيانات إلى مصفوفة
          const usersArray = Object.keys(usersData).map(userId => ({
            id: userId,
            ...usersData[userId]
          }));

          // ترتيب المستخدمين حسب تاريخ الحظر (الأحدث أولاً)
          usersArray.sort((a, b) => new Date(b.bannedAt) - new Date(a.bannedAt));

          setBannedUsers(usersArray);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching banned users:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في جلب قائمة المستخدمين المحظورين: {error.message}
          </Message>
        );
        setLoading(false);
      }
    };

    fetchBannedUsers();
  }, [isAdmin]);

  // إلغاء حظر مستخدم
  const handleUnbanUser = async (userId) => {
    try {
      console.log('Attempting to unban user:', userId);

      // التحقق من وجود المستخدم في قائمة المحظورين
      const bannedUserRef = ref(database, `/banned-users/${userId}`);
      const bannedUserSnapshot = await get(bannedUserRef);

      if (bannedUserSnapshot.exists()) {
        // حذف المستخدم من قائمة المحظورين
        await remove(bannedUserRef);

        // تحديث قائمة المستخدمين المحظورين
        setBannedUsers(prevUsers => prevUsers.filter(user => user.id !== userId));

        // عرض رسالة نجاح
        toaster.push(
          <Message type="success" closable duration={4000}>
            تم إلغاء حظر المستخدم بنجاح
          </Message>
        );
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            المستخدم غير موجود في قائمة المحظورين
          </Message>
        );
      }
    } catch (error) {
      console.error('Error unbanning user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إلغاء حظر المستخدم: {error.message}
        </Message>
      );
    }
  };

  // تصفية المستخدمين المحظورين حسب البحث
  const filteredUsers = bannedUsers.filter(user =>
    user.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (user.name && user.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (user.email && user.email.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <Container>
      <Content className="content-with-bottom-navbar">
        <FlexboxGrid justify="center">
          <FlexboxGrid.Item colspan={22} md={18} lg={14}>
            <Panel header={<h3>قائمة المستخدمين المحظورين</h3>} bordered className="mt-page">
              {isAdmin ? (
                <>
                  <InputGroup inside style={{ marginBottom: '15px' }}>
                    <Input
                      placeholder="البحث عن مستخدم محظور بالاسم أو UID..."
                      value={searchQuery}
                      onChange={setSearchQuery}
                    />
                    <InputGroup.Button>
                      <SearchIcon />
                    </InputGroup.Button>
                  </InputGroup>

                  {loading ? (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <Loader size="md" content="جاري تحميل قائمة المستخدمين المحظورين..." />
                    </div>
                  ) : filteredUsers.length === 0 ? (
                    <Message type="info" showIcon>
                      {searchQuery ? 'لا توجد نتائج مطابقة للبحث' : 'لا يوجد مستخدمين محظورين حاليًا'}
                    </Message>
                  ) : (
                    <List hover>
                      {filteredUsers.map(user => (
                        <List.Item key={user.id}>
                          <FlexboxGrid align="middle">
                            <FlexboxGrid.Item colspan={2}>
                              <ProfileAvatar
                                src={user.avatar}
                                name={user.name || 'مستخدم'}
                                uid={user.id}
                                showUid={true}
                                size="sm"
                              />
                            </FlexboxGrid.Item>
                            <FlexboxGrid.Item colspan={10}>
                              <div>
                                <strong>{user.name || 'مستخدم غير معروف'}</strong>
                                <div style={{ fontSize: '12px', color: '#999', direction: 'ltr' }}>
                                  {user.id}
                                </div>
                                {user.email && (
                                  <div style={{ fontSize: '12px', color: '#999' }}>
                                    {user.email}
                                  </div>
                                )}
                              </div>
                            </FlexboxGrid.Item>
                            <FlexboxGrid.Item colspan={8}>
                              <div>
                                <div>تم الحظر بواسطة: {user.bannedBy?.name || 'مشرف'}</div>
                                <div style={{ fontSize: '12px', color: '#999' }}>
                                  <TimeAgo datetime={new Date(user.bannedAt)} />
                                </div>
                              </div>
                            </FlexboxGrid.Item>
                            <FlexboxGrid.Item colspan={4} style={{ textAlign: 'right' }}>
                              <Button appearance="ghost" color="red" size="sm" onClick={() => handleUnbanUser(user.id)}>
                                إلغاء الحظر
                              </Button>
                            </FlexboxGrid.Item>
                          </FlexboxGrid>
                        </List.Item>
                      ))}
                    </List>
                  )}
                </>
              ) : (
                <Message type="error" showIcon>
                  ليس لديك صلاحية الوصول إلى هذه الصفحة. هذه الصفحة متاحة للمشرفين فقط.
                </Message>
              )}
            </Panel>
          </FlexboxGrid.Item>
        </FlexboxGrid>
      </Content>
    </Container>
  );
};

export default BannedUsersPage;
