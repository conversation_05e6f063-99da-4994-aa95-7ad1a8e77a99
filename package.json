{"name": "chat-app", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.0", "@fortawesome/free-regular-svg-icons": "^6.2.0", "@fortawesome/free-solid-svg-icons": "^6.2.0", "@fortawesome/react-fontawesome": "^0.2.0", "@rsuite/icons": "^1.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "firebase": "^9.23.0", "react": "^18.2.0", "react-avatar-editor": "^11.0.7", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-mic": "^12.4.6", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "5.0.1", "rsuite": "^5.22.1", "sass": "^1.56.1", "timeago-react": "^3.0.5", "use-context-selector": "^1.4.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}