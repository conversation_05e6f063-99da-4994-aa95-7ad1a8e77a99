import React from "react";
import { Badge, Whisper, Tooltip, IconButton } from "rsuite";

// تحويل الأرقام العربية إلى إنجليزية
const convertToEnglishNumbers = (num) => {
  if (num === undefined || num === null) return null;

  // التأكد من أن الرقم نص
  const numStr = num.toString();

  // تحويل الأرقام العربية إلى إنجليزية
  return numStr;
};

const ConditionalBadge = ({ condition, children, className, offset }) => {
  // تحويل الرقم إلى إنجليزي
  const englishNumber = convertToEnglishNumbers(condition);

  return condition ? (
    <Badge content={englishNumber} className={className} offset={offset}>
      {children}
    </Badge>
  ) : children;
};

const IconBtnControl = ({
  isVisible,
  iconName,
  tooltip,
  onClick,
  badgeContent,
  className,
  ...props
}) => {
  // تحديد ما إذا كان هذا زر إعجاب
  const isLikeButton = iconName === "heart";

  // تحديد كلاس خاص لشارة الإعجاب
  const badgeClassName = isLikeButton ? "like-badge" : "";

  // تحديد موضع الشارة للإعجاب
  const badgeOffset = isLikeButton ? [10, 5] : undefined;

  return (
    <div
      className={`ml-2 ${className || ""}`}
      style={{ visibility: isVisible ? "visible" : "hidden" }}
    >
      <ConditionalBadge condition={badgeContent} className={badgeClassName} offset={badgeOffset}>
        <Whisper
          placement="top"
          delay={0}
          delayHide={0}
          delayShow={0}
          trigger="hover"
          speaker={<Tooltip>{tooltip}</Tooltip>}
        >
          <IconButton
            {...props}
            onClick={onClick}
            circle
            size="xs"
            icon={
              iconName === "heart" ? (
                <i
                  style={{ color: props.color }}
                  className="fa-solid fa-heart"
                ></i>
              ) : iconName === "reply" ? (
                <i className="fa-solid fa-reply"></i>
              ) : (
                <i className="fa-solid fa-trash-can"></i>
              )
            }
          />
        </Whisper>
      </ConditionalBadge>
    </div>
  );
};

export default IconBtnControl;
