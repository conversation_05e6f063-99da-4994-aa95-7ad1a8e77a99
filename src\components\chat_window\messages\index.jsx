import React, { useEffect, useState, useRef, useCallback } from "react";
import { useParams } from "react-router";
import { Button, Message, toaster } from "rsuite";
import {
  ref as dbRef,
  off,
  onValue,
  query,
  orderByChild,
  equalTo,
  runTransaction,
  update,
  limitToLast,
} from "firebase/database";
import { deleteObject, ref as storageRef } from "firebase/storage";
import { auth, database, storage } from "../../../misc/firebase.config";
import { groupBy, transformToArrWithId } from "../../../misc/helpers";
import MessageItem from "./MessageItem";
import SpecialMessageItem from "./SpecialMessageItem";

const PAGE_SIZE = 15;
const messagesRef = dbRef(database, "/messages");

// تم إزالة هذه الدالة لأنها لم تعد مستخدمة
// function shouldScrollToBottom(node, threshold = 10) {
//   // إذا كان العنصر غير موجود، ارجع true
//   if (!node) return true;
//
//   const percentage =
//     (100 * node.scrollTop) / (node.scrollHeight - node.clientHeight) || 0;
//
//   // تقليل قيمة العتبة لجعل التمرير التلقائي أكثر حساسية
//   return percentage > threshold;
// }

const Messages = () => {
  const { chatId } = useParams();
  const [messages, setMessages] = useState(null);
  const [limit, setLimit] = useState(PAGE_SIZE);
  const selfRef = useRef();

  const isChatEmpty = messages && messages.length === 0;
  const canShowMessages = messages && messages.length > 0;

  const loadMessages = useCallback(
    (limitToUse) => {
      const node = selfRef.current;

      off(messagesRef);

      onValue(
        query(
          messagesRef,
          orderByChild("roomId"),
          equalTo(chatId),
          limitToLast(limitToUse || PAGE_SIZE)
        ),
        (snap) => {
          const data = transformToArrWithId(snap.val());
          setMessages(data);

          // دائماً قم بالتمرير إلى أسفل عند تحميل الرسائل لأول مرة
          // وليس عند تحميل المزيد من الرسائل القديمة
          if (!limitToUse) {
            setTimeout(() => {
              if (node) {
                node.scrollTop = node.scrollHeight;
              }
            }, 100);
          }
        }
      );

      setLimit((p) => p + PAGE_SIZE);
    },
    [chatId]
  );

  const onLoadMore = useCallback(() => {
    const node = selfRef.current;
    const oldHeight = node.scrollHeight;

    loadMessages(limit);

    setTimeout(() => {
      const newHeight = node.scrollHeight;
      node.scrollTop = newHeight - oldHeight;
    }, 200);
  }, [loadMessages, limit]);

  useEffect(() => {
    const node = selfRef.current;

    loadMessages();

    // تمرير الشاشة إلى أسفل بعد تحميل الرسائل
    const scrollToBottom = () => {
      if (node) {
        node.scrollTop = node.scrollHeight;
      }
    };

    // محاولة التمرير عدة مرات للتأكد من تحميل جميع الرسائل
    const scrollInterval = setInterval(scrollToBottom, 100);

    // إيقاف المحاولات بعد ثانية واحدة
    setTimeout(() => {
      clearInterval(scrollInterval);
      scrollToBottom(); // تمرير نهائي
    }, 1000);

    return () => {
      off(messagesRef);
      clearInterval(scrollInterval);
    };
  }, [loadMessages]);

  const handleAdmin = useCallback(
    async (uid) => {
      // التحقق من أن المستخدم الحالي هو المالك
      if (auth.currentUser.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1') {
        toaster.push(
          <Message type="error" closable duration={4000}>
            فقط مالك الموقع يمكنه منح أو إلغاء صلاحيات المشرف
          </Message>
        );
        return;
      }

      let alertMsg;

      await runTransaction(
        dbRef(database, `/rooms/${chatId}/admins`),
        (admins) => {
          if (admins) {
            if (admins[uid]) {
              admins[uid] = null;
              alertMsg = "تم إلغاء صلاحيات المشرف";
            } else {
              admins[uid] = true;
              alertMsg = "تم منح صلاحيات المشرف";
            }
          }
          return admins;
        }
      );

      toaster.push(
        <Message type="info" closable duration={4000}>
          {alertMsg}
        </Message>
      );
    },
    [chatId]
  );

  const handleLike = useCallback(async (msgId) => {
    // حفظ موضع التمرير الحالي
    const node = selfRef.current;
    const scrollPosition = node ? node.scrollTop : 0;
    const scrollHeight = node ? node.scrollHeight : 0;

    const { uid } = auth.currentUser;
    const messageRef = dbRef(database, `/messages/${msgId}`);

    await runTransaction(messageRef, (msg) => {
      if (msg) {
        if (msg.likes && msg.likes[uid]) {
          msg.likeCount -= 1;
          msg.likes[uid] = null;
        } else {
          msg.likeCount += 1;

          if (!msg.likes) {
            msg.likes = {};
          }

          msg.likes[uid] = true;
        }
      }

      return msg;
    });

    // استعادة موضع التمرير بعد تحديث الإعجاب
    // استخدام requestAnimationFrame للتأكد من تحديث DOM قبل إعادة ضبط موضع التمرير
    requestAnimationFrame(() => {
      if (node) {
        // حساب الفرق في ارتفاع المحتوى بعد التحديث
        const heightDiff = node.scrollHeight - scrollHeight;
        // الحفاظ على نفس الموضع النسبي بعد التحديث
        node.scrollTop = scrollPosition + heightDiff;
      }
    });
  }, []);

  const handleDelete = useCallback(
    async (msgId, file) => {
      // eslint-disable-next-line no-alert
      if (!window.confirm("هل أنت متأكد من حذف هذه الرسالة؟")) {
        return;
      }

      const isLast = messages[messages.length - 1].id === msgId;

      const updates = {};

      updates[`/messages/${msgId}`] = null;

      if (isLast && messages.length > 1) {
        updates[`/rooms/${chatId}/lastMessage`] = {
          ...messages[messages.length - 2],
          msgId: messages[messages.length - 2].id,
        };
      }

      if (isLast && messages.length === 1) {
        updates[`/rooms/${chatId}/lastMessage`] = null;
      }

      try {
        await update(dbRef(database), updates);

        toaster.push(
          <Message type="info" closable duration={4000}>
            تم حذف الرسالة بنجاح
          </Message>
        );
      } catch (err) {
        return toaster.push(
          <Message type="error" closable duration={4000}>
            {err.message}
          </Message>
        );
      }

      if (file) {
        try {
          const fileRef = storageRef(storage, file.url);
          await deleteObject(fileRef);
        } catch (err) {
          toaster.push(
            <Message type="error" closable duration={4000}>
              {err.message}
            </Message>
          );
        }
      }
    },
    [chatId, messages]
  );

  const renderMessages = () => {
    const groups = groupBy(messages, (item) =>
      new Date(item.createdAt).toDateString()
    );

    const items = [];

    // ترتيب التواريخ تصاعدياً (من الأقدم إلى الأحدث)
    const sortedDates = Object.keys(groups).sort((a, b) =>
      new Date(a) - new Date(b)
    );

    sortedDates.forEach((date) => {
      items.push(
        <li key={date} className="text-center mb-1 padded">
          {date}
        </li>
      );

      // ترتيب الرسائل داخل كل مجموعة تاريخ تصاعدياً
      const sortedMessages = groups[date].sort((a, b) =>
        new Date(a.createdAt) - new Date(b.createdAt)
      );

      // إضافة خاصية "same-author" عندما تكون الرسائل من نفس المؤلف
      const msgs = sortedMessages.map((msg, idx, arr) => {
        // التحقق مما إذا كانت الرسالة السابقة من نفس المؤلف
        const prevMsg = idx > 0 ? arr[idx - 1] : null;
        const isSameAuthor = prevMsg && prevMsg.author.uid === msg.author.uid;

        // التحقق مما إذا كانت رسالة خاصة
        if (msg.isSpecialMessage) {
          return (
            <SpecialMessageItem
              key={msg.id}
              message={msg}
            />
          );
        }

        return (
          <MessageItem
            key={msg.id}
            message={msg}
            handleAdmin={handleAdmin}
            handleLike={handleLike}
            handleDelete={handleDelete}
            isSameAuthor={isSameAuthor}
          />
        );
      });

      items.push(...msgs);
    });

    return items;
  };

  return (
    <ul ref={selfRef} className="msg-list custom-scroll">
      {messages && messages.length >= PAGE_SIZE && (
        <li className="text-center mt-2 mb-2">
          <Button onClick={onLoadMore} color="green" appearance="primary">
            Load more
          </Button>
        </li>
      )}
      {isChatEmpty && <li>No messages yet</li>}
      {canShowMessages && renderMessages()}
    </ul>
  );
};

export default Messages;
