import React, { useState } from 'react';
import { But<PERSON>, Message, toaster, Loader, Modal } from 'rsuite';
import { ref, get, set, remove, query, orderByChild, equalTo } from 'firebase/database';
import { database, auth } from '../../misc/firebase.config';

const RestoreRoomButton = ({ room }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // إذا لم يكن المستخدم هو المالك، لا تعرض الزر
  if (!isOwner) {
    return null;
  }

  // استعادة المجموعة المحذوفة
  const handleRestoreRoom = async () => {
    setIsLoading(true);

    try {
      // الحصول على بيانات المجموعة المحذوفة
      const deletedRoomRef = ref(database, `/deleted-rooms/${room.id}`);
      const snapshot = await get(deletedRoomRef);

      if (!snapshot.exists()) {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على بيانات المجموعة المحذوفة
          </Message>
        );
        setIsLoading(false);
        setIsConfirmOpen(false);
        return;
      }

      // التحقق من وجود رسائل للمجموعة المحذوفة
      const messagesRef = ref(database, 'messages');
      const messagesQuery = query(
        messagesRef,
        orderByChild('roomId'),
        equalTo(room.id)
      );

      const messagesSnapshot = await get(messagesQuery);
      console.log('Messages found for deleted room:', messagesSnapshot.exists());

      // الحصول على بيانات المجموعة
      const roomData = snapshot.val();

      // إعادة إنشاء المجموعة في قائمة المجموعات النشطة
      const activeRoomRef = ref(database, `/rooms/${room.id}`);

      // إزالة بعض البيانات التي لا نريد نقلها
      const { deletedAt, deletedBy, saleSuccessful, ...restoreData } = roomData;

      // تعديل بيانات المجموعة لتكون خاصة وتظهر فقط للمالك
      restoreData.isPrivate = true; // جعل المجموعة خاصة

      // إزالة جميع المشرفين والمستخدمين من المجموعة
      restoreData.admins = {
        [auth.currentUser.uid]: true // فقط المالك هو المشرف
      };

      // إزالة جميع المستخدمين من المجموعة
      restoreData.members = {
        [auth.currentUser.uid]: true // فقط المالك هو العضو
      };

      // إضافة علامة خاصة تشير إلى أن هذه مجموعة مستعادة
      restoreData.isRestored = true;

      // إضافة تاريخ الاستعادة ومعلومات إضافية
      restoreData.restoredAt = new Date().toISOString();
      restoreData.restoredBy = auth.currentUser.uid;

      // تحديث وصف المجموعة لإضافة معلومات الاستعادة
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate()}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()} ${currentDate.getHours()}:${currentDate.getMinutes()}`;

      restoreData.description = `
------ معلومات استعادة المجموعة ------
تمت استعادة هذه المجموعة بواسطة المالك
تاريخ الاستعادة: ${formattedDate}
هذه المجموعة خاصة وتظهر فقط للمالك
---------------------------

${restoreData.description || ''}`;

      // تحديث اسم المجموعة ليشير إلى أنها مستعادة
      restoreData.name = `[مستعادة] ${restoreData.name}`;

      // حفظ المجموعة في قائمة المجموعات النشطة
      await set(activeRoomRef, restoreData);

      // إضافة خاصية hasMessages للمجموعة إذا كانت تحتوي على رسائل
      if (messagesSnapshot.exists()) {
        // تحديث خاصية hasMessages في المجموعة
        await set(ref(database, `/rooms/${room.id}/hasMessages`), true);

        // تحديث آخر رسالة في المجموعة
        const messagesData = messagesSnapshot.val();
        const messagesArray = Object.values(messagesData);

        if (messagesArray.length > 0) {
          // ترتيب الرسائل حسب وقت الإنشاء (تنازلياً)
          messagesArray.sort((a, b) => {
            return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
          });

          // الحصول على آخر رسالة
          const lastMessage = messagesArray[0];

          // تحديث آخر رسالة في المجموعة
          await set(ref(database, `/rooms/${room.id}/lastMessage`), {
            ...lastMessage,
            msgId: Object.keys(messagesData).find(key => messagesData[key] === lastMessage)
          });
        }
      }

      // حذف المجموعة من قائمة المجموعات المحذوفة
      await remove(deletedRoomRef);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تمت استعادة المجموعة بنجاح
        </Message>
      );

      // إعادة تحميل الصفحة بعد الاستعادة
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error restoring room:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في استعادة المجموعة: {error.message}
        </Message>
      );
    } finally {
      setIsLoading(false);
      setIsConfirmOpen(false);
    }
  };

  return (
    <>
      <Button
        appearance="primary"
        color="green"
        onClick={() => setIsConfirmOpen(true)}
        disabled={isLoading}
      >
        استعادة المجموعة
      </Button>

      {/* نافذة تأكيد الاستعادة */}
      <Modal open={isConfirmOpen} onClose={() => setIsConfirmOpen(false)} size="xs" className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">تأكيد استعادة المجموعة</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <p>هل أنت متأكد من استعادة مجموعة "{room.name}"؟</p>
          <p style={{ color: '#ff5555', fontWeight: 'bold' }}>ملاحظة هامة:</p>
          <ul style={{ color: '#ff9999' }}>
            <li>سيتم استعادة المجموعة كمجموعة خاصة تظهر فقط لك</li>
            <li>سيتم طرد جميع المشرفين والمستخدمين من المجموعة</li>
            <li>سيتم تغيير اسم المجموعة ليشير إلى أنها مستعادة</li>
          </ul>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button
            onClick={handleRestoreRoom}
            color="green"
            appearance="primary"
            disabled={isLoading}
          >
            {isLoading ? <Loader content="جاري الاستعادة..." /> : 'استعادة'}
          </Button>
          <Button onClick={() => setIsConfirmOpen(false)} appearance="subtle">
            إلغاء
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default RestoreRoomButton;
