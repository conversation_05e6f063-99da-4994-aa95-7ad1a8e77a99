import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Whisper, Badge } from 'rsuite';
import { useLocation, useHistory } from 'react-router-dom';
import { useModalState } from '../misc/custom-hooks';
import { useProfile } from '../context/profile.context';
import { useLanguage } from '../context/language.context';
import { auth, database } from '../misc/firebase.config';
import { ref, onValue, off, query, orderByChild, limitToLast } from 'firebase/database';
import Dashboard from './dashboard';
import GearIcon from '@rsuite/icons/Gear';
import ChatIcon from '@rsuite/icons/legacy/Comment';
import DashboardIcon from '@rsuite/icons/Dashboard';
import PeoplesIcon from '@rsuite/icons/Peoples';
import CheckIcon from '@rsuite/icons/Check';

const EnhancedBottomNavbar = () => {
  const { isOpen, close } = useModalState();
  const { isOpen: isSupportOpen, open: openSupport, close: closeSupport } = useModalState();
  const { isOpen: isSettingsOpen, close: closeSettings } = useModalState();
  const { isOpen: isTermsOpen, open: openTerms, close: closeTerms } = useModalState();
  const location = useLocation();
  const history = useHistory();
  const { profile } = useProfile();

  // حالة تفعيل عجلة الحظ
  const [wheelEnabled, setWheelEnabled] = useState(true);

  // حالة وجود رسائل خاصة جديدة
  const [hasNewPrivateMessages, setHasNewPrivateMessages] = useState(false);

  // تحميل حالة تفعيل العجلة
  useEffect(() => {
    if (!auth.currentUser) return;

    const wheelConfigRef = ref(database, '/lucky-wheel/config/enabled');
    onValue(wheelConfigRef, (snapshot) => {
      if (snapshot.exists()) {
        setWheelEnabled(snapshot.val());
      }
    });

    return () => {
      off(wheelConfigRef);
    };
  }, []);

  // الاستماع للرسائل الخاصة الجديدة
  useEffect(() => {
    if (!auth.currentUser || !profile || !profile.uid) return;

    console.log('تهيئة مستمع الرسائل الخاصة الجديدة للمستخدم:', profile.uid);

    // قائمة لتخزين وظائف إلغاء الاشتراك
    const unsubscribeFunctions = [];

    // الاستماع للدردشات الخاصة
    const privateChatsRef = ref(database, 'private-chats');

    const chatsUnsubscribe = onValue(privateChatsRef, (snapshot) => {
      if (!snapshot.exists()) return;

      const chats = snapshot.val();

      // البحث عن الدردشات التي يشارك فيها المستخدم الحالي
      Object.entries(chats).forEach(([chatId, chat]) => {
        if (chat.members && chat.members[profile.uid]) {
          // تحديد المستخدم الآخر في الدردشة
          const otherUserId = Object.keys(chat.members).find(uid => uid !== profile.uid);

          if (otherUserId) {
            // الاستماع للرسائل الجديدة في هذه الدردشة
            const messagesRef = ref(database, 'private-messages');
            const messagesQuery = query(messagesRef, orderByChild('createdAt'), limitToLast(10));

            const messagesUnsubscribe = onValue(messagesQuery, (messagesSnapshot) => {
              if (!messagesSnapshot.exists()) return;

              const messages = messagesSnapshot.val();
              let hasUnread = false;

              // البحث عن رسائل جديدة من المستخدم الآخر
              Object.values(messages).forEach(message => {
                if (message.chatId === chatId &&
                    message.author &&
                    message.author.uid === otherUserId) {

                  // التحقق مما إذا كانت الرسالة حديثة (خلال آخر 24 ساعة)
                  const messageTime = message.createdAt || 0;
                  const currentTime = Date.now();
                  const isRecent = (currentTime - messageTime) < 24 * 60 * 60 * 1000; // 24 ساعة

                  // التحقق مما إذا كان المستخدم في صفحة الدردشة الخاصة مع هذا المستخدم
                  const isInChat = location.pathname === `/private-chat/${otherUserId}`;

                  if (isRecent && !isInChat) {
                    hasUnread = true;
                  }
                }
              });

              if (hasUnread) {
                setHasNewPrivateMessages(true);
              }
            });

            unsubscribeFunctions.push(messagesUnsubscribe);
          }
        }
      });
    });

    unsubscribeFunctions.push(chatsUnsubscribe);

    return () => {
      // إلغاء الاشتراك في جميع المستمعين
      unsubscribeFunctions.forEach(unsubscribe => {
        try {
          unsubscribe();
        } catch (error) {
          console.warn('خطأ في إلغاء الاشتراك:', error);
        }
      });
    };
  }, [profile, location.pathname]);

  // التحقق مما إذا كان المستخدم هو المالك أو مشرف
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );
  const isAdmin = profile && profile.isAdmin;

  // التحقق مما إذا كنا في صفحة دردشة أو دردشة خاصة
  const isChatPage = location.pathname.includes('/chat/') || location.pathname.includes('/private-chat/');

  // التحقق من الصفحة النشطة
  const isHomePage = location.pathname === '/';
  const isPrivateChatsPage = location.pathname === '/private-chats';
  const isSettingsPage = location.pathname === '/settings';

  // التحقق مما إذا كنا في صفحة عجلة الحظ
  const isLuckyWheelPage = location.pathname === '/lucky-wheel' || location.pathname === '/lucky-wheel-admin';

  // إعادة تعيين حالة الرسائل الجديدة عند الانتقال إلى صفحة الدردشات الخاصة
  useEffect(() => {
    if (isPrivateChatsPage || location.pathname.startsWith('/private-chat/')) {
      setHasNewPrivateMessages(false);
    }
  }, [isPrivateChatsPage, location.pathname]);

  // استخدام سياق اللغة
  const { t } = useLanguage();

  // التحقق مما إذا كان المستخدم مسجل الدخول
  const isAuthenticated = profile || localStorage.getItem('authState') === 'authenticated';

  // إذا لم يكن المستخدم مسجل الدخول، لا تعرض شريط التنقل
  if (!isAuthenticated || location.pathname === '/signin') {
    return null;
  }

  return (
    <div className={`enhanced-bottom-navbar ${isChatPage || isLuckyWheelPage ? 'hidden' : ''}`}>
      <div className="bottom-nav-box-container">
        {/* مربع الدردشة في اليسار */}
        <Whisper
          placement="top"
          trigger="hover"
          speaker={<Tooltip>{t('chat')}</Tooltip>}
        >
          <div className={`bottom-nav-box chat-box ${isHomePage ? 'active' : ''}`} onClick={() => location.pathname !== '/' && window.location.replace('/')}>
            <ChatIcon style={{ fontSize: '20px' }} />
          </div>
        </Whisper>

        {/* مربع الدردشة الخاصة في الوسط */}
        <Whisper
          placement="top"
          trigger="hover"
          speaker={<Tooltip>{t('privateChats')}</Tooltip>}
        >
          <div
            className={`bottom-nav-box private-chat-box ${isPrivateChatsPage ? 'active' : ''}`}
            onClick={() => history.push('/private-chats')}
          >
            <div style={{ position: 'relative' }}>
              <PeoplesIcon style={{ fontSize: '20px' }} />
              {hasNewPrivateMessages && (
                <span style={{
                  position: 'absolute',
                  top: '-5px',
                  right: '-5px',
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  backgroundColor: '#f44336',
                  animation: 'blink 1.5s infinite ease-in-out'
                }}></span>
              )}
            </div>
          </div>
        </Whisper>

        {/* مربع عجلة الحظ - يظهر فقط إذا كانت العجلة مفعلة أو المستخدم هو المالك */}
        {(wheelEnabled || isOwner) && (
          <Whisper
            placement="top"
            trigger="hover"
            speaker={<Tooltip>{t('luckyWheel')}</Tooltip>}
          >
            <div
              className={`bottom-nav-box wheel-box ${location.pathname === '/lucky-wheel' ? 'active' : ''}`}
              onClick={() => history.push('/lucky-wheel')}
            >
              <i className="fas fa-dharmachakra" style={{ fontSize: '20px' }}></i>
            </div>
          </Whisper>
        )}

        {/* مربع الإعدادات في اليمين */}
        <Whisper
          placement="top"
          trigger="hover"
          speaker={<Tooltip>{t('settings')}</Tooltip>}
        >
          <div className={`bottom-nav-box settings-box ${isSettingsPage ? 'active' : ''}`} onClick={() => history.push('/settings')}>
            <GearIcon style={{ fontSize: '20px' }} />
          </div>
        </Whisper>
      </div>

      {/* نافذة الإعدادات */}
      <Modal open={isSettingsOpen} onClose={closeSettings} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">الإعدادات</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <div className="settings-options">
            <Button
              appearance="subtle"
              block
              className="settings-option-btn"
              onClick={openSupport}
            >
              الدعم الفني
            </Button>
            <Button
              appearance="subtle"
              block
              className="settings-option-btn"
              onClick={openTerms}
            >
              <DashboardIcon style={{ marginLeft: '5px' }} /> الشروط والسياسات
            </Button>

            {/* زر فحص أكواد الجوائز (يظهر فقط للمالك والمشرفين) */}
            {(isAdmin || isOwner) && (
              <Button
                appearance="subtle"
                block
                className="settings-option-btn reward-code-btn"
                onClick={() => history.push('/reward-code-checker')}
              >
                <CheckIcon style={{ marginLeft: '5px' }} /> فحص أكواد الجوائز
              </Button>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={closeSettings} appearance="subtle" className="dark-close-btn">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة الملف الشخصي */}
      <Drawer open={isOpen} onClose={close} placement="right" size="xs" className="dark-drawer">
        <Dashboard onSignOut={() => {
          // إغلاق النافذة أولاً
          close();

          // إزالة حالة المصادقة من التخزين المحلي قبل تسجيل الخروج
          localStorage.removeItem('authState');

          // تسجيل الخروج
          auth.signOut();

          // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
          window.location.href = '/signin';
        }} />
      </Drawer>

      {/* نافذة الدعم الفني */}
      <Modal open={isSupportOpen} onClose={closeSupport} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">الدعم الفني</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <p className="dark-modal-text">يمكنك التواصل مع فريق الدعم الفني عبر البريد الإلكتروني:</p>
          <p className="dark-modal-email"><strong><EMAIL></strong></p>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={closeSupport} appearance="subtle" className="dark-close-btn">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة الشروط والسياسات */}
      <Modal open={isTermsOpen} onClose={closeTerms} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">الشروط والسياسات</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body terms-modal-body">
          <h4 className="terms-section-title">شروط الاستخدام وسياسات التطبيق</h4>

          <h5 className="terms-subsection-title">1. العمر المسموح</h5>
          <p className="dark-modal-text">
            يُشترط أن يكون عمر المستخدم 14 سنة فما فوق لاستخدام التطبيق.
          </p>

          <h5 className="terms-subsection-title">2. الهدف من التطبيق</h5>
          <p className="dark-modal-text">
            تم تصميم التطبيق لتسهيل التعامل الجيد والآمن بين البائع والمشتري.
          </p>
          <p className="dark-modal-text">
            هذا التطبيق لا يتحمل أي مسؤولية في حال حدوث نصب، احتيال، أو سرقة حسابات/أموال خارج نطاق النظام الرسمي للتطبيق.
          </p>

          <h5 className="terms-subsection-title">3. البيع والشراء داخل التطبيق</h5>
          <p className="dark-modal-text">
            يمكن للمستخدمين عرض حساباتهم للبيع أو البحث عن حسابات للشراء من خلال الأقسام والمجموعات المتاحة.
          </p>
          <p className="dark-modal-text">
            يمنع إرسال المال أو معلومات الحساب لأي شخص داخل الدردشة الخاصة دون إشراف من الإدارة.
          </p>

          <h5 className="terms-subsection-title">4. آلية التعامل الآمن</h5>
          <p className="dark-modal-text">
            عندما يتم الاتفاق بين البائع والمشتري، يتم إبلاغ المشرف.
          </p>
          <p className="dark-modal-text">
            المشرف (الذي يملك علامة زرقاء واسمه يظهر باللون الأزرق) يقوم بإنشاء مجموعة خاصة تضم البائع والمشتري.
          </p>
          <p className="dark-modal-text">
            يتم إضافة الطرفين إلى هذه المجموعة باستخدام رقم الـ ID الخاص بهم.
          </p>
          <p className="dark-modal-text">
            داخل هذه المجموعة، يقوم البائع بإرسال معلومات الحساب إلى المشتري بعد أن يؤكد المشرف استلام المبلغ.
          </p>

          <h5 className="terms-subsection-title">5. آلية الدفع والتحويل</h5>
          <p className="dark-modal-text">
            يتم إرسال المال من المشتري إلى الإدارة/المشرف.
          </p>
          <p className="dark-modal-text">
            بعد استلام المال، يقوم البائع بإرسال معلومات الحساب داخل المجموعة.
          </p>
          <p className="dark-modal-text">
            يقوم المشتري بتغيير معلومات الحساب خلال ساعة واحدة.
          </p>
          <p className="dark-modal-text">
            بعد التغيير، يتم التحقق من طرف المشرف بأن الحساب أصبح ملكًا للمشتري بشكل كامل.
          </p>
          <p className="dark-modal-text">
            بعدها، تقوم الإدارة بتحويل المال للبائع.
          </p>

          <h5 className="terms-subsection-title">6. في حال حدوث مشكلة</h5>
          <p className="dark-modal-text">
            إذا نسى المشتري تغيير معلومات الحساب، وتم استرجاع الحساب من البائع بعد المعاملة، لا تتحمل الإدارة أي مسؤولية.
          </p>
          <p className="dark-modal-text">
            في حال كذب أحد الطرفين (مثل ادعاء معلومات مزيفة)، يتم مراجعة الحالة من قبل المالك مباشرة.
          </p>
          <p className="dark-modal-text">
            إذا ثبتت النية للنصب من أحد الأطراف، يتم حظر حسابه نهائيًا.
          </p>
          <p className="dark-modal-text">
            إذا لم تستطع الإدارة تحديد من هو المخطئ، يتم تحويل القضية إلى الجهات القانونية.
          </p>

          <h5 className="terms-subsection-title">7. النزاعات</h5>
          <p className="dark-modal-text">
            إذا لم تتمكن إدارة التطبيق من تحديد من هو النصاب:
          </p>
          <ul className="terms-list">
            <li>يتم تجميد المبلغ حتى تنتهي التحقيقات.</li>
            <li>يُسمح للإدارة بحجز المبلغ مؤقتًا لضمان حقوق الطرفين.</li>
            <li>إذا لم يُحسم الأمر، يتم تحويل القضية للقانون، ويجب على الطرفين اتخاذ الإجراءات القانونية.</li>
          </ul>

          <h5 className="terms-subsection-title">8. السلوك في الدردشة</h5>
          <p className="dark-modal-text">
            يُمنع تمامًا استخدام كلمات نابية، سب، أو تلاعب داخل الدردشات.
          </p>
          <p className="dark-modal-text">
            أي مخالفة تؤدي إلى حظر دائم أو مؤقت بحسب شدة الحالة.
          </p>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={closeTerms} appearance="subtle" className="dark-close-btn">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EnhancedBottomNavbar;
