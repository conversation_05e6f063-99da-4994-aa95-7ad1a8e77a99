/* أنماط الوضع الخاص والرسائل الخاصة */

// أنماط زر تفعيل الوضع الخاص
.special-mode-toggle {
  margin-right: 5px;

  .room-btn {
    padding: 5px 10px;
    font-size: 12px;

    &.special-mode-active {
      color: #4caf50;
      font-weight: bold;
    }
  }
}

// أنماط واجهة الإرسال في الوضع الخاص
.special-mode-input {
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  margin-top: 10px;

  .special-mode-button {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 10px;
    font-weight: bold;

    i {
      margin-left: 8px;
    }

    &:hover {
      background-color: #388e3c;
    }
  }
}

// أنماط نافذة إنشاء رسالة خاصة
.special-message-modal {
  .special-image-editor {
    margin: 0 auto;
    border: 2px solid #4caf50;
    border-radius: 5px;
  }

  .special-image-placeholder {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
    border: 2px dashed #666;
    border-radius: 5px;
    color: #ccc;

    i {
      margin-bottom: 10px;
    }
  }

  .special-uploader {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px dashed #666;
    border-radius: 5px;
    color: #ccc;

    .rs-uploader-trigger-btn {
      color: #ccc;
    }
  }

  .special-input {
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    border: 1px solid #444;
  }
}

// أنماط عرض الرسالة الخاصة
.special-message-container {
  margin: 15px 0;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  border-right: 3px solid #4caf50;

  .special-message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .special-message-author {
      display: flex;
      align-items: center;

      .author-name {
        margin: 0 10px;
        font-weight: bold;
      }

      .message-time {
        color: #aaa;
        font-size: 12px;
      }
    }

    .special-message-delete-btn {
      color: #f44336;
      padding: 2px 8px;

      &:hover {
        background-color: rgba(244, 67, 54, 0.1);
      }

      i {
        margin-left: 5px;
      }
    }
  }

  .special-message-content {
    display: flex;
    flex-direction: column;

    .special-message-image {
      margin-bottom: 10px;
      cursor: pointer;
      text-align: center;

      img {
        max-width: 100%;
        max-height: 600px; // زيادة الارتفاع للصور العمودية في الرسائل الخاصة
        width: auto; // السماح للصورة بالحفاظ على نسبة العرض إلى الارتفاع
        height: auto; // السماح للصورة بالحفاظ على نسبة العرض إلى الارتفاع
        border-radius: 5px;
        object-fit: contain;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }
    }

    .special-message-description {
      margin-bottom: 15px;
      font-size: 16px;
      line-height: 1.5;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 10px;
      border-radius: 8px;
    }

    .special-message-target-btn {
      align-self: center;
      margin-top: 10px;
      padding: 8px 15px;
      font-weight: bold;
      background-color: #4caf50;
      color: white;
      border: none;
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #388e3c;
        transform: scale(1.05);
      }
    }
  }
}

// أنماط نافذة معاينة الصورة
.image-preview-modal {
  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .full-size-image {
      max-width: 100%;
      max-height: 90vh; // زيادة الارتفاع لعرض الصور بشكل أكبر
      object-fit: contain;
      width: auto; // السماح للصورة بالحفاظ على نسبة العرض إلى الارتفاع
      height: auto; // السماح للصورة بالحفاظ على نسبة العرض إلى الارتفاع
    }
  }
}

// تنسيق حاوية الصورة في الرسائل
.message-image-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 5px 0;
}

// تنسيق الصورة في الرسائل
.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// تنسيق الصورة العمودية
.vertical-image {
  max-width: 100%;
  max-height: 600px; // زيادة الارتفاع للصور العمودية
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// تنسيق رابط عرض الصورة الأصلية
.view-original-link {
  margin-right: 10px;
  color: #4caf50;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
