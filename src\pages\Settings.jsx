import React from 'react';
import { Button, Container, Content, Header, Modal, ButtonGroup } from 'rsuite';
import { useModalState } from '../misc/custom-hooks';
import DashboardIcon from '@rsuite/icons/Dashboard';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';
import GearIcon from '@rsuite/icons/Gear';
import { useHistory } from 'react-router';
import { useLanguage } from '../context/language.context';
import TranslatedText from '../components/TranslatedText';

const Settings = () => {
  const history = useHistory();
  const { isOpen: isSupportOpen, open: openSupport, close: closeSupport } = useModalState();
  const { isOpen: isTermsOpen, open: openTerms, close: closeTerms } = useModalState();
  const { isOpen: isLanguageOpen, open: openLanguage, close: closeLanguage } = useModalState();

  // استخدام سياق اللغة
  const { t, currentLanguage, changeLanguage, LANGUAGES } = useLanguage();

  const handleBack = () => {
    // التحقق من وجود صفحة سابقة في تاريخ التصفح
    if (window.history.length > 1) {
      // إذا كان هناك صفحة سابقة، استخدم history.goBack()
      history.goBack();
    } else {
      // إذا لم يكن هناك صفحة سابقة، انتقل إلى الصفحة الرئيسية
      history.push('/');
    }
  };

  return (
    <Container className="dark-container">
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          <h4 className="header-title"><TranslatedText text="settings" /></h4>
        </div>
      </Header>
      <Content className="dark-content">
        <div className="settings-page">
          <div className="settings-options">
            <Button
              appearance="subtle"
              block
              className="settings-option-btn"
              onClick={openSupport}
            >
              <TranslatedText text="support" />
            </Button>
            <Button
              appearance="subtle"
              block
              className="settings-option-btn"
              onClick={openTerms}
            >
              <DashboardIcon style={{ margin: '0 5px' }} /> <TranslatedText text="termsAndPolicies" />
            </Button>
            <Button
              appearance="subtle"
              block
              className="settings-option-btn"
              onClick={openLanguage}
            >
              <GearIcon style={{ margin: '0 5px' }} /> <TranslatedText text="language" />
            </Button>

          </div>
        </div>
      </Content>

      {/* نافذة الدعم الفني */}
      <Modal open={isSupportOpen} onClose={closeSupport} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title"><TranslatedText text="support" /></Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <p className="dark-modal-text"><TranslatedText text="contactSupport" />:</p>
          <p className="dark-modal-email"><strong><EMAIL></strong></p>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={closeSupport} appearance="subtle" className="dark-close-btn">
            <TranslatedText text="close" />
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة الشروط والسياسات */}
      <Modal open={isTermsOpen} onClose={closeTerms} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title"><TranslatedText text="termsAndPolicies" /></Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body terms-modal-body">
          <h4 className="terms-section-title"><TranslatedText text="termsOfUseAndPolicies" /></h4>

          <h5 className="terms-subsection-title">1. <TranslatedText text="allowedAge" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="ageRequirement" />
          </p>

          <h5 className="terms-subsection-title">2. <TranslatedText text="appPurpose" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="appPurposeDescription1" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="appPurposeDescription2" />
          </p>

          <h5 className="terms-subsection-title">3. <TranslatedText text="buyingAndSellingInApp" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="buyingAndSellingDescription1" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="buyingAndSellingDescription2" />
          </p>

          <h5 className="terms-subsection-title">4. <TranslatedText text="safeDealingMechanism" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="safeDealingDescription1" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="safeDealingDescription2" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="safeDealingDescription3" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="safeDealingDescription4" />
          </p>

          <h5 className="terms-subsection-title">5. <TranslatedText text="paymentMechanism" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="paymentDescription1" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="paymentDescription2" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="paymentDescription3" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="paymentDescription4" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="paymentDescription5" />
          </p>

          <h5 className="terms-subsection-title">6. <TranslatedText text="problemOccurrence" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="problemDescription1" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="problemDescription2" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="problemDescription3" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="problemDescription4" />
          </p>

          <h5 className="terms-subsection-title">7. <TranslatedText text="disputes" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="disputesDescription" />
          </p>
          <ul className="terms-list">
            <li><TranslatedText text="disputePoint1" /></li>
            <li><TranslatedText text="disputePoint2" /></li>
            <li><TranslatedText text="disputePoint3" /></li>
          </ul>

          <h5 className="terms-subsection-title">8. <TranslatedText text="chatBehavior" /></h5>
          <p className="dark-modal-text">
            <TranslatedText text="chatBehaviorDescription1" />
          </p>
          <p className="dark-modal-text">
            <TranslatedText text="chatBehaviorDescription2" />
          </p>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={closeTerms} appearance="subtle" className="dark-close-btn">
            <TranslatedText text="close" />
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة تغيير اللغة */}
      <Modal open={isLanguageOpen} onClose={closeLanguage} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title"><TranslatedText text="language" /></Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <p className="dark-modal-text"><TranslatedText text="selectLanguage" />:</p>
          <div className="language-buttons">
            <ButtonGroup justified>
              <Button
                appearance={currentLanguage === LANGUAGES.AR ? "primary" : "subtle"}
                onClick={() => changeLanguage(LANGUAGES.AR)}
                className={`language-btn ${currentLanguage === LANGUAGES.AR ? 'active' : ''}`}
              >
                <TranslatedText text="arabic" />
              </Button>
              <Button
                appearance={currentLanguage === LANGUAGES.EN ? "primary" : "subtle"}
                onClick={() => changeLanguage(LANGUAGES.EN)}
                className={`language-btn ${currentLanguage === LANGUAGES.EN ? 'active' : ''}`}
              >
                <TranslatedText text="english" />
              </Button>
              <Button
                appearance={currentLanguage === LANGUAGES.KU ? "primary" : "subtle"}
                onClick={() => changeLanguage(LANGUAGES.KU)}
                className={`language-btn ${currentLanguage === LANGUAGES.KU ? 'active' : ''}`}
              >
                <TranslatedText text="kurdish" />
              </Button>
            </ButtonGroup>
          </div>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={closeLanguage} appearance="subtle" className="dark-close-btn">
            <TranslatedText text="close" />
          </Button>
        </Modal.Footer>
      </Modal>



    </Container>
  );
};

export default Settings;
