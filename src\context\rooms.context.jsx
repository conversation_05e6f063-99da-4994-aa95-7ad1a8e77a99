import { off, onValue, ref, get } from "firebase/database";
import React, { createContext, useContext, useEffect, useState } from "react";
import { database, auth } from "../misc/firebase.config";
import { transformToArrWithId } from "../misc/helpers";
import { useLocation } from "react-router-dom";

const RoomsContext = createContext();

export const RoomsProvider = ({ children }) => {
  const [rooms, setRooms] = useState();
  const location = useLocation();

  useEffect(() => {
    const roomListRef = ref(database, "rooms");

    // subscribe to realtime database
    onValue(roomListRef, async (snap) => {
      if (snap.exists()) {
        const data = transformToArrWithId(snap.val());

        // التحقق مما إذا كنا في صفحة الدردشات الخاصة
        const isPrivateChatsPage = location.pathname === '/private-chats';

        // التحقق مما إذا كنا في صفحة دردشة محددة
        const isChatPage = location.pathname.includes('/chat/');
        const chatId = isChatPage ? location.pathname.split('/chat/')[1] : null;

        // جلب قائمة المجموعات المخفية للمالك
        let ownerHiddenRooms = {};
        if (auth.currentUser) {
          const hiddenRoomsRef = ref(database, `/users/${auth.currentUser.uid}/hiddenRooms`);
          const hiddenRoomsSnap = await get(hiddenRoomsRef);
          if (hiddenRoomsSnap.exists()) {
            ownerHiddenRooms = hiddenRoomsSnap.val();
          }
        }

        // تصفية الغرف بناءً على الخصوصية
        const filteredRooms = data.filter(room => {
          // التحقق مما إذا كان المستخدم هو المالك
          const isOwner = auth.currentUser && (
            auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
            auth.currentUser.email === '<EMAIL>'
          );

          // إذا كانت المجموعة مخفية من قبل المالك في الصفحة الرئيسية، لا تظهرها
          if (isOwner && ownerHiddenRooms && ownerHiddenRooms[room.id]) {
            // إذا كنا في صفحة دردشة محددة وهذه هي الدردشة المطلوبة، أظهرها بغض النظر
            if (isChatPage && room.id === chatId) {
              return true;
            }
            return false;
          }

          // إذا كانت المجموعة مستعادة أو مخفية، أظهرها فقط للمالك
          if (room.isRestored || room.isHidden) {
            return isOwner;
          }

          // إذا كنا في صفحة دردشة محددة وهذه هي الدردشة المطلوبة، أظهرها دائماً
          if (isChatPage && room.id === chatId) {
            return true;
          }

          // إذا كانت الغرفة دردشة خاصة بين شخصين
          if (room.isDirectMessage) {
            // تحقق مما إذا كان المستخدم عضو في المجموعة
            const isMember = room.members && room.members[auth.currentUser.uid];

            // إذا كنا في صفحة الدردشات الخاصة، أظهر جميع الدردشات الخاصة التي المستخدم عضو فيها
            if (isPrivateChatsPage && isMember) {
              return true;
            }

            // إذا كنا في صفحة دردشة محددة وهذه هي الدردشة المطلوبة، أظهرها دائماً
            if (isChatPage && room.id === chatId) {
              return true;
            }

            // لا تظهر الدردشات الخاصة في الصفحة الرئيسية
            // فقط تظهر في صفحة الدردشات الخاصة أو عند فتحها مباشرة
            return false;
          }

          // إذا كانت الغرفة عامة، أظهرها للجميع
          if (!room.isPrivate) {
            return true;
          }

          // إذا كان المستخدم هو مشرف الغرفة، أظهرها له
          if (room.admins && room.admins[auth.currentUser.uid]) {
            return true;
          }

          // إذا كان المستخدم في قائمة المستخدمين المسموح لهم، أظهرها له
          if (room.allowedUsers && room.allowedUsers[auth.currentUser.uid]) {
            return true;
          }

          // غير ذلك، لا تظهر الغرفة
          return false;
        });

        setRooms(filteredRooms);
      } else {
        setRooms([]);
      }
    });

    // unsubscribe to realtime database
    return () => {
      off(roomListRef);
    };
  }, [location.pathname]);

  return (
    <RoomsContext.Provider value={rooms}>{children}</RoomsContext.Provider>
  );
};

export const useRooms = () => useContext(RoomsContext);
