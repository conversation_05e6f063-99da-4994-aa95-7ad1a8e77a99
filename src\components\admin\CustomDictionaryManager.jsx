import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Form, Schema, Message, toaster, Loader, Table } from 'rsuite';
import { database } from '../../misc/firebase.config';
import { ref, set, get } from 'firebase/database';
import { LANGUAGES, customDictionary } from '../../context/language.context';

const { StringType } = Schema.Types;

const CustomDictionaryManager = ({ open, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [dictionaryItems, setDictionaryItems] = useState({});
  const [newKey, setNewKey] = useState('');
  const [newTranslations, setNewTranslations] = useState({
    [LANGUAGES.AR]: '',
    [LANGUAGES.EN]: '',
    [LANGUAGES.KU]: '',
  });
  const [editingKey, setEditingKey] = useState(null);

  // تحميل قاموس الكلمات المخصص
  useEffect(() => {
    if (!open) return;

    const loadDictionary = async () => {
      try {
        setIsLoading(true);
        const dictionaryRef = ref(database, 'customDictionary');
        const snapshot = await get(dictionaryRef);

        if (snapshot.exists()) {
          setDictionaryItems(snapshot.val());
        } else {
          // إذا لم يكن هناك قاموس مخصص في قاعدة البيانات، استخدم القاموس المحلي
          await set(dictionaryRef, customDictionary);
          setDictionaryItems(customDictionary);
        }
      } catch (error) {
        console.error('Error loading dictionary:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            حدث خطأ أثناء تحميل القاموس المخصص
          </Message>
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadDictionary();
  }, [open]);

  // إضافة كلمة جديدة إلى القاموس
  const handleAddWord = async () => {
    if (!newKey.trim()) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          يرجى إدخال مفتاح للكلمة
        </Message>
      );
      return;
    }

    if (!newTranslations[LANGUAGES.AR].trim() || !newTranslations[LANGUAGES.EN].trim() || !newTranslations[LANGUAGES.KU].trim()) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          يرجى إدخال الترجمات لجميع اللغات
        </Message>
      );
      return;
    }

    try {
      const updatedDictionary = {
        ...dictionaryItems,
        [newKey]: newTranslations
      };

      await set(ref(database, 'customDictionary'), updatedDictionary);
      setDictionaryItems(updatedDictionary);

      // إعادة تعيين الحقول
      setNewKey('');
      setNewTranslations({
        [LANGUAGES.AR]: '',
        [LANGUAGES.EN]: '',
        [LANGUAGES.KU]: '',
      });

      toaster.push(
        <Message type="success" closable duration={4000}>
          تمت إضافة الكلمة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error adding word:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء إضافة الكلمة
        </Message>
      );
    }
  };

  // تحديث كلمة موجودة في القاموس
  const handleUpdateWord = async (key) => {
    try {
      const updatedDictionary = {
        ...dictionaryItems,
        [key]: dictionaryItems[key]
      };

      await set(ref(database, 'customDictionary'), updatedDictionary);

      setEditingKey(null);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تحديث الكلمة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error updating word:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تحديث الكلمة
        </Message>
      );
    }
  };

  // حذف كلمة من القاموس
  const handleDeleteWord = async (key) => {
    try {
      const updatedDictionary = { ...dictionaryItems };
      delete updatedDictionary[key];

      await set(ref(database, 'customDictionary'), updatedDictionary);
      setDictionaryItems(updatedDictionary);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حذف الكلمة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error deleting word:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء حذف الكلمة
        </Message>
      );
    }
  };

  // تحديث قيمة الترجمة للكلمة المحددة
  const handleTranslationChange = (key, lang, value) => {
    setDictionaryItems({
      ...dictionaryItems,
      [key]: {
        ...dictionaryItems[key],
        [lang]: value
      }
    });
  };

  return (
    <Modal open={open} onClose={onClose} size="lg" className="dark-modal">
      <Modal.Header className="dark-modal-header">
        <Modal.Title className="dark-modal-title">إدارة قاموس الكلمات المخصص</Modal.Title>
      </Modal.Header>
      <Modal.Body className="dark-modal-body">
        {isLoading ? (
          <div className="loader-container">
            <Loader size="md" content="جاري التحميل..." />
          </div>
        ) : (
          <>
            {/* نموذج إضافة كلمة جديدة */}
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>مفتاح الكلمة</Form.ControlLabel>
                <Form.Control
                  name="key"
                  value={newKey}
                  onChange={value => setNewKey(value)}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>الترجمة العربية</Form.ControlLabel>
                <Form.Control
                  name="ar"
                  value={newTranslations[LANGUAGES.AR]}
                  onChange={value => setNewTranslations({ ...newTranslations, [LANGUAGES.AR]: value })}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>الترجمة الإنجليزية</Form.ControlLabel>
                <Form.Control
                  name="en"
                  value={newTranslations[LANGUAGES.EN]}
                  onChange={value => setNewTranslations({ ...newTranslations, [LANGUAGES.EN]: value })}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>الترجمة الكردية</Form.ControlLabel>
                <Form.Control
                  name="ku"
                  value={newTranslations[LANGUAGES.KU]}
                  onChange={value => setNewTranslations({ ...newTranslations, [LANGUAGES.KU]: value })}
                />
              </Form.Group>
              <Button appearance="primary" onClick={handleAddWord}>
                إضافة كلمة
              </Button>
            </Form>

            <hr className="dictionary-divider" />

            {/* جدول الكلمات الموجودة */}
            <h4 className="dictionary-section-title">الكلمات الموجودة في القاموس</h4>
            <Table
              data={Object.keys(dictionaryItems).map(key => ({ key, ...dictionaryItems[key] }))}
              autoHeight
              className="dictionary-table"
            >
              <Table.Column width={150}>
                <Table.HeaderCell>المفتاح</Table.HeaderCell>
                <Table.Cell dataKey="key" />
              </Table.Column>
              <Table.Column width={200}>
                <Table.HeaderCell>العربية</Table.HeaderCell>
                <Table.Cell>
                  {rowData => (
                    editingKey === rowData.key ? (
                      <Form.Control
                        value={dictionaryItems[rowData.key][LANGUAGES.AR]}
                        onChange={value => handleTranslationChange(rowData.key, LANGUAGES.AR, value)}
                      />
                    ) : (
                      dictionaryItems[rowData.key][LANGUAGES.AR]
                    )
                  )}
                </Table.Cell>
              </Table.Column>
              <Table.Column width={200}>
                <Table.HeaderCell>الإنجليزية</Table.HeaderCell>
                <Table.Cell>
                  {rowData => (
                    editingKey === rowData.key ? (
                      <Form.Control
                        value={dictionaryItems[rowData.key][LANGUAGES.EN]}
                        onChange={value => handleTranslationChange(rowData.key, LANGUAGES.EN, value)}
                      />
                    ) : (
                      dictionaryItems[rowData.key][LANGUAGES.EN]
                    )
                  )}
                </Table.Cell>
              </Table.Column>
              <Table.Column width={200}>
                <Table.HeaderCell>الكردية</Table.HeaderCell>
                <Table.Cell>
                  {rowData => (
                    editingKey === rowData.key ? (
                      <Form.Control
                        value={dictionaryItems[rowData.key][LANGUAGES.KU]}
                        onChange={value => handleTranslationChange(rowData.key, LANGUAGES.KU, value)}
                      />
                    ) : (
                      dictionaryItems[rowData.key][LANGUAGES.KU]
                    )
                  )}
                </Table.Cell>
              </Table.Column>
              <Table.Column width={150} fixed="right">
                <Table.HeaderCell>الإجراءات</Table.HeaderCell>
                <Table.Cell>
                  {rowData => (
                    <div className="dictionary-actions">
                      {editingKey === rowData.key ? (
                        <>
                          <Button appearance="primary" size="xs" onClick={() => handleUpdateWord(rowData.key)}>
                            حفظ
                          </Button>
                          <Button appearance="subtle" size="xs" onClick={() => setEditingKey(null)}>
                            إلغاء
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button appearance="primary" size="xs" onClick={() => setEditingKey(rowData.key)}>
                            تعديل
                          </Button>
                          <Button appearance="subtle" color="red" size="xs" onClick={() => handleDeleteWord(rowData.key)}>
                            حذف
                          </Button>
                        </>
                      )}
                    </div>
                  )}
                </Table.Cell>
              </Table.Column>
            </Table>
          </>
        )}
      </Modal.Body>
      <Modal.Footer className="dark-modal-footer">
        <Button onClick={onClose} appearance="subtle" className="dark-close-btn">
          إغلاق
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CustomDictionaryManager;
