const { initializeApp } = require('firebase/app');
const { getDatabase, ref, set, getAuth, signInWithEmailAndPassword } = require('firebase/database');

// تكوين Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDXYQFC7QiRZ1sP-3QQnX_J3GQRjE1kkwI",
  authDomain: "toika-369.firebaseapp.com",
  databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
  projectId: "toika-369",
  storageBucket: "toika-369.appspot.com",
  messagingSenderId: "1000053379604",
  appId: "1:1000053379604:web:c9b8a9c6c0a0e1c9a0e1c9"
};

// تهيئة Firebase
const app = initializeApp(firebaseConfig);
const database = getDatabase(app);

// نحتاج إلى تسجيل الدخول أولاً للحصول على صلاحيات الكتابة
// لكن هذا يتطلب معلومات تسجيل الدخول التي لا نملكها

// بدلاً من ذلك، سنستخدم واجهة المستخدم لتعديل البطاقات
console.log('لا يمكن تعديل البطاقات مباشرة من خلال هذا السكريبت بسبب قيود الأمان.');
console.log('يرجى استخدام واجهة المستخدم لتعديل البطاقات بعد تسجيل الدخول كمالك.');
console.log('اتبع الخطوات التالية:');
console.log('1. قم بتسجيل الدخول إلى التطبيق باستخدام حساب المالك (<EMAIL>)');
console.log('2. انتقل إلى صفحة عجلة الحظ');
console.log('3. استخدم أدوات المطور في المتصفح (F12) للوصول إلى وحدة التحكم');
console.log('4. قم بتنفيذ الأمر التالي في وحدة التحكم:');
console.log(`
const database = firebase.database();
const ticketsRef = database.ref('/lucky-wheel/users/W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1/tickets');
ticketsRef.set(9999).then(() => console.log('تم تعيين 9999 بطاقة للمالك بنجاح!')).catch(err => console.error(err));
`);

process.exit(0);
