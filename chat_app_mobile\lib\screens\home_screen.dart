import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/webview_service.dart';
import '../services/notification_service.dart';
import '../services/firebase_service.dart';
import '../widgets/loading_indicator.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  WebViewController? _controller;
  bool _isLoading = true;
  StreamSubscription? _messageSubscription;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _listenForMessages();
  }

  // Initialize WebView
  void _initializeWebView() {
    _controller = WebViewService.initializeController();

    // Auto login if credentials are saved
    Future.delayed(const Duration(seconds: 2), () {
      WebViewService.autoLogin();
    });
  }

  // Listen for messages from WebView
  void _listenForMessages() {
    _messageSubscription = WebViewService.messageStream.listen((message) {
      // Handle messages from WebView
      switch (message['type']) {
        case 'replyNotification':
          _handleReplyNotification(message['data']);
          break;
        case 'userId':
          _handleUserId(message['data']);
          break;
        default:
          print('Unknown message type: ${message['type']}');
      }
    });
  }

  // Handle reply notification
  void _handleReplyNotification(Map<String, dynamic> data) {
    // Show notification
    NotificationService.showNotification(
      title: data['title'] ?? 'New Reply',
      body: data['body'] ?? 'Someone replied to your message',
      payload: data['payload'],
    );
  }

  // Handle user ID from WebView
  void _handleUserId(Map<String, dynamic> data) async {
    final userId = data['userId'];
    if (userId != null) {
      print('Received user ID from WebView: $userId');

      // Get FCM token
      final token = await FirebaseService.getFcmToken();

      // Register device token with user ID
      if (token != null) {
        await NotificationService.registerDeviceToken(token, userId);
      }
    }
  }

  // Handle back button press
  Future<bool> _handleBackButton() async {
    if (_controller == null) return true;

    if (await _controller!.canGoBack()) {
      await _controller!.goBack();
      return false;
    }
    return true;
  }

  @override
  void dispose() {
    _messageSubscription?.cancel();
    WebViewService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        final canPop = await _handleBackButton();
        if (canPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Stack(
            children: [
              // WebView
              WebViewWidget(controller: _controller!),

              // Loading indicator
              if (_isLoading) const LoadingIndicator(),

              // Progress listener for WebView
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: StreamBuilder<double>(
                  stream: Stream.periodic(const Duration(milliseconds: 100), (
                    _,
                  ) {
                    // Check if navigation is complete
                    if (!_isLoading) {
                      return 1.0;
                    }
                    return 0.5; // Indeterminate progress
                  }),
                  initialData: 0.0,
                  builder: (context, snapshot) {
                    // Hide progress bar when loading is complete
                    if (!_isLoading || snapshot.data! >= 1.0) {
                      return const SizedBox.shrink();
                    }

                    // Show progress indicator
                    return LinearProgressIndicator(
                      value: snapshot.data! < 0 ? null : snapshot.data,
                      backgroundColor: Colors.transparent,
                      color: Theme.of(context).primaryColor,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
