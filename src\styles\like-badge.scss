// تنسيق شارة الإعجاب
.like-badge {
  .rs-badge-content {
    font-size: 14px !important; // زيادة حجم الخط
    font-weight: bold !important; // جعل الخط سميك
    min-width: 18px !important; // زيادة العرض الأدنى
    height: 18px !important; // زيادة الارتفاع
    line-height: 18px !important; // ضبط ارتفاع السطر
    padding: 0 4px !important; // زيادة المساحة الداخلية
    background-color: rgba(255, 0, 0, 0.8) !important; // تغيير لون الخلفية
    font-family: 'Arial', sans-serif !important; // استخدام خط إنجليزي
    direction: ltr !important; // اتجاه من اليسار إلى اليمين للأرقام الإنجليزية
  }
}

// تنسيق زر الإعجاب للمستخدم الحالي (صاحب الرسالة)
.like-button-self {
  opacity: 0.8; // جعل الزر شفاف قليلاً
  cursor: default !important; // تغيير شكل المؤشر
  margin-right: 5px; // إضافة مسافة بين زر الإعجاب وزر الحذف

  .rs-btn {
    background-color: rgba(0, 0, 0, 0.2) !important; // تغيير لون خلفية الزر

    &:hover {
      background-color: rgba(0, 0, 0, 0.2) !important; // منع تغيير اللون عند التحويم
    }
  }
}

// تنسيق زر الإعجاب للمستخدمين الآخرين
.like-button {
  .rs-badge-content {
    transition: all 0.3s ease; // إضافة تأثير انتقالي
  }

  &:hover .rs-badge-content {
    transform: scale(1.1); // تكبير الشارة عند التحويم
  }
}
