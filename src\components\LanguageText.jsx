import React from 'react';
import { useLanguage } from '../context/language.context';

/**
 * مكون لعرض النص بالتنسيق المناسب للغة الحالية
 * يضيف فئة CSS مناسبة للغة دون تغيير اتجاه الصفحة
 */
const LanguageText = ({ children, className = '', ...props }) => {
  const { currentLanguage, LANGUAGES } = useLanguage();
  
  // تحديد فئة CSS بناءً على اللغة الحالية
  let langClass = '';
  if (currentLanguage === LANGUAGES.AR) {
    langClass = 'ar-text';
  } else if (currentLanguage === LANGUAGES.KU) {
    langClass = 'ku-text';
  }
  
  // دمج الفئات
  const combinedClassName = `${langClass} ${className}`.trim();
  
  return (
    <span className={combinedClassName} {...props}>
      {children}
    </span>
  );
};

export default LanguageText;
