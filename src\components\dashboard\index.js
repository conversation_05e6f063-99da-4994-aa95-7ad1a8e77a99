import { ref, update, get, set } from "firebase/database";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Divider, Drawer, Message, toaster } from "rsuite";
import { useProfile } from "../../context/profile.context";
import { database } from "../../misc/firebase.config";
import { getUserUpdates } from "../../misc/helpers";
import EditableInput from "../EditableInput";
import AvatarUploadBtn from "./AvatarUploadBtn";
import ProviderBlock from "./ProviderBlock";

const Dashboard = ({ onSignOut }) => {
  const { profile } = useProfile();
  const [canChangeName, setCanChangeName] = useState(true);
  const [lastNameChange, setLastNameChange] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [daysLeft, setDaysLeft] = useState(0);

  // التحقق مما إذا كان المستخدم مشرفًا أو مالكًا
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        // التحقق مما إذا كان المستخدم هو المالك
        const isOwnerUser = profile.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
                           profile.email === '<EMAIL>';

        // التحقق مما إذا كان المستخدم مشرفًا
        const adminRef = ref(database, `/admins/${profile.uid}`);
        const adminSnapshot = await get(adminRef);
        const isAdminUser = adminSnapshot.exists();

        setIsAdmin(isAdminUser);
        setIsOwner(isOwnerUser);

        // إذا كان المستخدم مشرفًا أو مالكًا، فيمكنه تغيير اسمه في أي وقت
        if (isAdminUser || isOwnerUser) {
          setCanChangeName(true);
          return;
        }

        // التحقق من آخر مرة تم فيها تغيير الاسم
        const lastChangeRef = ref(database, `/profile/${profile.uid}/lastNameChange`);
        const snapshot = await get(lastChangeRef);

        if (snapshot.exists()) {
          const lastChangeTime = snapshot.val();
          const now = Date.now();
          const daysSinceLastChange = Math.floor((now - lastChangeTime) / (1000 * 60 * 60 * 24));

          // يمكن تغيير الاسم مرة واحدة كل 7 أيام
          if (daysSinceLastChange < 7) {
            setCanChangeName(false);
            setDaysLeft(7 - daysSinceLastChange);
          } else {
            setCanChangeName(true);
          }

          setLastNameChange(lastChangeTime);
        } else {
          // إذا لم يتم تغيير الاسم من قبل، فيمكن تغييره
          setCanChangeName(true);
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
      }
    };

    if (profile) {
      checkAdminStatus();
    }
  }, [profile]);

  const onSave = async (newData) => {
    try {
      // إذا كان المستخدم ليس مشرفًا أو مالكًا ولا يمكنه تغيير اسمه
      if (!isAdmin && !isOwner && !canChangeName) {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لا يمكنك تغيير اسمك إلا مرة واحدة كل 7 أيام. يرجى الانتظار {daysLeft} يوم.
          </Message>
        );
        return;
      }

      const updates = await getUserUpdates(
        profile.uid,
        "name",
        newData,
        database
      );

      // إضافة تحديث لتسجيل وقت آخر تغيير للاسم
      if (!isAdmin && !isOwner) {
        updates[`/profile/${profile.uid}/lastNameChange`] = Date.now();
      }

      await update(ref(database), updates);

      // تحديث حالة التغيير
      if (!isAdmin && !isOwner) {
        setLastNameChange(Date.now());
        setCanChangeName(false);
        setDaysLeft(7);
      }

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تحديث الاسم بنجاح
        </Message>
      );
    } catch (error) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          {error.message}
        </Message>
      );
    }
  };

  return (
    <>
      <Drawer.Header>
        <Drawer.Title>الملف الشخصي</Drawer.Title>
      </Drawer.Header>

      <Drawer.Body>
        <div style={{ height: "90%" }}>
          <h3>مرحباً، {profile.name}</h3>
          <p className="user-email">{profile.email}</p>
          <ProviderBlock />
          <Divider />

          {/* إنذار لتغيير الاسم والصورة */}
          {!isAdmin && !isOwner && (
            <div className="name-change-warning">
              <div className="warning-box">
                يمكنك تغيير الاسم والصورة مرة واحدة في الأسبوع
                {!canChangeName && daysLeft > 0 && (
                  <div>
                    متبقي {daysLeft} يوم قبل أن تتمكن من تغيير اسمك مرة أخرى
                  </div>
                )}
              </div>
            </div>
          )}

          <EditableInput
            name="nickname"
            initialValue={profile.name}
            onSave={onSave}
            label={<h6 className="mb-2">الاسم المستعار</h6>}
            disabled={!canChangeName && !isAdmin && !isOwner}
          />
          <AvatarUploadBtn isAdmin={isAdmin} isOwner={isOwner} />
        </div>
        <div style={{ height: "10%" }}>
          <Drawer.Actions>
            <Button block color="red" appearance="primary" onClick={onSignOut}>
              تسجيل الخروج
            </Button>
          </Drawer.Actions>
        </div>
      </Drawer.Body>
    </>
  );
};

export default Dashboard;
