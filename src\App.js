import React, { useEffect } from "react";
import { Switch, Route } from "react-router";
import "rsuite/dist/rsuite.min.css";
import "./styles/main.scss";

import PrivateRoute from "./components/PrivateRoute";
import PublicRoute from "./components/PublicRoute";
import { ProfileProvider } from "./context/profile.context";
import { NotificationProvider } from "./context/notification.context";
import Home from "./pages/Home";
import SignIn from "./pages/SignIn";
import AuthBridge from "./pages/AuthBridge";
import DeletedRoomsPage from "./pages/DeletedRoomsPage";
import BannedUsersPage from "./pages/BannedUsersPage";
import RecordsPage from "./pages/RecordsPage";
import Settings from "./pages/Settings";
import PrivateChats from "./pages/PrivateChats";
import PrivateChat from "./pages/PrivateChat";
import LuckyWheel from "./pages/LuckyWheel";
import LuckyWheelAdmin from "./pages/LuckyWheelAdmin";
import Reward<PERSON>ode<PERSON>hecker from "./pages/RewardCodeChecker";
import Notifications from "./pages/Notifications";
import { auth, database } from "./misc/firebase.config";
import { ref, get, set, serverTimestamp } from "firebase/database";
import EnhancedBottomNavbar from "./components/EnhancedBottomNavbar";
import { startChatScheduler } from "./misc/auto-chat-scheduler";

function App() {

  // التحقق من حالة المصادقة عند بدء التطبيق
  useEffect(() => {
    const checkAuth = () => {
      // إضافة مستمع لتغييرات حالة المصادقة
      const unsubscribe = auth.onAuthStateChanged(user => {
        console.log('Auth state changed in App.js:', user ? `User: ${user.email}` : 'No user');

        if (user) {
          // تخزين حالة المصادقة في التخزين المحلي
          localStorage.setItem('authState', 'authenticated');

          // التحقق من وجود المستخدم في قاعدة البيانات
          const userRef = ref(database, `/users/${user.uid}`);
          get(userRef).then(snapshot => {
            if (!snapshot.exists()) {
              // إذا لم يكن المستخدم موجودًا، قم بإنشائه
              set(userRef, {
                name: user.displayName,
                email: user.email,
                createdAt: serverTimestamp(),
                isAdmin: user.email === '<EMAIL>'
              });
            }
          });
        } else {
          // إزالة حالة المصادقة من التخزين المحلي
          localStorage.removeItem('authState');
        }
      });

      // تنظيف المستمع عند إلغاء تحميل المكون
      return () => {
        unsubscribe();
      };
    };

    checkAuth();
  }, []);

  // بدء جدولة الدردشة التلقائية
  useEffect(() => {
    // بدء جدولة الدردشة التلقائية عند تحميل التطبيق
    startChatScheduler();
  }, []);

  return (
    <ProfileProvider>
      <NotificationProvider>
        <Switch>
          <PublicRoute path="/signin">
            <SignIn />
          </PublicRoute>

          <Route path="/auth-bridge">
            <AuthBridge />
          </Route>

          <PrivateRoute path="/deleted-rooms">
            <DeletedRoomsPage />
          </PrivateRoute>

          <PrivateRoute path="/banned-users">
            <BannedUsersPage />
          </PrivateRoute>

          <PrivateRoute path="/records">
            <RecordsPage />
          </PrivateRoute>

          <PrivateRoute path="/settings">
            <Settings />
          </PrivateRoute>

          <PrivateRoute path="/private-chats">
            <PrivateChats />
          </PrivateRoute>

          <PrivateRoute path="/private-chat/:userId">
            <PrivateChat />
          </PrivateRoute>

          <PrivateRoute path="/lucky-wheel">
            <LuckyWheel />
          </PrivateRoute>

          <PrivateRoute path="/lucky-wheel-admin">
            <LuckyWheelAdmin />
          </PrivateRoute>

          <PrivateRoute path="/reward-code-checker">
            <RewardCodeChecker />
          </PrivateRoute>

          <PrivateRoute path="/notifications">
            <Notifications />
          </PrivateRoute>

          <PrivateRoute path="/">
            <Home />
          </PrivateRoute>
        </Switch>
        <EnhancedBottomNavbar />
      </NotificationProvider>
    </ProfileProvider>
  );
}

export default App;
