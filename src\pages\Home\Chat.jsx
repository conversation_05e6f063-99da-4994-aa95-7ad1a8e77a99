import React from "react";
import { useParams } from "react-router";
import { Loader } from "rsuite";
import ChatBottom from "../../components/chat_window/bottom";
import Messages from "../../components/chat_window/messages";
import ChatTop from "../../components/chat_window/top";
import { CurrentRoomProvider } from "../../context/current-room.context";
import { ReplyProvider } from "../../context/reply.context";
import { useRooms } from "../../context/rooms.context";
import { auth } from "../../misc/firebase.config";
import { transformToArr } from "../../misc/helpers";
import AutoWarningMessage from "../../components/chat_window/messages/AutoWarningMessage";

const Chat = () => {
  const { chatId } = useParams();

  const rooms = useRooms();

  if (!rooms) {
    return <Loader center vertical size="md" content="Loading" speed="slow" />;
  }

  const currentRoom = rooms.find((room) => room.id === chatId);

  if (!currentRoom) {
    // التحقق مما إذا كانت الدردشة قيد الإنشاء
    const isCreatingChat = sessionStorage.getItem('creating_chat') === 'true';

    if (isCreatingChat) {
      // إذا كانت الدردشة قيد الإنشاء، عرض رسالة انتظار
      return (
        <div className="text-center mt-page">
          <Loader center vertical size="md" content="جاري إنشاء الدردشة..." speed="slow" />
          <p className="mt-3">يرجى الانتظار قليلاً...</p>
        </div>
      );
    }

    // إذا لم تكن الدردشة قيد الإنشاء، عرض رسالة الخطأ
    return <h6 className="text-center mt-page">Chat {chatId} not found</h6>;
  }

  const { name, description, isOwnerOnly, isPrivate, isDirectMessage, isSpecial, members, isRestored } = currentRoom;

  // استخدام الكائن الأصلي للمشرفين بدلاً من تحويله إلى مصفوفة
  const admins = currentRoom.admins || {};
  const isAdmin = admins[auth.currentUser.uid] === true;

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  const currentRoomData = {
    name,
    description,
    admins,
    isAdmin,
    isOwner, // إضافة خاصية isOwner
    isOwnerOnly: isOwnerOnly || false, // إضافة خاصية isOwnerOnly
    isPrivate: isPrivate || false, // إضافة خاصية isPrivate
    isDirectMessage: isDirectMessage || false, // إضافة خاصية isDirectMessage
    isSpecial: isSpecial || false, // إضافة خاصية isSpecial
    isRestored: isRestored || false, // إضافة خاصية isRestored
    members: members || {}, // إضافة أعضاء المجموعة
  };

  return (
    <CurrentRoomProvider data={currentRoomData}>
      <ReplyProvider>
        <div className="chat-top">
          <ChatTop />
        </div>

        <div className="chat-middle">
          <Messages />
          {/* إضافة مكون التحذير التلقائي للدردشات الخاصة بين طرفين */}
          <AutoWarningMessage isDirectMessage={currentRoomData.isDirectMessage} />
        </div>

        <div className="chat-bottom">
          <ChatBottom />
        </div>
      </ReplyProvider>
    </CurrentRoomProvider>
  );
};

export default Chat;
