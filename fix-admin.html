<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح حالة المشرف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #1DA1F2;
        }
        .info {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-right: 4px solid #1DA1F2;
        }
        .user-info {
            background-color: #3a3b3c;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .user-name {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .user-uid {
            font-family: monospace;
            color: #999;
            margin-bottom: 10px;
        }
        .user-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 5px;
        }
        .status-true {
            background-color: #4CAF50;
        }
        .status-false {
            background-color: #f44336;
        }
        button {
            width: 100%;
            padding: 15px;
            border-radius: 5px;
            border: none;
            background-color: #1DA1F2;
            color: white;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
            font-size: 16px;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #2d2d2d;
            display: none;
        }
        .success {
            border-right: 4px solid #4CAF50;
            color: #4CAF50;
        }
        .error {
            border-right: 4px solid #f44336;
            color: #f44336;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إصلاح حالة المشرف</h1>
        
        <div class="info">
            هذه الأداة تقوم بإصلاح حالة المشرف للمستخدم في قاعدة البيانات. سيتم تعيين قيمة <code>isAdmin</code> إلى <code>true</code> للمستخدم المحدد.
        </div>
        
        <div class="user-info">
            <div class="user-name" id="userName">Ala studio official</div>
            <div class="user-uid" id="userUid">HdgvqlLyeagYNofjTtYGefH2wjD3</div>
            <div class="user-status">
                حالة المشرف: <span id="adminStatus">جاري التحميل...</span>
                <div class="status-indicator" id="statusIndicator"></div>
            </div>
        </div>
        
        <button id="fixAdminBtn">تعيين كمشرف</button>
        <button id="checkStatusBtn">التحقق من الحالة</button>
        
        <div id="result" class="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const userNameElement = document.getElementById('userName');
        const userUidElement = document.getElementById('userUid');
        const adminStatusElement = document.getElementById('adminStatus');
        const statusIndicatorElement = document.getElementById('statusIndicator');
        const fixAdminBtn = document.getElementById('fixAdminBtn');
        const checkStatusBtn = document.getElementById('checkStatusBtn');
        const resultDiv = document.getElementById('result');
        
        // معرف المستخدم المراد تعيينه كمشرف
        const targetUid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';
        
        // التحقق من حالة المشرف
        async function checkAdminStatus() {
            try {
                const userRef = firebase.database().ref(`users/${targetUid}`);
                const snapshot = await userRef.once('value');
                
                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    const isAdmin = userData.isAdmin === true;
                    
                    adminStatusElement.textContent = isAdmin ? 'مشرف' : 'مستخدم عادي';
                    statusIndicatorElement.className = `status-indicator status-${isAdmin}`;
                    
                    // تحديث اسم المستخدم
                    userNameElement.textContent = userData.name || 'مستخدم غير معروف';
                    
                    return isAdmin;
                } else {
                    adminStatusElement.textContent = 'غير موجود';
                    statusIndicatorElement.className = 'status-indicator status-false';
                    showResult('لم يتم العثور على المستخدم في قاعدة البيانات', false);
                    return false;
                }
            } catch (error) {
                console.error('Error checking admin status:', error);
                adminStatusElement.textContent = 'خطأ';
                statusIndicatorElement.className = 'status-indicator status-false';
                showResult(`خطأ في التحقق من حالة المشرف: ${error.message}`, false);
                return false;
            }
        }
        
        // تعيين المستخدم كمشرف
        async function fixAdminStatus() {
            fixAdminBtn.disabled = true;
            fixAdminBtn.innerHTML = '<span class="loading"></span> جاري التعيين...';
            
            try {
                // تسجيل الدخول كمستخدم مجهول
                await firebase.auth().signInAnonymously();
                
                // تعيين المستخدم كمشرف
                await firebase.database().ref(`users/${targetUid}/isAdmin`).set(true);
                
                // التحقق من نجاح العملية
                const isAdmin = await checkAdminStatus();
                
                if (isAdmin) {
                    showResult('تم تعيين المستخدم كمشرف بنجاح', true);
                } else {
                    showResult('فشل في تعيين المستخدم كمشرف', false);
                }
            } catch (error) {
                console.error('Error fixing admin status:', error);
                showResult(`خطأ في تعيين المستخدم كمشرف: ${error.message}`, false);
            } finally {
                fixAdminBtn.disabled = false;
                fixAdminBtn.textContent = 'تعيين كمشرف';
            }
        }
        
        // عرض نتيجة العملية
        function showResult(message, isSuccess) {
            resultDiv.textContent = message;
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            resultDiv.style.display = 'block';
        }
        
        // إضافة مستمعي الأحداث للأزرار
        fixAdminBtn.addEventListener('click', fixAdminStatus);
        checkStatusBtn.addEventListener('click', checkAdminStatus);
        
        // التحقق من حالة المشرف عند تحميل الصفحة
        checkAdminStatus();
    </script>
</body>
</html>
