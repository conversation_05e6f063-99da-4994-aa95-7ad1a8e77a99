import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/firebase_service.dart';
import '../widgets/loading_indicator.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  WebViewController? _controller;
  bool _isLoading = true;
  final String _websiteUrl = 'https://toika-3.web.app';

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  // Initialize WebView
  void _initWebView() {
    // Create WebView controller
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(Colors.black)
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                debugPrint('WebView is loading (progress: $progress%)');
              },
              onPageStarted: (String url) {
                debugPrint('Page started loading: $url');
                setState(() {
                  _isLoading = true;
                });
              },
              onPageFinished: (String url) {
                debugPrint('Page finished loading: $url');
                _injectJavaScript();
                Future.delayed(const Duration(milliseconds: 300), () {
                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });
                  }
                });
              },
              onWebResourceError: (WebResourceError error) {
                debugPrint('''
              Page resource error:
              code: ${error.errorCode}
              description: ${error.description}
              errorType: ${error.errorType}
              isForMainFrame: ${error.isForMainFrame}
            ''');
              },
              onNavigationRequest: (NavigationRequest request) {
                if (request.url.startsWith('https://toika-3.web.app')) {
                  return NavigationDecision.navigate;
                }
                _launchExternalUrl(request.url);
                return NavigationDecision.prevent;
              },
            ),
          )
          ..addJavaScriptChannel(
            'Flutter',
            onMessageReceived: (JavaScriptMessage message) {
              _handleJavaScriptMessage(message.message);
            },
          )
          ..loadRequest(Uri.parse(_websiteUrl));
  }

  // Inject JavaScript to communicate with the web app
  void _injectJavaScript() {
    if (_controller == null) return;

    const String script = '''
      // Function to get user ID from the page
      function getUserId() {
        try {
          // Try to get user ID from Firebase Auth
          if (window.firebase && firebase.auth && firebase.auth().currentUser) {
            return firebase.auth().currentUser.uid;
          }

          // Try to get from localStorage
          if (localStorage.getItem('user_id')) {
            return localStorage.getItem('user_id');
          }

          return null;
        } catch (e) {
          console.error('Error getting user ID:', e);
          return null;
        }
      }

      // Function to send user ID to Flutter
      function sendUserIdToFlutter() {
        const userId = getUserId();
        if (userId) {
          Flutter.postMessage(JSON.stringify({
            type: 'userId',
            data: { userId: userId }
          }));
        }

        // Try again in 5 seconds if no user ID found
        if (!userId) {
          setTimeout(sendUserIdToFlutter, 5000);
        }
      }

      // Listen for reply notifications
      document.addEventListener('replyNotification', function(event) {
        // Send notification data to Flutter
        Flutter.postMessage(JSON.stringify({
          type: 'replyNotification',
          data: event.detail
        }));
      });

      // Send user ID to Flutter immediately
      sendUserIdToFlutter();

      // Check again every 30 seconds
      setInterval(sendUserIdToFlutter, 30000);
    ''';

    _controller!.runJavaScript(script);
  }

  // Handle messages from JavaScript
  void _handleJavaScriptMessage(String message) {
    try {
      final Map<String, dynamic> data = jsonDecode(message);

      // Handle different message types
      switch (data['type']) {
        case 'userId':
          final userId = data['data']['userId'];
          debugPrint('Received user ID: $userId');
          _handleUserId(userId);
          break;
        case 'replyNotification':
          debugPrint('Received reply notification: ${data['data']}');
          break;
        default:
          debugPrint('Received unknown message type: ${data['type']}');
      }
    } catch (e) {
      debugPrint('Error handling JavaScript message: $e');
    }
  }

  // Handle user ID from JavaScript
  Future<void> _handleUserId(String userId) async {
    try {
      // Save user ID to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_id', userId);

      // Get FCM token
      final token = prefs.getString('fcm_token');

      // Register token with server if available
      if (token != null) {
        await FirebaseService.registerTokenWithServer(userId, token);
      }
    } catch (e) {
      debugPrint('Error handling user ID: $e');
    }
  }

  // Launch external URL
  Future<void> _launchExternalUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  // Handle back button press
  Future<bool> _handleBackButton() async {
    if (_controller == null) return true;

    final canGoBack = await _controller!.canGoBack();
    if (canGoBack) {
      await _controller!.goBack();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        final canPop = await _handleBackButton();
        if (canPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Stack(
            children: [
              // WebView
              WebViewWidget(controller: _controller!),

              // Loading indicator
              if (_isLoading) const LoadingIndicator(),
            ],
          ),
        ),
      ),
    );
  }
}
