<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>التحقق من حالة المشرف (مع المصادقة)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 600px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #1DA1F2;
            margin-bottom: 20px;
        }
        .login-form {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #3a3b3c;
            background-color: #3a3b3c;
            color: #e4e6eb;
            box-sizing: border-box;
        }
        .user-info {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .user-name {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .user-uid {
            font-family: monospace;
            color: #999;
            margin-bottom: 10px;
        }
        .status-section {
            background-color: #3a3b3c;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #4a4a4a;
        }
        .status-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .status-label {
            font-weight: bold;
        }
        .status-value {
            display: flex;
            align-items: center;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 5px;
        }
        .status-true {
            background-color: #4CAF50;
        }
        .status-false {
            background-color: #f44336;
        }
        .room-list {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .room-item {
            background-color: #3a3b3c;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .room-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .room-id {
            font-family: monospace;
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }
        .room-admin-status {
            display: flex;
            align-items: center;
        }
        button {
            background-color: #1DA1F2;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 10px;
            width: 100%;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
        .log {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #1DA1F2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>التحقق من حالة المشرف (مع المصادقة)</h1>
        
        <div class="login-form" id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password">
            </div>
            <button id="loginBtn">تسجيل الدخول</button>
        </div>
        
        <div class="user-info" id="userInfo">
            <div class="user-name" id="userName">جاري التحميل...</div>
            <div class="user-uid" id="userUid">HdgvqlLyeagYNofjTtYGefH2wjD3</div>
        </div>
        
        <div class="status-section" id="statusSection">
            <h3>حالة المشرف في النظام</h3>
            <div class="status-item">
                <div class="status-label">مشرف نظام (isAdmin):</div>
                <div class="status-value" id="systemAdminStatus">
                    جاري التحميل...
                    <div class="status-indicator" id="systemAdminIndicator"></div>
                </div>
            </div>
        </div>
        
        <div class="room-list" id="roomList">
            <h3>حالة المشرف في الغرف</h3>
            <div id="roomsContainer">جاري تحميل الغرف...</div>
        </div>
        
        <button id="fixRoomAdminBtn" style="display: none;">تعيين كمشرف في جميع الغرف</button>
        
        <div class="log" id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "300804286264",
            appId: "1:300804286264:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const loginForm = document.getElementById('loginForm');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const userInfo = document.getElementById('userInfo');
        const userNameElement = document.getElementById('userName');
        const userUidElement = document.getElementById('userUid');
        const statusSection = document.getElementById('statusSection');
        const systemAdminStatusElement = document.getElementById('systemAdminStatus');
        const systemAdminIndicatorElement = document.getElementById('systemAdminIndicator');
        const roomList = document.getElementById('roomList');
        const roomsContainerElement = document.getElementById('roomsContainer');
        const fixRoomAdminBtn = document.getElementById('fixRoomAdminBtn');
        const logElement = document.getElementById('log');
        
        // معرف المستخدم المراد التحقق منه
        const targetUid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';
        
        // إضافة رسالة إلى السجل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // تسجيل الدخول
        loginBtn.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            
            if (!email || !password) {
                log('الرجاء إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري تسجيل الدخول...';
            
            try {
                // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
                await firebase.auth().signInWithEmailAndPassword(email, password);
                log(`تم تسجيل الدخول بنجاح كـ ${email}`, 'success');
                
                // إخفاء نموذج تسجيل الدخول وإظهار معلومات المستخدم
                loginForm.style.display = 'none';
                userInfo.style.display = 'block';
                statusSection.style.display = 'block';
                roomList.style.display = 'block';
                fixRoomAdminBtn.style.display = 'block';
                
                // التحقق من حالة المشرف
                await checkSystemAdminStatus();
                await checkRoomAdminStatus();
            } catch (error) {
                log(`خطأ في تسجيل الدخول: ${error.message}`, 'error');
                loginBtn.disabled = false;
                loginBtn.textContent = 'تسجيل الدخول';
            }
        });
        
        // التحقق من حالة المشرف في النظام
        async function checkSystemAdminStatus() {
            try {
                log(`التحقق من حالة المشرف في النظام للمستخدم ${targetUid}...`);
                
                // التحقق من وجود المستخدم
                const userRef = firebase.database().ref(`users/${targetUid}`);
                const snapshot = await userRef.once('value');
                
                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    const userName = userData.name || 'مستخدم غير معروف';
                    const isAdmin = userData.isAdmin === true;
                    
                    // تحديث معلومات المستخدم
                    userNameElement.textContent = userName;
                    
                    // تحديث حالة المشرف في النظام
                    systemAdminStatusElement.textContent = isAdmin ? 'مشرف' : 'مستخدم عادي';
                    systemAdminIndicatorElement.className = `status-indicator status-${isAdmin}`;
                    
                    log(`اسم المستخدم: ${userName}`, 'info');
                    log(`حالة المشرف في النظام: ${isAdmin ? 'مشرف' : 'مستخدم عادي'}`, isAdmin ? 'success' : 'info');
                    
                    return { userData, isAdmin };
                } else {
                    log(`لم يتم العثور على المستخدم ${targetUid}`, 'error');
                    return { userData: null, isAdmin: false };
                }
            } catch (error) {
                console.error('Error checking system admin status:', error);
                log(`خطأ في التحقق من حالة المشرف في النظام: ${error.message}`, 'error');
                return { userData: null, isAdmin: false };
            }
        }
        
        // التحقق من حالة المشرف في الغرف
        async function checkRoomAdminStatus() {
            try {
                log(`التحقق من حالة المشرف في الغرف للمستخدم ${targetUid}...`);
                
                // الحصول على جميع الغرف
                const roomsRef = firebase.database().ref('rooms');
                const snapshot = await roomsRef.once('value');
                
                if (snapshot.exists()) {
                    const rooms = snapshot.val();
                    const roomsArray = [];
                    
                    // تحويل الغرف إلى مصفوفة
                    for (const [roomId, roomData] of Object.entries(rooms)) {
                        const isRoomAdmin = roomData.admins && roomData.admins[targetUid] === true;
                        
                        roomsArray.push({
                            id: roomId,
                            name: roomData.name || 'غرفة بدون اسم',
                            isAdmin: isRoomAdmin
                        });
                        
                        log(`الغرفة: ${roomData.name || 'غرفة بدون اسم'} - حالة المشرف: ${isRoomAdmin ? 'مشرف' : 'مستخدم عادي'}`, isRoomAdmin ? 'success' : 'info');
                    }
                    
                    // عرض الغرف
                    if (roomsArray.length > 0) {
                        roomsContainerElement.innerHTML = '';
                        
                        roomsArray.forEach(room => {
                            const roomElement = document.createElement('div');
                            roomElement.className = 'room-item';
                            roomElement.innerHTML = `
                                <div class="room-name">${room.name}</div>
                                <div class="room-id">${room.id}</div>
                                <div class="room-admin-status">
                                    <div>حالة المشرف: ${room.isAdmin ? 'مشرف' : 'مستخدم عادي'}</div>
                                    <div class="status-indicator status-${room.isAdmin}"></div>
                                </div>
                            `;
                            roomsContainerElement.appendChild(roomElement);
                        });
                    } else {
                        roomsContainerElement.textContent = 'لا توجد غرف';
                    }
                    
                    return roomsArray;
                } else {
                    log('لا توجد غرف', 'info');
                    roomsContainerElement.textContent = 'لا توجد غرف';
                    return [];
                }
            } catch (error) {
                console.error('Error checking room admin status:', error);
                log(`خطأ في التحقق من حالة المشرف في الغرف: ${error.message}`, 'error');
                return [];
            }
        }
        
        // تعيين المستخدم كمشرف في جميع الغرف
        async function fixRoomAdminStatus() {
            try {
                log(`تعيين المستخدم ${targetUid} كمشرف في جميع الغرف...`);
                
                // الحصول على جميع الغرف
                const roomsRef = firebase.database().ref('rooms');
                const snapshot = await roomsRef.once('value');
                
                if (snapshot.exists()) {
                    const rooms = snapshot.val();
                    let successCount = 0;
                    
                    // تعيين المستخدم كمشرف في كل غرفة
                    for (const [roomId, roomData] of Object.entries(rooms)) {
                        try {
                            // تحديث حالة المشرف في الغرفة
                            await firebase.database().ref(`rooms/${roomId}/admins/${targetUid}`).set(true);
                            
                            log(`تم تعيين المستخدم كمشرف في الغرفة: ${roomData.name || 'غرفة بدون اسم'}`, 'success');
                            successCount++;
                        } catch (roomError) {
                            log(`خطأ في تعيين المستخدم كمشرف في الغرفة ${roomData.name || 'غرفة بدون اسم'}: ${roomError.message}`, 'error');
                        }
                    }
                    
                    log(`تم تعيين المستخدم كمشرف في ${successCount} غرفة من أصل ${Object.keys(rooms).length} غرفة`, 'success');
                    
                    // تحديث حالة المشرف في الغرف
                    await checkRoomAdminStatus();
                } else {
                    log('لا توجد غرف لتعيين المستخدم كمشرف فيها', 'info');
                }
            } catch (error) {
                console.error('Error fixing room admin status:', error);
                log(`خطأ في تعيين المستخدم كمشرف في الغرف: ${error.message}`, 'error');
            }
        }
        
        // إضافة مستمع حدث للزر
        fixRoomAdminBtn.addEventListener('click', fixRoomAdminStatus);
        
        // التحقق من حالة المصادقة عند تحميل الصفحة
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                // المستخدم مسجل الدخول بالفعل
                log(`تم تسجيل الدخول تلقائيًا كـ ${user.email}`, 'success');
                
                // إخفاء نموذج تسجيل الدخول وإظهار معلومات المستخدم
                loginForm.style.display = 'none';
                userInfo.style.display = 'block';
                statusSection.style.display = 'block';
                roomList.style.display = 'block';
                fixRoomAdminBtn.style.display = 'block';
                
                // التحقق من حالة المشرف
                checkSystemAdminStatus();
                checkRoomAdminStatus();
            }
        });
    </script>
</body>
</html>
