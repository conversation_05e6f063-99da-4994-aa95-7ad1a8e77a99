import React, { useState, useRef } from 'react';
import { Modal, Button, Form, Message, toaster, Uploader, Schema } from 'rsuite';
import { ref, push, update, serverTimestamp } from 'firebase/database';
import { database } from '../../../misc/firebase.config';
import { useParams } from 'react-router';
import { useProfile } from '../../../context/profile.context';
import AvatarEditor from 'react-avatar-editor';

const { StringType } = Schema.Types;

// تعريف نموذج التحقق من صحة البيانات
const model = Schema.Model({
  description: StringType().isRequired('الوصف مطلوب'),
  targetUid: StringType().isRequired('معرف المستخدم مطلوب')
});

const SpecialMessageModal = ({ open, onClose }) => {
  const { chatId } = useParams();
  const { profile } = useProfile();
  const [formValue, setFormValue] = useState({
    description: '',
    targetUid: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [fileInfo, setFileInfo] = useState(null);
  const [img, setImg] = useState(null);
  const avatarEditorRef = useRef();
  const formRef = useRef();

  // معالجة تغيير قيم النموذج
  const handleFormChange = (formValue) => {
    setFormValue(formValue);
  };

  // معالجة تحميل الصورة
  const handleFileChange = (fileList) => {
    const file = fileList[0];
    if (file) {
      setFileInfo(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImg(reader.result);
      };
      reader.readAsDataURL(file.blobFile);
    }
  };

  // معالجة إرسال النموذج
  const handleSubmit = async () => {
    if (!formRef.current.check()) {
      return;
    }

    if (!img) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          الرجاء اختيار صورة
        </Message>
      );
      return;
    }

    setIsLoading(true);

    try {
      // 1. تحويل الصورة إلى Data URL
      const canvas = avatarEditorRef.current.getImageScaledToCanvas();
      // استخدام صيغة JPEG بدلاً من PNG لتجنب مشاكل العرض
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9); // تحويل الصورة إلى سلسلة نصية بصيغة base64 بجودة 90%

      // 2. إنشاء رسالة خاصة في قاعدة البيانات
      // إذا كان المستخدم هو المالك، نخفي معلوماته
      const isOwner = profile.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
                      profile.email === '<EMAIL>';

      const messageData = {
        roomId: chatId,
        author: {
          // إذا كان المستخدم هو المالك، نستخدم معلومات خاصة
          name: isOwner ? "إدارة الموقع" : profile.name,
          uid: profile.uid,
          email: profile.email,
          createdAt: profile.createdAt,
          ...(isOwner ? {} : (profile.avatar ? { avatar: profile.avatar } : {}))
        },
        createdAt: serverTimestamp(),
        likeCount: 0,
        isSpecialMessage: true,
        specialData: {
          imageUrl: imageDataUrl, // استخدام Data URL بدلاً من رابط Firebase Storage
          description: formValue.description,
          targetUid: formValue.targetUid
        }
      };

      // 3. إضافة الرسالة إلى قاعدة البيانات
      const updates = {};
      const messageId = push(ref(database, "messages")).key;

      updates[`/messages/${messageId}`] = messageData;
      updates[`/rooms/${chatId}/lastMessage`] = {
        ...messageData,
        msgId: messageId
      };

      await update(ref(database), updates);

      // 4. إعادة تعيين النموذج وإغلاق النافذة
      setFormValue({
        description: '',
        targetUid: ''
      });
      setFileInfo(null);
      setImg(null);
      onClose();

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم إرسال الرسالة الخاصة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error sending special message:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء إرسال الرسالة: {error.message}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} className="dark-modal special-message-modal">
      <Modal.Header className="dark-modal-header">
        <Modal.Title className="dark-modal-title">إرسال رسالة خاصة</Modal.Title>
      </Modal.Header>
      <Modal.Body className="dark-modal-body">
        <Form
          ref={formRef}
          fluid
          onChange={handleFormChange}
          formValue={formValue}
          model={model}
        >
          <div className="text-center mb-3">
            {img ? (
              <AvatarEditor
                ref={avatarEditorRef}
                image={img}
                width={200}
                height={200}
                border={10}
                borderRadius={0}
                scale={1.2}
                className="special-image-editor"
              />
            ) : (
              <div className="special-image-placeholder">
                <i className="fa fa-image fa-3x"></i>
                <p>اختر صورة</p>
              </div>
            )}
          </div>

          <Form.Group>
            <Form.ControlLabel>الصورة</Form.ControlLabel>
            <Uploader
              listType="picture"
              autoUpload={false}
              action=""
              fileList={fileInfo ? [fileInfo] : []}
              onChange={handleFileChange}
              draggable
              className="special-uploader"
            >
              <div style={{ lineHeight: '100px' }}>
                اسحب الصورة هنا، أو انقر لاختيار صورة
              </div>
            </Uploader>
          </Form.Group>

          <Form.Group>
            <Form.ControlLabel>وصف الصورة</Form.ControlLabel>
            <Form.Control
              name="description"
              placeholder="أدخل وصفاً للصورة"
              className="special-input"
            />
          </Form.Group>

          <Form.Group>
            <Form.ControlLabel>معرف المستخدم (UID)</Form.ControlLabel>
            <Form.Control
              name="targetUid"
              placeholder="أدخل معرف المستخدم المستهدف"
              className="special-input"
              dir="ltr"
            />
            <Form.HelpText>
              يمكنك الحصول على معرف المستخدم من صفحة الملف الشخصي
            </Form.HelpText>
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer className="dark-modal-footer">
        <Button
          appearance="primary"
          onClick={handleSubmit}
          disabled={isLoading}
          block
        >
          {isLoading ? 'جاري الإرسال...' : 'إرسال'}
        </Button>
        <Button
          appearance="subtle"
          onClick={onClose}
          disabled={isLoading}
          block
        >
          إلغاء
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SpecialMessageModal;
