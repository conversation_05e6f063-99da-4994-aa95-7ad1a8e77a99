rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // السماح للمستخدمين المسجلين بقراءة وكتابة الملفات في مجلد المستخدم الخاص بهم فقط
    match /users/{userId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // السماح للمستخدمين المسجلين بقراءة وكتابة الملفات في مجلد الدردشة
    match /chat/{chatId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // قواعد عامة - منع الوصول الافتراضي
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
