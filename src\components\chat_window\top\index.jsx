import React, { memo, useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useCurrentRoom } from "../../../context/current-room.context";
import ArowBackIcon from '@rsuite/icons/ArowBack';
import { useMediaQuery } from "../../../misc/custom-hooks";
import RoomInfoBtnModal from "./RoomInfoBtnModal";
import SpecialModeToggle from "./SpecialModeToggle";
import RoomUsersCount from "./RoomUsersCount";
import ChatCloseToggle from "./ChatCloseToggle";
import { database, auth } from "../../../misc/firebase.config";
import { ref, get } from "firebase/database";

const ChatTop = () => {
  const name = useCurrentRoom((v) => v.name);
  const isMobile = useMediaQuery("(max-width: 992px)");
  const isAdmin = useCurrentRoom(v => v.isAdmin);
  const isDirectMessage = useCurrentRoom(v => v.isDirectMessage);
  const members = useCurrentRoom(v => v.members);
  const { chatId } = useParams();
  const [displayName, setDisplayName] = useState(name);

  // إذا كانت المجموعة هي دردشة خاصة بين شخصين، احصل على اسم المستخدم الآخر
  useEffect(() => {
    if (isDirectMessage && members && chatId) {
      const currentUserId = auth.currentUser.uid;

      // احصل على معرف المستخدم الآخر
      const otherUserId = Object.keys(members).find(id => id !== currentUserId);

      if (otherUserId) {
        // احصل على بيانات المستخدم الآخر
        const userRef = ref(database, `users/${otherUserId}`);
        get(userRef).then(snapshot => {
          if (snapshot.exists()) {
            const userData = snapshot.val();
            setDisplayName(userData.name);
          }
        }).catch(error => {
          console.error('Error fetching other user data:', error);
        });
      }
    } else {
      setDisplayName(name);
    }
  }, [chatId, isDirectMessage, members, name]);

  // التحقق مما إذا كان المستخدم هو المالك
  const [isOwner, setIsOwner] = useState(false);

  useEffect(() => {
    if (auth.currentUser) {
      const currentUserId = auth.currentUser.uid;
      const isOwnerUser = currentUserId === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
                         auth.currentUser.email === '<EMAIL>';
      setIsOwner(isOwnerUser);
    }
  }, []);





  return (
    <div className="chat-top-container">
      <div className="d-flex justify-content-between align-items-center">
        <div className="d-flex align-items-center">
          <Link to={"/"} className="link-unstyled back-button">
            <ArowBackIcon
              className="d-inline-block p-0 mr-2 text-blue"
            />
          </Link>
          <span className="room-name-display">{displayName}</span>
        </div>

        <div className="d-flex align-items-center">
          {!isDirectMessage && <RoomUsersCount />}
          {/* إضافة الأزرار مباشرة */}
          {isOwner && <SpecialModeToggle />}
          <ChatCloseToggle />
          <RoomInfoBtnModal />
        </div>
      </div>
    </div>
  );
};

export default memo(ChatTop);
