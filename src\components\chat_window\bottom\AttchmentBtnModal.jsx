import React, { useState } from "react";
import { useParams } from "react-router";
import { Button, InputGroup, Message, Modal, toaster, Uploader } from "rsuite";
import { useModalState } from "../../../misc/custom-hooks";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { storage, database } from "../../../misc/firebase.config";
import { useCurrentRoom } from "../../../context/current-room.context";
import { ref as dbRef, get as dbGet } from "firebase/database";

const MAX_FILE_SIZE = 1000 * 1024 * 5;

const AttchmentBtnModal = ({ afterUpload }) => {
  const { chatId } = useParams();
  const { isOpen, open, close } = useModalState();
  const isAdmin = useCurrentRoom(v => v.isAdmin);

  const [fileList, setFileList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [, setIsPrivateRoom] = useState(false);
  const [canUploadImages, setCanUploadImages] = useState(false);

  // التحقق من نوع الغرفة وصلاحيات المستخدم
  React.useEffect(() => {
    const checkRoomType = async () => {
      try {
        const roomRef = dbRef(database, `/rooms/${chatId}`);
        const roomSnapshot = await dbGet(roomRef);

        if (roomSnapshot.exists()) {
          const roomData = roomSnapshot.val();
          const isPrivate = roomData.isPrivate || false;
          const isDirectMessage = roomData.isDirectMessage || false;

          setIsPrivateRoom(isPrivate);

          // يمكن للمستخدم رفع الصور في الحالات التالية:
          // 1. إذا كان مشرفاً (في أي نوع من الغرف)
          // 2. إذا كانت الغرفة خاصة (لجميع المستخدمين)
          // 3. إذا كانت الغرفة دردشة خاصة بين شخصين (لجميع المستخدمين)
          setCanUploadImages(isAdmin || isPrivate || isDirectMessage);
        }
      } catch (error) {
        console.error("Error checking room type:", error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في التحقق من نوع الغرفة: {error.message}
          </Message>
        );
      }
    };

    checkRoomType();
  }, [chatId, isAdmin]);

  const onChange = (fileArr) => {
    const filtered = fileArr
      .filter((el) => el.blobFile.size <= MAX_FILE_SIZE)
      .slice(0, 5);

    setFileList(filtered);
  };

  const onUpload = async () => {
    setIsLoading(true);
    try {
      const uploadPromises = fileList.map((f) => {
        // إنشاء اسم فريد للملف باستخدام الوقت الحالي واسم الملف
        const fileName = `${Date.now()}_${Math.floor(Math.random() * 1000)}_${f.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;

        // طباعة معلومات التخزين للتشخيص
        console.log('Storage reference:', storage);
        console.log('Storage bucket:', storage.app.options.storageBucket);
        console.log('Uploading file to:', `/chat/${chatId}/${fileName}`);

        // رفع الملف إلى مجلد الدردشة
        try {
          return uploadBytes(
            ref(storage, `/chat/${chatId}/${fileName}`),
            f.blobFile,
            {
              cacheControl: `public, max-age=${3600 * 24 * 3}`,
              contentType: f.blobFile.type, // تحديد نوع المحتوى بشكل صريح
            }
          );
        } catch (uploadError) {
          console.error('Error during uploadBytes:', uploadError);
          throw uploadError;
        }
      });

      const uploadSnapshots = await Promise.all(uploadPromises);
      console.log('تم رفع الملفات بنجاح:', uploadSnapshots);

      const shapePromises = uploadSnapshots.map(async (snap) => {
        try {
          const downloadUrl = await getDownloadURL(snap.ref);
          return {
            contentType: snap.metadata.contentType,
            name: snap.metadata.name,
            url: downloadUrl,
          };
        } catch (error) {
          console.error('خطأ في الحصول على رابط التنزيل:', error);
          throw error;
        }
      });

      const files = await Promise.all(shapePromises);
      console.log('تم تجهيز الملفات للإرسال:', files);

      await afterUpload(files);
      console.log('تم إرسال الملفات إلى الدردشة بنجاح');

      setIsLoading(false);
      close();

      // إظهار رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم رفع الصور بنجاح
        </Message>
      );
    } catch (err) {
      console.error('خطأ أثناء رفع الصور:', err);
      setIsLoading(false);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء رفع الصورة: {err.message}
        </Message>
      );
    }
  };

  return (
    <>
      {/* عرض زر رفع الملفات فقط إذا كان المستخدم مشرفاً أو إذا كانت الغرفة خاصة */}
      {canUploadImages && (
        <InputGroup.Button onClick={open} className="attachment-btn">
          <i className="fa-solid fa-image"></i>
        </InputGroup.Button>
      )}

      <Modal open={isOpen} onClose={close} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">رفع صور</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <Uploader
            autoUpload={false}
            action=""
            fileList={fileList}
            onChange={onChange}
            multiple
            listType="picture-text"
            className="w-100"
            disabled={isLoading}
            accept="image/*"
          />
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button
            block
            appearance="primary"
            disabled={isLoading}
            onClick={onUpload}
            className="dark-upload-btn"
          >
            {isLoading ? 'جاري الرفع...' : 'إرسال إلى الدردشة'}
          </Button>
          <div className="text-center mt-2">
            <small>* يسمح فقط بالملفات التي حجمها أقل من 5 ميجابايت</small>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default AttchmentBtnModal;
