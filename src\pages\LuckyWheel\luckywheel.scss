.lucky-wheel-page {
  padding: 15px;
  min-height: 100vh;
  background-color: #121212;
  color: #e4e6eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 80px; // للتأكد من عدم تداخل المحتوى مع شريط التنقل السفلي
  overflow-x: hidden; // منع التمرير الأفقي
}

.lucky-wheel-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 0 5px;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
    color: #e4e6eb;
  }

  .back-button {
    color: #e4e6eb;
    background-color: transparent;
    border: none;
    padding: 8px 12px;

    &:hover, &:focus {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.lucky-wheel-content {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10px;
}

.tickets-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  width: fit-content;

  i {
    color: #ffc107;
    margin-left: 8px;
    font-size: 16px;
  }

  span {
    font-size: 15px;
    font-weight: bold;
  }
}

.wheel-container {
  position: relative;
  width: calc(100vw - 40px); // تكيف مع عرض الشاشة
  max-width: 300px; // الحد الأقصى للعرض
  height: auto; // الارتفاع سيتم تحديده تلقائيًا للحفاظ على النسبة
  aspect-ratio: 1/1; // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
  margin: 15px auto;
  transition: transform 5s cubic-bezier(0.33, 1, 0.68, 1);
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  border: 4px solid #fff;
  overflow: visible;
  background-color: transparent;
  cursor: pointer;

  &:hover {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
  }

  &:before {
    content: '';
    display: block;
    padding-top: 100%; // للحفاظ على نسبة العرض إلى الارتفاع 1:1
  }
}

.wheel-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  background-color: transparent;
}

.spin-button {
  margin-top: 20px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;

  &:disabled {
    opacity: 0.9;
  }

  &.disabled-button {
    background-color: #4a4a4a !important;
    color: #ffffff !important;
    border-color: #4a4a4a !important;
    cursor: not-allowed;

    &:hover, &:focus {
      background-color: #4a4a4a !important;
    }
  }
}

.daily-tasks {
  width: 100%;
  margin-top: 10px;
}

.task-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;

  &.completed {
    background-color: rgba(76, 175, 80, 0.1);
    border-right: 4px solid #4caf50;
  }

  .task-content {
    flex: 1;

    h4 {
      margin: 0 0 5px 0;
      font-size: 16px;
      font-weight: bold;
    }

    p {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #b0b3b8;
    }

    .task-reward {
      display: flex;
      align-items: center;

      i {
        color: #ffc107;
        margin-left: 5px;
      }

      span {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }

  .task-button {
    margin-right: 10px;

    &:disabled {
      background-color: #4caf50;
      color: white;
    }
  }
}

.reward-modal {
  .rs-modal-content {
    background-color: #1e1e1e;
    color: #e4e6eb;
    border-radius: 10px;
  }

  .rs-modal-header {
    border-bottom: 1px solid #3a3b3c;
  }

  .rs-modal-title {
    color: #e4e6eb;
    font-size: 20px;
    font-weight: bold;
  }

  .rs-modal-footer {
    border-top: 1px solid #3a3b3c;
  }

  .reward-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .reward-icon {
      font-size: 50px;
      color: #ffc107;
      margin-bottom: 20px;
    }

    h3 {
      margin-bottom: 20px;
      font-size: 18px;
    }

    .reward-code {
      width: 100%;

      p {
        margin-bottom: 10px;
      }

      .code-box {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 20px;
        font-weight: bold;
        letter-spacing: 2px;
        margin-bottom: 10px;
      }

      .code-hint {
        font-size: 12px;
        color: #b0b3b8;
      }
    }
  }
}

// تنسيقات لصفحة إدارة العجلة
.wheel-admin-page {
  padding: 20px;
  min-height: 100vh;
  background-color: #121212;
  color: #e4e6eb;

  .admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
    }
  }

  .admin-section {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .reward-item {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;

    .color-preview {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-left: 10px;
    }

    .reward-details {
      flex: 1;

      .reward-text {
        font-weight: bold;
      }

      .reward-probability {
        font-size: 12px;
        color: #b0b3b8;
      }
    }

    .reward-actions {
      display: flex;

      button {
        margin-right: 5px;
      }
    }
  }

  .task-item {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;

    .task-details {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .task-info {
        flex: 1;

        .task-title {
          font-weight: bold;
        }

        .task-description {
          font-size: 12px;
          color: #b0b3b8;
        }

        .task-reward {
          display: flex;
          align-items: center;

          i {
            color: #ffc107;
            margin-left: 5px;
          }
        }
      }

      .task-actions {
        display: flex;

        button {
          margin-right: 5px;
        }
      }
    }
  }
}
