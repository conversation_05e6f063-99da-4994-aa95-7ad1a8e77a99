import { onAuthStateChanged } from "firebase/auth";
import {
  off,
  onValue,
  ref,
  serverTimestamp,
  onDisconnect,
  set,
  get,
  query,
  orderByChild,
  equalTo,
  push,
} from "firebase/database";
import React, { createContext, useContext, useEffect, useState } from "react";
import { auth, database } from "../misc/firebase.config";

// تحديد بريد المشرف
const ADMIN_EMAIL = "<EMAIL>";

// دالة لإنشاء غرف الدردشة الافتراضية
const createDefaultRooms = async (adminUid) => {
  try {
    // التحقق من وجود غرف دردشة
    const roomsRef = ref(database, "rooms");
    const roomsSnapshot = await get(roomsRef);

    // إذا لم تكن هناك غرف دردشة، قم بإنشاء الغرف الافتراضية
    if (!roomsSnapshot.exists()) {
      // إنشاء غرفة "كروب عرب"
      await push(roomsRef, {
        name: "كروب عرب",
        description: "غرفة دردشة للمستخدمين العرب",
        createdAt: serverTimestamp(),
        admins: {
          [adminUid]: true,
        },
        fcmUsers: {
          [adminUid]: true,
        },
      });

      // إنشاء غرفة "كروب كردي"
      await push(roomsRef, {
        name: "كروب كردي",
        description: "غرفة دردشة للمستخدمين الأكراد",
        createdAt: serverTimestamp(),
        admins: {
          [adminUid]: true,
        },
        fcmUsers: {
          [adminUid]: true,
        },
      });

      console.log("تم إنشاء الغرف الافتراضية بنجاح");
    }
  } catch (error) {
    console.error("خطأ في إنشاء الغرف الافتراضية:", error);
  }
};

export const isOfflineForDatabase = {
  state: "offline",
  last_changed: serverTimestamp(),
};

const isOnlineForDatabase = {
  state: "online",
  last_changed: serverTimestamp(),
};

const ProfileContext = createContext();

export const ProfileProvider = ({ children }) => {
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let userRef;
    let userStatusRef;

    const authUnsub = onAuthStateChanged(auth, async (authObj) => {
      console.log('Auth state changed:', authObj ? `User: ${authObj.email}` : 'No user');

      if (authObj) {
        // تخزين حالة المصادقة في التخزين المحلي
        localStorage.setItem('authState', 'authenticated');

        userStatusRef = ref(database, `/status/${authObj.uid}`);
        userRef = ref(database, `/users/${authObj.uid}`);

        // تسجيل حالة الاتصال للمستخدم
        const connectedRef = ref(database, '.info/connected');
        onValue(connectedRef, (snap) => {
          if (snap.val() === true) {
            console.log('User is online, setting status');
            // عند الاتصال، قم بتعيين حالة المستخدم كمتصل
            set(userStatusRef, isOnlineForDatabase);

            // عند قطع الاتصال، قم بتعيين حالة المستخدم كغير متصل
            onDisconnect(userStatusRef).set(isOfflineForDatabase);
          }
        });

        // التحقق من حظر المستخدم
        const bannedRef = ref(database, `/banned-users/${authObj.uid}`);
        const bannedSnapshot = await get(bannedRef);

        if (bannedSnapshot.exists()) {
          console.log('User is banned, signing out');
          // المستخدم محظور، قم بتسجيل الخروج
          localStorage.removeItem('authState');
          auth.signOut();
          setProfile(null);
          setIsLoading(false);
          return;
        }



        // استخدام get بدلاً من onValue للتأكد من تحميل البيانات مرة واحدة
        const userSnapshot = await get(userRef);

        if (userSnapshot.exists()) {
          const { name, createdAt, avatar, isAdmin } = userSnapshot.val();

          const data = {
            name,
            createdAt,
            avatar,
            uid: authObj.uid,
            email: authObj.email,
            isAdmin: isAdmin || authObj.email === ADMIN_EMAIL,
          };

          // إذا كان البريد الإلكتروني هو بريد المشرف ولم يتم تعيينه كمشرف بعد
          if (authObj.email === ADMIN_EMAIL && !isAdmin) {
            // تحديث حالة المشرف في قاعدة البيانات
            await set(ref(database, `/users/${authObj.uid}/isAdmin`), true);

            // إنشاء غرف الدردشة الافتراضية إذا كان المستخدم هو المشرف
            await createDefaultRooms(authObj.uid);

            // تحديث البيانات مع حالة المشرف الجديدة
            data.isAdmin = true;
          }

          console.log('User profile loaded:', data);
          setProfile(data);
        } else {
          // إذا لم يكن المستخدم موجودًا في قاعدة البيانات، قم بإنشائه
          console.log('Creating new user profile');
          const isAdmin = authObj.email === ADMIN_EMAIL;

          const data = {
            name: authObj.displayName,
            createdAt: serverTimestamp(),
            uid: authObj.uid,
            email: authObj.email,
            isAdmin,
          };

          // حفظ بيانات المستخدم في قاعدة البيانات
          await set(userRef, {
            name: authObj.displayName,
            createdAt: serverTimestamp(),
            isAdmin,
          });

          // إذا كان المستخدم هو المشرف، قم بإنشاء غرف الدردشة الافتراضية
          if (isAdmin) {
            await createDefaultRooms(authObj.uid);
          }

          console.log('New user profile created:', data);
          setProfile(data);
        }

        // الاستماع للتغييرات في بيانات المستخدم بعد التحميل الأولي
        onValue(userRef, (snap) => {
          if (snap.exists()) {
            const { name, createdAt, avatar, isAdmin } = snap.val();

            setProfile(prevProfile => ({
              ...prevProfile,
              name,
              createdAt,
              avatar,
              isAdmin: isAdmin || authObj.email === ADMIN_EMAIL,
            }));
          }
        });

        setIsLoading(false);
      } else {
        // لا يوجد مستخدم مسجل الدخول
        console.log('No user is signed in');

        // إزالة حالة المصادقة من التخزين المحلي
        localStorage.removeItem('authState');

        if (userRef) {
          off(userRef);
        }

        if (userStatusRef) {
          off(userStatusRef);
        }

        off(ref(database, ".info/connected"));

        setProfile(null);
        setIsLoading(false);
      }
    });

    return () => {
      authUnsub();

      off(ref(database, ".info/connected"));

      if (userRef) {
        off(userRef);
      }

      if (userStatusRef) {
        off(userStatusRef);
      }
    };
  }, []);

  return (
    <ProfileContext.Provider value={{ profile, isLoading }}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => useContext(ProfileContext);
