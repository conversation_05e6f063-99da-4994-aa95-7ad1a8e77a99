import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { BrowserRouter } from "react-router-dom";
import { ProfileProvider } from './context/profile.context';
import { LanguageProvider } from './context/language.context';
import './styles/main.scss';
import 'rsuite/dist/rsuite.min.css';

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <BrowserRouter>
    <ProfileProvider>
      <LanguageProvider>
        <App />
      </LanguageProvider>
    </ProfileProvider>
  </BrowserRouter>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
