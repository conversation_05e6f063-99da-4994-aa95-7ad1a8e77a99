import React, { useState, useRef, useEffect } from "react";
import { But<PERSON>, Message, Modal, toaster, Alert } from "rsuite";
import AvatarEditor from "react-avatar-editor";
import { useModalState } from "../../misc/custom-hooks";
import { useProfile } from "../../context/profile.context";
import {
  ref as storageRef,
  uploadBytes,
  getDownloadURL,
} from "firebase/storage";
import { database, storage, auth } from "../../misc/firebase.config";
import { getUserUpdates } from "../../misc/helpers";
import { update, ref as dbRef, get } from "firebase/database";
import ProfileAvatar from "../ProfileAvatar";

const fileInputTypes = ".png, .jpeg, .jpg";

const acceptedFileTypes = ["image/png", "image/jpeg", "image/pjpeg"];
const isValidFile = (file) => acceptedFileTypes.includes(file.type);

const getBlob = (canvas) => {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error("File process error"));
      }
    });
  });
};

const AvatarUploadBtn = ({ isAdmin = false, isOwner = false }) => {
  const { isOpen, open, close } = useModalState();
  const { profile } = useProfile();
  const [isLoading, setIsLoading] = useState(false);
  const [img, setImg] = useState(null);
  const avatarEditorRef = useRef();
  const [canChangeAvatar, setCanChangeAvatar] = useState(true);
  const [daysLeft, setDaysLeft] = useState(0);

  // التحقق من إمكانية تغيير الصورة الشخصية
  useEffect(() => {
    const checkAvatarChangeStatus = async () => {
      try {
        // إذا كان المستخدم مشرفًا أو مالكًا، فيمكنه تغيير صورته في أي وقت
        if (isAdmin || isOwner) {
          setCanChangeAvatar(true);
          return;
        }

        // التحقق من آخر مرة تم فيها تغيير الصورة الشخصية
        const lastChangeRef = dbRef(database, `/profile/${profile.uid}/lastAvatarChange`);
        const snapshot = await get(lastChangeRef);

        if (snapshot.exists()) {
          const lastChangeTime = snapshot.val();
          const now = Date.now();
          const daysSinceLastChange = Math.floor((now - lastChangeTime) / (1000 * 60 * 60 * 24));

          // يمكن تغيير الصورة مرة واحدة كل 7 أيام
          if (daysSinceLastChange < 7) {
            setCanChangeAvatar(false);
            setDaysLeft(7 - daysSinceLastChange);
          } else {
            setCanChangeAvatar(true);
          }
        } else {
          // إذا لم يتم تغيير الصورة من قبل، فيمكن تغييرها
          setCanChangeAvatar(true);
        }
      } catch (error) {
        console.error("Error checking avatar change status:", error);
      }
    };

    if (profile) {
      checkAvatarChangeStatus();
    }
  }, [profile, isAdmin, isOwner]);

  const onFileInputChange = (ev) => {
    // التحقق من إمكانية تغيير الصورة الشخصية
    if (!isAdmin && !isOwner && !canChangeAvatar) {
      toaster.push(
        <Message type="error" duration={4000} closable>
          لا يمكنك تغيير صورتك الشخصية إلا مرة واحدة كل 7 أيام. يرجى الانتظار {daysLeft} يوم.
        </Message>
      );
      return;
    }

    const currentFiles = ev.target.files;
    // console.log(currentFiles);

    if (currentFiles.length === 1) {
      const file = currentFiles[0];

      if (isValidFile(file)) {
        setImg(file);
        open();
      } else {
        toaster.push(
          <Message type="warning" duration={4000} closable>
            {`نوع الملف غير صالح ${file.type}`}
          </Message>
        );
      }
    }
  };

  const onUploadClick = async () => {
    const canvas = avatarEditorRef.current.getImageScaledToCanvas();

    setIsLoading(true);

    try {
      const blob = await getBlob(canvas);

      // التأكد من أن المستخدم مسجل الدخول
      if (!auth.currentUser) {
        throw new Error('يجب تسجيل الدخول لرفع الصورة');
      }

      // إنشاء اسم فريد للملف باستخدام الطابع الزمني
      const fileName = `avatar_${new Date().getTime()}`;
      const avatarFileRef = storageRef(storage, `/users/${profile.uid}/${fileName}`);

      // تعيين نوع MIME للصورة بشكل صريح
      const metadata = {
        contentType: 'image/jpeg',
        cacheControl: `public, max-age=${3600 * 24 * 3}`,
      };

      console.log('Storage reference:', storage);
      console.log('Storage bucket:', storage.app.options.storageBucket);
      console.log('Uploading avatar to:', `/users/${profile.uid}/${fileName}`);

      try {
        // رفع الصورة مع البيانات الوصفية
        const uploadResult = await uploadBytes(avatarFileRef, blob, metadata);
        console.log('Upload successful:', uploadResult);
      } catch (uploadError) {
        console.error('Error during uploadBytes:', uploadError);
        throw uploadError;
      }

      // الحصول على رابط التنزيل
      const downloadUrl = await getDownloadURL(avatarFileRef);
      console.log('Download URL:', downloadUrl);

      // تحديث قاعدة البيانات
      const updates = await getUserUpdates(
        profile.uid,
        "avatar",
        downloadUrl,
        database
      );

      // إضافة تحديث لتسجيل وقت آخر تغيير للصورة الشخصية
      if (!isAdmin && !isOwner) {
        updates[`/profile/${profile.uid}/lastAvatarChange`] = Date.now();
      }

      await update(dbRef(database), updates);

      // تحديث حالة التغيير
      if (!isAdmin && !isOwner) {
        setCanChangeAvatar(false);
        setDaysLeft(7);
      }

      setIsLoading(false);
      toaster.push(
        <Message type="info" closable duration={4000}>
          تم رفع الصورة الشخصية بنجاح
        </Message>
      );
      close();
    } catch (error) {
      console.error('Error uploading avatar:', error);
      setIsLoading(false);

      // عرض رسالة خطأ أكثر تفصيلاً
      let errorMessage = 'خطأ غير معروف';

      if (error.code === 'storage/unauthorized') {
        errorMessage = 'ليس لديك صلاحية لرفع الصورة. تأكد من تسجيل الدخول.';
      } else if (error.code === 'storage/canceled') {
        errorMessage = 'تم إلغاء عملية رفع الصورة.';
      } else if (error.code === 'storage/unknown') {
        errorMessage = 'حدث خطأ غير معروف أثناء رفع الصورة.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء رفع الصورة: {errorMessage}
        </Message>
      );
    }
  };

  return (
    <div className="mt-3 text-center">
      <ProfileAvatar
        src={profile.avatar}
        name={profile.name}
        className="width-5 height-5 img-fullsize font-huge"
      />

      <div>
        <label
          htmlFor="avatar-upload"
          className={`d-block cursor-pointer padded avatar-upload-btn ${!canChangeAvatar && !isAdmin && !isOwner ? 'disabled-upload-btn' : ''}`}
        >
          اختيار صورة شخصية جديدة
          <input
            type="file"
            className="d-none"
            id="avatar-upload"
            accept={fileInputTypes}
            onChange={(e) => onFileInputChange(e)}
            disabled={!canChangeAvatar && !isAdmin && !isOwner}
          />
        </label>

        <Modal open={isOpen} onClose={close} className="dark-modal">
          <Modal.Header className="dark-modal-header">
            <Modal.Title className="dark-modal-title">تعديل ورفع صورة شخصية جديدة</Modal.Title>
          </Modal.Header>
          <Modal.Body className="dark-modal-body">
            <div className="d-flex justify-content-center align-items-center h-100">
              {img && (
                <AvatarEditor
                  ref={avatarEditorRef}
                  image={img}
                  width={200}
                  height={200}
                  border={10}
                  borderRadius={100}
                  rotate={0}
                />
              )}
            </div>
          </Modal.Body>
          <Modal.Footer className="dark-modal-footer">
            <Button
              block
              appearance="primary"
              onClick={onUploadClick}
              disabled={isLoading}
              className="dark-upload-btn"
            >
              {isLoading ? 'جاري الرفع...' : 'رفع الصورة الشخصية الجديدة'}
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    </div>
  );
};

export default AvatarUploadBtn;
