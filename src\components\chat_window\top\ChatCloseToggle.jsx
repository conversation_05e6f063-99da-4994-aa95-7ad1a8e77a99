import React, { useState, useEffect } from 'react';
import { Button, Modal, Form, Message, toaster, Toggle, InputNumber } from 'rsuite';
import { useParams } from 'react-router';
import { database, auth } from '../../../misc/firebase.config';
import { ref, set, get, onValue, off } from 'firebase/database';
import { useLanguage } from '../../../context/language.context';
import TranslatedText from '../../TranslatedText';

const ChatCloseToggle = () => {
  const { chatId } = useParams();
  const { t } = useLanguage();
  const [isChatClosed, setIsChatClosed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAutoChatScheduleEnabled, setIsAutoChatScheduleEnabled] = useState(false);
  const [chatOpenTime, setChatOpenTime] = useState(9); // 9 AM
  const [chatCloseTime, setChatCloseTime] = useState(24); // 12 AM (24 hour format)
  const [isDirectMessage, setIsDirectMessage] = useState(false);

  // التحقق مما إذا كان المستخدم هو المالك أو مشرف
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );
  const [isAdmin, setIsAdmin] = useState(false);

  // التحقق من نوع الغرفة وصلاحيات المستخدم
  useEffect(() => {
    if (!chatId) return;

    const roomRef = ref(database, `/rooms/${chatId}`);
    const unsubscribe = onValue(roomRef, (snapshot) => {
      if (snapshot.exists()) {
        const roomData = snapshot.val();
        
        // التحقق مما إذا كانت دردشة خاصة
        const isDM = roomData.isDirectMessage || false;
        setIsDirectMessage(isDM);
        
        // التحقق من حالة إغلاق الدردشة
        const chatClosed = roomData.isChatClosed || false;
        setIsChatClosed(chatClosed);
        
        // التحقق من إعدادات الجدولة التلقائية
        const autoScheduleEnabled = roomData.isAutoChatScheduleEnabled || false;
        setIsAutoChatScheduleEnabled(autoScheduleEnabled);
        
        // تحميل أوقات الفتح والإغلاق
        if (roomData.chatOpenTime !== undefined) {
          setChatOpenTime(roomData.chatOpenTime);
        }
        
        if (roomData.chatCloseTime !== undefined) {
          setChatCloseTime(roomData.chatCloseTime);
        }
        
        // التحقق مما إذا كان المستخدم مشرفًا في هذه الغرفة
        const admins = roomData.admins || {};
        const isUserAdmin = admins[auth.currentUser.uid] === true;
        setIsAdmin(isUserAdmin);
      }
    });

    return () => {
      off(roomRef);
    };
  }, [chatId]);

  // تبديل حالة إغلاق الدردشة
  const toggleChatClosed = async () => {
    if (!isOwner && !isAdmin) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="فقط المالك والمشرفين يمكنهم إيقاف أو فتح الدردشة" />
        </Message>
      );
      return;
    }

    setIsLoading(true);

    try {
      const newStatus = !isChatClosed;
      await set(ref(database, `/rooms/${chatId}/isChatClosed`), newStatus);
      setIsChatClosed(newStatus);

      toaster.push(
        <Message type="success" closable duration={4000}>
          {newStatus ? <TranslatedText text="تم إيقاف الدردشة بنجاح" /> : <TranslatedText text="تم فتح الدردشة بنجاح" />}
        </Message>
      );
    } catch (error) {
      console.error('Error toggling chat closed state:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="حدث خطأ أثناء تغيير حالة الدردشة" />: {error.message}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  // حفظ إعدادات الجدولة التلقائية
  const saveScheduleSettings = async () => {
    if (!isOwner && !isAdmin) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="فقط المالك والمشرفين يمكنهم تعديل إعدادات الجدولة" />
        </Message>
      );
      return;
    }

    setIsLoading(true);

    try {
      // التحقق من صحة الإدخال
      if (chatOpenTime < 0 || chatOpenTime > 24 || chatCloseTime < 0 || chatCloseTime > 24) {
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="يرجى إدخال قيم صحيحة للوقت (0-24)" />
          </Message>
        );
        setIsLoading(false);
        return;
      }

      // حفظ الإعدادات
      const updates = {
        isAutoChatScheduleEnabled: isAutoChatScheduleEnabled,
        chatOpenTime: chatOpenTime,
        chatCloseTime: chatCloseTime
      };

      await set(ref(database, `/rooms/${chatId}/isAutoChatScheduleEnabled`), isAutoChatScheduleEnabled);
      await set(ref(database, `/rooms/${chatId}/chatOpenTime`), chatOpenTime);
      await set(ref(database, `/rooms/${chatId}/chatCloseTime`), chatCloseTime);

      toaster.push(
        <Message type="success" closable duration={4000}>
          <TranslatedText text="chatScheduleSaved" />
        </Message>
      );

      // إغلاق النافذة
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving schedule settings:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="حدث خطأ أثناء حفظ إعدادات الجدولة" />: {error.message}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  // إذا كانت دردشة خاصة، لا تعرض الزر
  if (isDirectMessage) {
    return null;
  }

  return (
    <>
      {/* زر إيقاف/فتح الدردشة */}
      {(isOwner || isAdmin) && (
        <Button
          appearance="subtle"
          onClick={toggleChatClosed}
          disabled={isLoading}
          className="chat-close-btn"
        >
          <TranslatedText text={isChatClosed ? 'openChatButton' : 'closeChatButton'} />
        </Button>
      )}

      {/* زر إعدادات الجدولة التلقائية */}
      {(isOwner || isAdmin) && (
        <Button
          appearance="subtle"
          onClick={() => setIsModalOpen(true)}
          className="schedule-btn"
        >
          <TranslatedText text="autoChatSchedule" />
        </Button>
      )}

      {/* نافذة إعدادات الجدولة التلقائية */}
      <Modal open={isModalOpen} onClose={() => setIsModalOpen(false)} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">
            <TranslatedText text="autoChatSchedule" />
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <Form fluid>
            <Form.Group>
              <Form.ControlLabel>
                <TranslatedText text="autoChatSchedule" />
              </Form.ControlLabel>
              <Toggle
                checked={isAutoChatScheduleEnabled}
                onChange={setIsAutoChatScheduleEnabled}
              />
            </Form.Group>

            <Form.Group>
              <Form.ControlLabel>
                <TranslatedText text="chatOpenTime" /> (0-24)
              </Form.ControlLabel>
              <InputNumber
                min={0}
                max={24}
                value={chatOpenTime}
                onChange={value => setChatOpenTime(value)}
              />
            </Form.Group>

            <Form.Group>
              <Form.ControlLabel>
                <TranslatedText text="chatCloseTime" /> (0-24)
              </Form.ControlLabel>
              <InputNumber
                min={0}
                max={24}
                value={chatCloseTime}
                onChange={value => setChatCloseTime(value)}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={() => setIsModalOpen(false)} appearance="subtle">
            <TranslatedText text="cancel" />
          </Button>
          <Button onClick={saveScheduleSettings} appearance="primary" loading={isLoading}>
            <TranslatedText text="saveSchedule" />
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ChatCloseToggle;
