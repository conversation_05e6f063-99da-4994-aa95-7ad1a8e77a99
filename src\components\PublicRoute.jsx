import React, { useEffect, useState } from "react";
import { Redirect, Route } from "react-router";
import { Container, Loader } from "rsuite";
import { useProfile } from "../context/profile.context";

const PublicRoute = ({ children, ...routeProps }) => {
  const { profile, isLoading } = useProfile();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // التحقق من حالة المصادقة في التخزين المحلي
  useEffect(() => {
    const checkLocalAuth = () => {
      const savedAuthState = localStorage.getItem('authState');
      if (savedAuthState === 'authenticated') {
        console.log('Found authenticated state in localStorage');
        setIsAuthenticated(true);
      }
    };

    checkLocalAuth();
  }, []);

  if (isLoading) {
    return (
      <Container>
        <Loader center vertical size="md" speed="slow" content="جاري التحميل..." />
      </Container>
    );
  }

  // إعادة التوجيه إذا كان المستخدم مصادقًا (إما من السياق أو من التخزين المحلي)
  if ((profile || isAuthenticated) && !isLoading) {
    console.log('User is authenticated, redirecting to home page');
    // استخدم الصفحة الوسيطة للتأكد من حالة المصادقة
    return <Redirect to={"/auth-bridge"} />;
  }

  return <Route {...routeProps}>{ children }</Route>;
};

export default PublicRoute;
