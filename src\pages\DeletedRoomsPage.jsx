import React, { useEffect, useState } from 'react';
import { ref, get, set, remove } from 'firebase/database';
import { database, auth } from '../misc/firebase.config';
import { Container, Content, FlexboxGrid, Panel, List, Button, Message, toaster, Loader, Modal, Header } from 'rsuite';
import TimeAgo from 'timeago-react';
import { useProfile } from '../context/profile.context';
import ProfileAvatar from '../components/ProfileAvatar';
import RestoreRoomButton from '../components/dashboard/RestoreRoomButton';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';
import { useHistory } from 'react-router-dom';

const DeletedRoomsPage = () => {
  const [deletedRooms, setDeletedRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [showMessagesModal, setShowMessagesModal] = useState(false);
  const [roomMessages, setRoomMessages] = useState([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const { profile } = useProfile();
  const history = useHistory();

  // دالة للعودة إلى الصفحة السابقة
  const handleBack = () => {
    // التحقق من وجود صفحة سابقة في تاريخ التصفح
    if (window.history.length > 1) {
      // إذا كان هناك صفحة سابقة، استخدم history.goBack()
      history.goBack();
    } else {
      // إذا لم يكن هناك صفحة سابقة، انتقل إلى الصفحة الرئيسية
      history.push('/');
    }
  };

  // التحقق مما إذا كان المستخدم مشرفًا
  const isAdmin = profile && (
    profile.email === '<EMAIL>' ||
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2' ||
    profile.isAdmin === true
  );

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // جلب رسائل الغرفة المحذوفة
  const fetchRoomMessages = async (roomId) => {
    if (!roomId) return;

    setLoadingMessages(true);
    try {
      // جلب رسائل الغرفة من قاعدة البيانات
      const messagesRef = ref(database, `/deleted-rooms/${roomId}/messages`);
      const snapshot = await get(messagesRef);

      if (snapshot.exists()) {
        const messagesData = snapshot.val();

        // تحويل البيانات إلى مصفوفة
        const messagesArray = Object.keys(messagesData).map(messageId => ({
          id: messageId,
          ...messagesData[messageId]
        }));

        // ترتيب الرسائل حسب تاريخ الإنشاء
        messagesArray.sort((a, b) => a.createdAt - b.createdAt);

        setRoomMessages(messagesArray);
      } else {
        setRoomMessages([]);
      }

      setLoadingMessages(false);
    } catch (error) {
      console.error('Error fetching room messages:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في جلب رسائل الغرفة: {error.message}
        </Message>
      );
      setLoadingMessages(false);
    }
  };

  // فتح نافذة عرض الرسائل
  const handleViewMessages = (room) => {
    setSelectedRoom(room);
    setShowMessagesModal(true);
    fetchRoomMessages(room.id);
  };

  useEffect(() => {
    const fetchDeletedRooms = async () => {
      try {
        // التحقق من وجود مستخدم مسجل الدخول
        if (!auth.currentUser) {
          setLoading(false);
          return;
        }

        // التحقق مما إذا كان المستخدم مشرفًا
        if (!isAdmin) {
          setLoading(false);
          return;
        }

        // جلب قائمة الغرف المحذوفة
        const deletedRoomsRef = ref(database, '/deleted-rooms');
        const snapshot = await get(deletedRoomsRef);

        if (snapshot.exists()) {
          const roomsData = snapshot.val();

          // تحويل البيانات إلى مصفوفة
          const roomsArray = Object.keys(roomsData).map(roomId => ({
            id: roomId,
            ...roomsData[roomId]
          }));

          // ترتيب الغرف حسب تاريخ الحذف (الأحدث أولاً)
          roomsArray.sort((a, b) => new Date(b.deletedAt) - new Date(a.deletedAt));

          setDeletedRooms(roomsArray);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching deleted rooms:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في جلب سجلات الغرف المحذوفة: {error.message}
          </Message>
        );
        setLoading(false);
      }
    };

    fetchDeletedRooms();
  }, [isAdmin]);

  // إذا كان المستخدم ليس مشرفًا، عرض رسالة خطأ
  if (!isAdmin) {
    return (
      <Container>
        <FlexboxGrid justify="center" align="middle" style={{ height: '100vh' }}>
          <FlexboxGrid.Item colspan={12}>
            <Panel header="غير مصرح" bordered>
              <Message type="error" showIcon>
                لا يمكنك الوصول إلى هذه الصفحة. هذه الصفحة متاحة للمشرفين فقط.
              </Message>
            </Panel>
          </FlexboxGrid.Item>
        </FlexboxGrid>
      </Container>
    );
  }

  return (
    <Container>
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          <h4 className="header-title">سجل المجموعات المحذوفة</h4>
        </div>
      </Header>
      <Content className="mt-3">
        <FlexboxGrid justify="center">
          <FlexboxGrid.Item colspan={20}>
            <Panel bordered className="dark-panel">
              {loading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <Loader size="md" content="جاري التحميل..." />
                </div>
              ) : deletedRooms.length === 0 ? (
                <Message type="info" showIcon>
                  لا توجد مجموعات محذوفة.
                </Message>
              ) : (
                <List hover>
                  {deletedRooms.map(room => (
                    <List.Item key={room.id}>
                      <FlexboxGrid align="middle">
                        <FlexboxGrid.Item colspan={14}>
                          <h4>{room.name}</h4>
                          <p>{room.description}</p>
                          <small>
                            تم الحذف: <TimeAgo datetime={new Date(room.deletedAt)} />
                          </small>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={4} style={{ textAlign: 'center' }}>
                          <div>
                            حالة البيع: {room.saleSuccessful ? (
                              <span style={{ color: 'green' }}>ناجح ✅</span>
                            ) : (
                              <span style={{ color: 'red' }}>غير ناجح ❌</span>
                            )}
                          </div>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={3} style={{ textAlign: 'center' }}>
                          {/* زر استعادة المجموعة (يظهر فقط للمالك) */}
                          <RestoreRoomButton room={room} />
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={3} style={{ textAlign: 'right' }}>
                          <Button appearance="link" onClick={() => handleViewMessages(room)}>
                            عرض الرسائل
                          </Button>
                        </FlexboxGrid.Item>
                      </FlexboxGrid>
                    </List.Item>
                  ))}
                </List>
              )}
            </Panel>
          </FlexboxGrid.Item>
        </FlexboxGrid>
      </Content>

      {/* نافذة عرض رسائل الغرفة المحذوفة */}
      <Modal
        open={showMessagesModal}
        onClose={() => setShowMessagesModal(false)}
        size="full"
        className="dark-theme deleted-messages-modal"
        full
      >
        <Modal.Header>
          <Modal.Title>
            {selectedRoom && (
              <div>
                رسائل الغرفة: {selectedRoom.name}
                <div style={{ fontSize: '12px', opacity: 0.7 }}>
                  تم الحذف: <TimeAgo datetime={new Date(selectedRoom.deletedAt)} />
                  {' | '}
                  حالة البيع: {selectedRoom.saleSuccessful ? (
                    <span style={{ color: 'green' }}>ناجح ✅</span>
                  ) : (
                    <span style={{ color: 'red' }}>غير ناجح ❌</span>
                  )}
                </div>
              </div>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body style={{
          height: 'calc(100vh - 130px)',
          overflow: 'auto',
          backgroundColor: '#000',
          color: '#fff',
          padding: '20px',
          width: '100%'
        }}>
          {loadingMessages ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Loader size="md" content="جاري تحميل الرسائل..." />
            </div>
          ) : roomMessages.length === 0 ? (
            <Message type="info" showIcon>
              لا توجد رسائل في هذه الغرفة.
            </Message>
          ) : (
            <div className="messages-container" style={{ width: '100%' }}>
              {roomMessages.map(message => {
                const isCurrentUser = message.author?.uid === auth.currentUser.uid;

                return (
                  <div
                    key={message.id}
                    className={`message-group ${isCurrentUser ? 'ml-auto' : 'mr-auto'}`}
                    style={{
                      maxWidth: '80%',
                      marginBottom: '15px',
                      alignSelf: isCurrentUser ? 'flex-end' : 'flex-start',
                      display: 'flex',
                      flexDirection: isCurrentUser ? 'row-reverse' : 'row'
                    }}
                  >
                    <div style={{ margin: isCurrentUser ? '0 0 0 10px' : '0 10px 0 0' }}>
                      <ProfileAvatar
                        src={message.author?.avatar}
                        name={message.author?.name}
                        size="md"
                      />
                    </div>
                    <div
                      style={{
                        backgroundColor: isCurrentUser ? '#dcf8c6' : 'white',
                        borderRadius: '10px',
                        padding: '10px',
                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                        position: 'relative'
                      }}
                    >
                      <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                        {message.author?.name}
                      </div>
                      <div style={{ wordBreak: 'break-word' }}>
                        {message.text || (message.file && (
                          <div>
                            <span>ملف مرفق</span>
                            {message.file.url && (
                              <div>
                                {message.file.contentType.includes('image') ? (
                                  <img
                                    src={message.file.url}
                                    alt="صورة مرفقة"
                                    style={{ maxWidth: '100%', maxHeight: '200px', marginTop: '5px', borderRadius: '5px' }}
                                  />
                                ) : (
                                  <a
                                    href={message.file.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{ display: 'block', marginTop: '5px' }}
                                  >
                                    تحميل الملف
                                  </a>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                      <div style={{ fontSize: '11px', color: '#999', textAlign: 'right', marginTop: '5px' }}>
                        <TimeAgo datetime={new Date(message.createdAt)} />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          {/* زر استعادة المجموعة (يظهر فقط للمالك) */}
          {isOwner && selectedRoom && (
            <RestoreRoomButton room={selectedRoom} />
          )}
          <Button onClick={() => setShowMessagesModal(false)} appearance="primary">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default DeletedRoomsPage;
