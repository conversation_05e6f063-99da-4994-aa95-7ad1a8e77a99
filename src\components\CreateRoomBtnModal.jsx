import React, { useCallback, useEffect, useRef, useState } from "react";
import { Button, Form, Input, Message, Modal, Schema, toaster, Toggle } from "rsuite";
import CreativeIcon from "@rsuite/icons/Creative";
import { useModalState } from "../misc/custom-hooks";
import { serverTimestamp, ref, push, get } from "firebase/database";
import { database, auth } from "../misc/firebase.config";

const Textarea = React.forwardRef((props, ref) => (
  <Input {...props} as="textarea" ref={ref} />
));

const { StringType, BooleanType, ArrayType } = Schema.Types;

const model = Schema.Model({
  name: StringType().isRequired("اسم الغرفة مطلوب"),
  description: StringType(), // جعل الوصف غير إجباري
  isPrivate: BooleanType(),
  isOwnerOnly: BooleanType(), // إضافة خاصية جديدة للسماح فقط للمالك بالكتابة
  allowedUsers: ArrayType()
});

const INITIAL_FORM = {
  name: "",
  description: "",
  isPrivate: false,
  isOwnerOnly: false, // إضافة الخاصية الجديدة
  allowedUser1: "",
  allowedUser2: "",
  allowedUser3: "",
  allowedUser4: "",
  // إضافة خصائص جديدة لتخصيص المجموعة
  textColor: "", // لون النص
  borderColor: "" // لون الإطار
};

// دالة للتحقق من صحة البريد الإلكتروني
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// دالة للبحث عن مستخدم بواسطة البريد الإلكتروني
const findUserByEmail = async (email) => {
  try {
    console.log('Searching for user with email:', email);
    const usersRef = ref(database, 'users');
    const snapshot = await get(usersRef);

    if (snapshot.exists()) {
      const usersData = snapshot.val();
      const users = Object.entries(usersData);
      console.log('Total users found:', users.length);

      for (const [uid, userData] of users) {
        console.log('Checking user:', uid, userData);
        // تحقق من وجود البريد الإلكتروني في بيانات المستخدم
        if (userData && userData.email) {
          console.log('Comparing emails:', userData.email.toLowerCase(), 'vs', email.toLowerCase());
          if (userData.email.toLowerCase() === email.toLowerCase()) {
            console.log('User found with UID:', uid);
            return uid;
          }
        }
      }
    } else {
      console.log('No users found in database');
    }

    console.log('User not found with email:', email);
    return null;
  } catch (error) {
    console.error('Error finding user by email:', error);
    return null;
  }
};

const CreateRoomBtnModal = () => {
  const { isOpen, open, close } = useModalState();

  // التحقق من صلاحيات المستخدم لإنشاء غرفة عامة
  const isOwner = auth.currentUser && (auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.currentUser.email === '<EMAIL>');

  // التحقق من صلاحيات المشرف
  const [isUserAdmin, setIsUserAdmin] = useState(false);

  // التحقق من حالة المشرف للمستخدم الحالي
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        if (auth.currentUser) {
          const userRef = ref(database, `users/${auth.currentUser.uid}`);
          const snapshot = await get(userRef);

          if (snapshot.exists()) {
            const userData = snapshot.val();
            setIsUserAdmin(userData.isAdmin === true);
          }
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      }
    };

    checkAdminStatus();
  }, []);

  // تعيين القيمة الافتراضية للنموذج (إذا كان المستخدم ليس المالك، اجعل الغرفة خاصة افتراضياً)
  const [formValue, setFormValue] = useState({
    ...INITIAL_FORM,
    isPrivate: !isOwner, // المشرفين يمكنهم فقط إنشاء مجموعات خاصة
    allowedUser4: 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' // إضافة معرف المالك تلقائياً في الخانة الرابعة
  });
  const [isLoading, setIsLoading] = useState(false);
  const formRef = useRef();

  const onFormChange = useCallback((value) => {
    setFormValue(value);
  }, []);

  // معالجة إضافة مستخدم عبر البريد الإلكتروني أو معرف المستخدم
  const validateUser = async (value) => {
    if (!value || value.trim() === "") {
      return null;
    }

    setIsLoading(true);
    console.log('Validating user input:', value);

    try {
      let uid = value.trim();

      // التحقق من وجود المستخدم في قاعدة البيانات باستخدام UID أولاً
      console.log('Checking if user exists with UID:', uid);
      const userRef = ref(database, `users/${uid}`);
      const userSnapshot = await get(userRef);

      if (userSnapshot.exists()) {
        console.log('User exists with UID:', uid);
        // عرض معلومات المستخدم
        const userData = userSnapshot.val();
        console.log('User data:', userData);

        // عرض رسالة نجاح
        toaster.push(
          <Message type="success" closable duration={4000}>
            تم العثور على المستخدم وإضافته بنجاح: {userData.name || 'مستخدم'}
          </Message>
        );

        setIsLoading(false);
        return uid;
      }
      // إذا كان البريد الإلكتروني، ابحث عن معرف المستخدم المرتبط به
      else if (isValidEmail(uid)) {
        console.log('Input is an email, searching for user...');
        const foundUid = await findUserByEmail(uid);

        if (foundUid) {
          console.log('User found with email, using UID:', foundUid);

          // عرض رسالة نجاح
          toaster.push(
            <Message type="success" closable duration={4000}>
              تم العثور على المستخدم وإضافته بنجاح
            </Message>
          );

          setIsLoading(false);
          return foundUid;
        } else {
          console.log('No user found with email:', uid);
          toaster.push(
            <Message type="error" closable duration={4000}>
              لم يتم العثور على مستخدم بهذا البريد الإلكتروني: {uid}
            </Message>
          );
          setIsLoading(false);
          return null;
        }
      }
      // إذا لم يكن UID صالح ولا بريد إلكتروني
      else {
        console.log('Invalid input, not a valid UID or email:', uid);
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على مستخدم بهذا المعرف: {uid}
          </Message>
        );
        setIsLoading(false);
        return null;
      }
    } catch (error) {
      console.error('Error validating user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في التحقق من المستخدم: {error.message}
        </Message>
      );
      setIsLoading(false);
      return null;
    }
  };

  const onSubmit = async () => {
    if (!formRef.current.check()) {
      return;
    }

    setIsLoading(true);

    // إذا كان المستخدم يحاول إنشاء غرفة عامة وليس هو المالك
    if (!formValue.isPrivate && !isOwner) {
      setIsLoading(false);
      toaster.push(
        <Message type="error" closable duration={4000}>
          فقط المالك يمكنه إنشاء غرف دردشة عامة. يمكنك إنشاء غرفة خاصة فقط.
        </Message>
      );
      return;
    }

    // الحصول على معلومات المستخدم الحالي
    const userRef = ref(database, `/users/${auth.currentUser.uid}`);
    const userSnapshot = await get(userRef);
    const userData = userSnapshot.exists() ? userSnapshot.val() : {};
    const creatorName = userData.name || 'مستخدم غير معروف';
    const creatorEmail = userData.email || auth.currentUser.uid;

    // إنشاء وصف مخصص للمجموعات الخاصة
    let finalDescription = formValue.description || '';

    // إذا كانت المجموعة خاصة، أضف معلومات المشرف المنشئ في الوصف
    if (formValue.isPrivate) {
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate()}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()} ${currentDate.getHours()}:${currentDate.getMinutes()}`;

      const adminInfo = `
------ معلومات المجموعة ------
المنشئ: ${creatorName}
معرف المنشئ: ${auth.currentUser.uid}
البريد الإلكتروني: ${creatorEmail}
تاريخ الإنشاء: ${formattedDate}
---------------------------

${finalDescription}`;

      finalDescription = adminInfo;
    }

    // إعداد بيانات الغرفة الجديدة
    const newRoomData = {
      name: formValue.name,
      description: finalDescription, // استخدام الوصف المعدل
      isPrivate: formValue.isPrivate,
      isOwnerOnly: formValue.isOwnerOnly, // إضافة خاصية منع الدردشة
      createdAt: serverTimestamp(),
      createdBy: auth.currentUser.uid, // إضافة معرف منشئ الغرفة
      creatorName: creatorName, // إضافة اسم المنشئ
      creatorEmail: creatorEmail, // إضافة بريد المنشئ
      admins: {
        [auth.currentUser.uid]: true,
      },
      fcmUsers: {
        [auth.currentUser.uid]: true,
      },
    };

    // إضافة خصائص التخصيص إذا تم تحديدها من قبل المالك
    if (isOwner) {
      if (formValue.textColor) {
        newRoomData.textColor = formValue.textColor;
      }

      if (formValue.borderColor) {
        newRoomData.borderColor = formValue.borderColor;
      }
    }

    // إذا كانت الغرفة خاصة، أضف قائمة المستخدمين المسموح لهم
    if (formValue.isPrivate) {
      newRoomData.allowedUsers = {};

      // إضافة المستخدم الحالي (منشئ الغرفة) إلى قائمة المستخدمين المسموح لهم
      newRoomData.allowedUsers[auth.currentUser.uid] = true;

      // التحقق من المستخدمين المضافين
      const userInputs = [
        formValue.allowedUser1,
        formValue.allowedUser2,
        formValue.allowedUser3,
        formValue.allowedUser4
      ];

      for (const userInput of userInputs) {
        const uid = await validateUser(userInput);
        if (uid) {
          newRoomData.allowedUsers[uid] = true;
        }
      }
    }

    try {
      await push(ref(database, "rooms"), newRoomData);

      toaster.push(
        <Message type="info" closable duration={4000}>
          {`تم إنشاء غرفة ${formValue.name} بنجاح`}
        </Message>
      );

      setIsLoading(false);
      setFormValue({
        ...INITIAL_FORM,
        allowedUser4: 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' // الحفاظ على معرف المالك في الخانة الرابعة
      });
      close();
    } catch (error) {
      setIsLoading(false);
      toaster.push(
        <Message type="error" closable duration={4000}>
          {error.message}
        </Message>
      );
    }
  };

  return (
    <div className="mt-1">
      <Button block color="green" appearance="primary" onClick={open}>
        <CreativeIcon /> إنشاء غرفة دردشة جديدة
      </Button>

      <Modal open={isOpen} onClose={close} size="md" className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">غرفة دردشة جديدة</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <Form
            fluid
            onChange={onFormChange}
            formValue={formValue}
            model={model}
            ref={formRef}
            className="dark-form"
          >
            <Form.Group controlId="name">
              <Form.ControlLabel>اسم الغرفة</Form.ControlLabel>
              <Form.Control name="name" placeholder="أدخل اسم غرفة الدردشة..." />
              <Form.HelpText>اسم الغرفة مطلوب</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="description">
              <Form.ControlLabel>الوصف</Form.ControlLabel>
              <Form.Control
                rows={5}
                name="description"
                accepter={Textarea}
                placeholder="أدخل وصف الغرفة..."
              />
              <Form.HelpText>وصف الغرفة اختياري</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="isPrivate">
              <Form.ControlLabel>نوع الغرفة</Form.ControlLabel>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                <Toggle
                  name="isPrivate"
                  checked={formValue.isPrivate}
                  onChange={checked => {
                    // إذا كان المستخدم مشرف (وليس المالك) ويحاول إنشاء غرفة عامة
                    if (!checked && !isOwner && isUserAdmin) {
                      toaster.push(
                        <Message type="warning" closable duration={4000}>
                          فقط المالك يمكنه إنشاء غرف دردشة عامة. يمكنك إنشاء غرفة خاصة فقط.
                        </Message>
                      );
                      return; // منع تغيير الحالة
                    }

                    setFormValue({
                      ...formValue,
                      isPrivate: checked,
                      // الحفاظ على معرف المالك في الخانة الرابعة
                      allowedUser4: 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1'
                    });
                  }}
                  disabled={(!isOwner && !isUserAdmin && !formValue.isPrivate) || (!isOwner && isUserAdmin && !formValue.isPrivate)} // تعطيل التبديل للمستخدمين العاديين والمشرفين عند محاولة التغيير إلى غرفة عامة
                />
                <span style={{ marginRight: '10px' }}>
                  {formValue.isPrivate ? 'غرفة خاصة' : 'غرفة عامة'}
                </span>
              </div>
              <Form.HelpText>
                {formValue.isPrivate
                  ? 'الغرفة الخاصة تظهر فقط للمستخدمين المحددين'
                  : 'الغرفة العامة تظهر لجميع المستخدمين'}
                {!isOwner && !isUserAdmin && (
                  <div style={{ color: '#ff4d4f', marginTop: '5px' }}>
                    ملاحظة: فقط المالك يمكنه إنشاء غرف دردشة عامة. يمكنك إنشاء غرفة خاصة فقط.
                  </div>
                )}
                {!isOwner && isUserAdmin && (
                  <div style={{ color: '#ff9800', marginTop: '5px' }}>
                    ملاحظة: فقط المالك يمكنه إنشاء غرف دردشة عامة. يمكنك إنشاء غرفة خاصة فقط.
                  </div>
                )}
              </Form.HelpText>
            </Form.Group>

            {/* إضافة خيار منع الدردشة - يظهر فقط للمالك */}
            {isOwner && (
              <>
                <Form.Group controlId="isOwnerOnly">
                  <Form.ControlLabel>صلاحيات الدردشة</Form.ControlLabel>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                    <Toggle
                      name="isOwnerOnly"
                      checked={formValue.isOwnerOnly}
                      onChange={checked => setFormValue({
                        ...formValue,
                        isOwnerOnly: checked,
                        // الحفاظ على معرف المالك في الخانة الرابعة
                        allowedUser4: 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1'
                      })}
                    />
                    <span style={{ marginRight: '10px' }}>
                      {formValue.isOwnerOnly ? 'منع الدردشة (فقط المالك يمكنه الكتابة)' : 'السماح بالدردشة للجميع'}
                    </span>
                  </div>
                  <Form.HelpText>
                    {formValue.isOwnerOnly
                      ? 'فقط المالك يمكنه إرسال الرسائل في هذه المجموعة، ولا يمكن للمستخدمين الآخرين (حتى المشرفين) الكتابة فيها'
                      : 'يمكن لجميع المستخدمين إرسال الرسائل في هذه المجموعة'}
                  </Form.HelpText>
                </Form.Group>

                {/* إضافة خيارات تخصيص المجموعة للمالك فقط */}
                <Form.Group controlId="textColor">
                  <Form.ControlLabel>لون نص اسم المجموعة</Form.ControlLabel>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginBottom: '10px' }}>
                    {/* لا لون (افتراضي) */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: ''})}
                      style={{
                        width: '30px',
                        height: '30px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        backgroundColor: '#333',
                        boxShadow: formValue.textColor === '' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    >
                      <span style={{fontSize: '18px'}}>✖</span>
                    </div>

                    {/* أبيض */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: 'white'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: 'white',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.textColor === 'white' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* أحمر */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: 'red'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: 'red',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.textColor === 'red' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* أخضر */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: 'green'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: 'green',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.textColor === 'green' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* أزرق */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: 'blue'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: 'blue',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.textColor === 'blue' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* أصفر */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: 'yellow'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: 'yellow',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.textColor === 'yellow' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* بنفسجي */}
                    <div
                      onClick={() => setFormValue({...formValue, textColor: 'purple'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: 'purple',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.textColor === 'purple' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />
                  </div>
                  <Form.HelpText>
                    اختر لون نص اسم المجموعة (اختياري)
                  </Form.HelpText>
                </Form.Group>

                <Form.Group controlId="borderColor">
                  <Form.ControlLabel>لون إطار المجموعة</Form.ControlLabel>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginBottom: '10px' }}>
                    {/* لا إطار (افتراضي) */}
                    <div
                      onClick={() => setFormValue({...formValue, borderColor: ''})}
                      style={{
                        width: '30px',
                        height: '30px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        backgroundColor: '#333',
                        boxShadow: formValue.borderColor === '' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    >
                      <span style={{fontSize: '18px'}}>✖</span>
                    </div>

                    {/* أبيض */}
                    <div
                      onClick={() => setFormValue({...formValue, borderColor: 'white'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: '#333',
                        border: '2px solid white',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.borderColor === 'white' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* رمادي */}
                    <div
                      onClick={() => setFormValue({...formValue, borderColor: 'gray'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: '#333',
                        border: '2px solid gray',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.borderColor === 'gray' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* أسود */}
                    <div
                      onClick={() => setFormValue({...formValue, borderColor: 'black'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: '#333',
                        border: '2px solid black',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.borderColor === 'black' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* أصفر */}
                    <div
                      onClick={() => setFormValue({...formValue, borderColor: 'yellow'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: '#333',
                        border: '2px solid yellow',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.borderColor === 'yellow' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />

                    {/* بنفسجي */}
                    <div
                      onClick={() => setFormValue({...formValue, borderColor: 'purple'})}
                      style={{
                        width: '30px',
                        height: '30px',
                        backgroundColor: '#333',
                        border: '2px solid purple',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        boxShadow: formValue.borderColor === 'purple' ? '0 0 0 2px #4caf50' : 'none'
                      }}
                    />
                  </div>
                  <Form.HelpText>
                    اختر لون إطار المجموعة (اختياري)
                  </Form.HelpText>
                </Form.Group>
              </>
            )}

            {formValue.isPrivate && (
              <>
                <Form.Group controlId="allowedUser1">
                  <Form.ControlLabel>المستخدم المسموح له 1</Form.ControlLabel>
                  <Form.Control
                    name="allowedUser1"
                    placeholder="أدخل معرف المستخدم (UID)"
                    dir="ltr"
                  />
                </Form.Group>

                <Form.Group controlId="allowedUser2">
                  <Form.ControlLabel>المستخدم المسموح له 2</Form.ControlLabel>
                  <Form.Control
                    name="allowedUser2"
                    placeholder="أدخل معرف المستخدم (UID)"
                    dir="ltr"
                  />
                </Form.Group>

                <Form.Group controlId="allowedUser3">
                  <Form.ControlLabel>المستخدم المسموح له 3</Form.ControlLabel>
                  <Form.Control
                    name="allowedUser3"
                    placeholder="أدخل معرف المستخدم (UID)"
                    dir="ltr"
                  />
                </Form.Group>

                {/* الخانة الرابعة مخفية وتحتوي على معرف المالك تلقائياً */}
                <Form.Group controlId="allowedUser4" style={{ display: 'none' }}>
                  <Form.ControlLabel>المستخدم المسموح له 4</Form.ControlLabel>
                  <Form.Control
                    name="allowedUser4"
                    value={formValue.allowedUser4}
                    dir="ltr"
                    readOnly
                  />
                </Form.Group>

                <Form.HelpText>
                  <div>يفضل إدخال معرف المستخدم (UID) للإضافة إلى الغرفة</div>
                  <div>يمكنك الحصول على معرف المستخدم (UID) من صفحة الملف الشخصي للمستخدم</div>
                </Form.HelpText>
              </>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button
            block
            appearance="primary"
            onClick={onSubmit}
            disabled={isLoading}
            className="dark-submit-btn"
          >
            {isLoading ? 'جاري الإنشاء...' : 'إنشاء غرفة دردشة جديدة'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default CreateRoomBtnModal;
