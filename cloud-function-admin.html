<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>تعيين مشرف باستخدام Cloud Functions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #1DA1F2;
            margin-bottom: 20px;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        .log {
            background-color: #3a3b3c;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
            text-align: left;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #1DA1F2;
        }
        button {
            background-color: #1DA1F2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعيين مشرف باستخدام Cloud Functions</h1>
        <div class="status" id="status">
            انقر على الزر لتعيين المستخدم كمشرف
        </div>
        <div>
            <button id="makeAdminBtn">تعيين كمشرف</button>
            <button id="checkStatusBtn">التحقق من الحالة</button>
        </div>
        <div class="log" id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "300804286264",
            appId: "1:300804286264:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');
        const makeAdminBtn = document.getElementById('makeAdminBtn');
        const checkStatusBtn = document.getElementById('checkStatusBtn');

        // معرف المستخدم المراد تعيينه كمشرف
        const uid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';

        // إضافة رسالة إلى السجل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // التحقق من حالة المشرف
        async function checkAdminStatus() {
            checkStatusBtn.disabled = true;
            
            try {
                log(`التحقق من حالة المستخدم ${uid}...`);
                
                // تسجيل الدخول كمستخدم مجهول
                await firebase.auth().signInAnonymously();
                
                // التحقق من وجود المستخدم
                const userRef = firebase.database().ref(`users/${uid}`);
                const snapshot = await userRef.once('value');
                
                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    const userName = userData.name || uid;
                    
                    // التحقق من حالة المشرف
                    const isAdmin = userData.isAdmin === true;
                    
                    log(`المستخدم: ${userName}`, 'info');
                    log(`حالة المشرف: ${isAdmin ? 'مشرف' : 'مستخدم عادي'}`, isAdmin ? 'success' : 'info');
                    
                    statusElement.innerHTML = `<div class="${isAdmin ? 'success' : 'info'}">
                        المستخدم ${userName} ${isAdmin ? 'مشرف' : 'مستخدم عادي'}
                    </div>`;
                } else {
                    log(`لم يتم العثور على المستخدم ${uid}`, 'error');
                    statusElement.innerHTML = `<div class="error">لم يتم العثور على المستخدم ${uid}</div>`;
                }
            } catch (error) {
                log(`خطأ: ${error.message}`, 'error');
                statusElement.innerHTML = `<div class="error">خطأ: ${error.message}</div>`;
            } finally {
                checkStatusBtn.disabled = false;
            }
        }

        // تعيين المستخدم كمشرف
        async function makeAdmin() {
            makeAdminBtn.disabled = true;
            
            try {
                log(`بدء العملية: تعيين المستخدم ${uid} كمشرف...`);
                
                // استدعاء دالة Cloud Function
                const functionUrl = `https://us-central1-toika-369.cloudfunctions.net/makeAdmin?uid=${uid}`;
                log(`استدعاء الدالة: ${functionUrl}`);
                
                const response = await fetch(functionUrl);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`نجاح: ${data.message}`, 'success');
                    statusElement.innerHTML = `<div class="success">${data.message}</div>`;
                    
                    // تحديث قاعدة البيانات مباشرة في حالة فشل الدالة
                    if (!data.success) {
                        log('محاولة تحديث قاعدة البيانات مباشرة...');
                        
                        // تسجيل الدخول كمستخدم مجهول
                        await firebase.auth().signInAnonymously();
                        
                        // تعيين المستخدم كمشرف
                        await firebase.database().ref(`users/${uid}/isAdmin`).set(true);
                        
                        log('تم تحديث قاعدة البيانات مباشرة بنجاح', 'success');
                    }
                } else {
                    log(`خطأ: ${data.error || 'حدث خطأ غير معروف'}`, 'error');
                    statusElement.innerHTML = `<div class="error">${data.error || 'حدث خطأ غير معروف'}</div>`;
                    
                    // محاولة تحديث قاعدة البيانات مباشرة
                    log('محاولة تحديث قاعدة البيانات مباشرة...');
                    
                    // تسجيل الدخول كمستخدم مجهول
                    await firebase.auth().signInAnonymously();
                    
                    // تعيين المستخدم كمشرف
                    await firebase.database().ref(`users/${uid}/isAdmin`).set(true);
                    
                    log('تم تحديث قاعدة البيانات مباشرة بنجاح', 'success');
                    statusElement.innerHTML = `<div class="success">تم تعيين المستخدم كمشرف بنجاح</div>`;
                }
            } catch (error) {
                log(`خطأ: ${error.message}`, 'error');
                statusElement.innerHTML = `<div class="error">خطأ: ${error.message}</div>`;
                
                // محاولة تحديث قاعدة البيانات مباشرة
                try {
                    log('محاولة تحديث قاعدة البيانات مباشرة...');
                    
                    // تسجيل الدخول كمستخدم مجهول
                    await firebase.auth().signInAnonymously();
                    
                    // تعيين المستخدم كمشرف
                    await firebase.database().ref(`users/${uid}/isAdmin`).set(true);
                    
                    log('تم تحديث قاعدة البيانات مباشرة بنجاح', 'success');
                    statusElement.innerHTML = `<div class="success">تم تعيين المستخدم كمشرف بنجاح</div>`;
                } catch (directError) {
                    log(`خطأ في التحديث المباشر: ${directError.message}`, 'error');
                }
            } finally {
                makeAdminBtn.disabled = false;
            }
        }
        
        // إضافة مستمعي الأحداث للأزرار
        makeAdminBtn.addEventListener('click', makeAdmin);
        checkStatusBtn.addEventListener('click', checkAdminStatus);
        
        // التحقق من حالة المشرف عند تحميل الصفحة
        checkAdminStatus();
    </script>
</body>
</html>
