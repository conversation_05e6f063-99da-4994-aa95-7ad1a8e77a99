{"rules": {"banned-users": {".read": "auth !== null", ".write": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || root.child('users').child(auth.uid).child('uid').val() === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}, "users": {".read": "auth !== null", "$uid": {".read": "auth !== null", ".write": "$uid === auth.uid || auth !== null"}}, "rooms": {".read": "auth !== null", "$room_id": {".read": "auth !== null", ".write": "!data.exists() || data.child('admins').child(auth.uid).val() == true", "lastMessage": {".write": "auth !== null"}}}, "messages": {".read": "auth !== null", ".write": "auth !== null", ".indexOn": ["roomId", "author/uid"], "$message_id": {".read": "auth !== null", ".write": "auth !== null"}}, "status": {"$user_id": {".read": "auth !== null", ".write": "$user_id === auth.uid"}}, ".read": false, ".write": false}}