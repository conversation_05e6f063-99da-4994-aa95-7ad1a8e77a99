# Toika Chat - تطبيق دردشة متطور

هذا المشروع مبني باستخدام React و Firebase.

Toika Chat هو تطبيق دردشة في الوقت الفعلي حيث يمكن للمستخدم إجراء دردشة جماعية. يمكن للمستخدمين إرسال الرسائل لبعضهم البعض، ومشاركة الصور والمستندات وملفات الصوت حتى 5 ميجابايت. يدعم التطبيق مصادقة جوجل مما يعني أن المستخدم يمكنه تسجيل الدخول باستخدام حساب جوجل. يمكن للمستخدم رؤية معلومات المستخدمين الآخرين مثل الملف الشخصي وآخر حالة اتصال. يمكن للمستخدم إنشاء غرفة دردشة خاصة به أو الانضمام إلى أي غرفة دردشة متاحة وإجراء دردشة جماعية.

## Languages used

- HTML
- CSS
- JavaScript

## Tools/Library used

- React (v18.2.0)
- React router (v5.2.0)
- Firebase (v9.14.0)
- Rsuite (v5.22.1)
- Sass (v1.56.1)
- React mic (v12.4.6)
- Timeage react (v3.0.5)
- Use context selector (v1.4.1)

- For storing messages I have used firebase storage and firebase real-time database

- Deployed on firebase hoisting

## Features/Functionality

- Fully responsive app
- مصادقة المستخدم باستخدام جوجل (تسجيل الدخول باستخدام جوجل)
- Manage user profile (edit username, upload avatar)
- Create chat room
- Update chat room information
- Realtime chat message
- Send chat, like chat and delete own chat
- Send file (images, documents, audio) upto 5mb
- Oraganized chat date wise
- Display chat send time
- View user status (like if user is online or offlinne and last online time)
- View user information

## Author

- [Manish Tiwari](https://linkedin.com/in/wdmanisht)

## Screenshots

### Signin page

![Login-page](https://user-images.githubusercontent.com/46663132/202082943-0ea8cbfe-e3d4-4dbf-adb0-80e8156fe3f8.png)

### Google signin modal

![google-login](https://user-images.githubusercontent.com/46663132/202083059-95cbd0a1-67ff-460f-8967-0d903465d659.png)

### Home

![home](https://user-images.githubusercontent.com/46663132/202083004-c7cc39fa-09f9-4556-990c-2d8b265f4adc.png)

### Dashboard

![dashboard](https://user-images.githubusercontent.com/46663132/202083135-6d23036c-ab73-4791-97ed-c653f2347847.png)

### Chat

![chat-page](https://user-images.githubusercontent.com/46663132/202083172-3ed644aa-ebdf-428f-9dd4-8c865fe640e6.png)

*If you liked my project feel free give it a star*

*Thank you*

*Manish Tiwari*
