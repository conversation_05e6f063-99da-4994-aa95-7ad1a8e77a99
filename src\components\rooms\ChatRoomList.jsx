import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Loader, Nav } from "rsuite";
import { useRooms } from "../../context/rooms.context";
import RoomItem from "./RoomItem";
import TranslatedText from "../TranslatedText";

const ChatRoomList = ({ aboveElHeight }) => {
  const rooms = useRooms();
  const location = useLocation();

  return (
    <Nav
      appearance="subtle"
      vertical
      reversed
      className="overflow-y-scroll custom-scroll"
      style={{
        height: `calc(100% - ${aboveElHeight}px)`,
      }}
      activeKey={location.pathname}
    >
      {!rooms && (
        <Loader center vertical content={<TranslatedText text="loading" />} speed="slow" size="md" />
      )}
      {rooms &&
        rooms.length > 0 &&
        rooms.map((room) => (
          <Nav.Item eventKey={`/chat/${room.id}`} key={room.id}>
            <Link style={{ textDecoration: "none" }} to={`/chat/${room.id}`}>
              <RoomItem room={room} />
            </Link>
          </Nav.Item>
        ))}
    </Nav>
  );
};

export default ChatRoomList;
