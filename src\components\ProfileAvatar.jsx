import React from "react";
import { Avatar } from "rsuite";
import { getNameInitials } from "../misc/helpers";

const ProfileAvatar = ({ name, uid, showUid = false, circle = true, src, ...avatarProps }) => {
  // تعديل: جعل جميع الصور دائرية بغض النظر عن قيمة circle وتحسين حجم الصورة
  return (
    <div className="profile-avatar-container">
      <Avatar
        circle={true}
        className={`enhanced-avatar ${avatarProps.className || ''}`}
        style={{
          width: avatarProps.size || '5px', // تصغير حجم صورة الملف الشخصي إلى 5px
          height: avatarProps.size || '5px', // تصغير حجم صورة الملف الشخصي إلى 5px
          fontSize: avatarProps.size ? `calc(${avatarProps.size} * 0.4)` : '2px', // تصغير حجم الخط
          overflow: 'hidden',
          objectFit: 'cover',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '50%', // جعل الصورة دائرية
          border: '0.5px solid rgba(255, 255, 255, 0.1)' // تقليل سمك الحدود
        }}
        src={src}
        {...avatarProps}
      >
        {!src && getNameInitials(name)}
      </Avatar>
      {showUid && uid && (
        <div className="user-uid" style={{ fontSize: '10px', marginTop: '2px', color: '#666', textAlign: 'center' }}>
          {uid.substring(0, 8)}
        </div>
      )}
    </div>
  );
};

export default ProfileAvatar;
