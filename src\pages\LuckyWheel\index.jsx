import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Loader, Message, toaster, Divider } from 'rsuite';
import { useHistory } from 'react-router';
import { database, auth } from '../../misc/firebase.config';
import { ref, get, set, push, serverTimestamp, onValue, off } from 'firebase/database';
import { useProfile } from '../../context/profile.context';
import { useLanguage } from '../../context/language.context';
import WheelOfFortune from '../../components/WheelOfFortune';
import './luckywheel.scss';

const LuckyWheel = () => {
  const history = useHistory();
  const { profile } = useProfile();
  const { t } = useLanguage(); // استخدام دالة الترجمة
  const [loading, setLoading] = useState(true);
  const [spinning, setSpinning] = useState(false);
  const [tickets, setTickets] = useState(0);
  const [dailyTasks, setDailyTasks] = useState([]);
  const [rewards, setRewards] = useState([]);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [currentReward, setCurrentReward] = useState(null);
  const [wheelConfig, setWheelConfig] = useState(null);
  const currentUserUid = auth.currentUser.uid;
  const currentUserEmail = auth.currentUser.email;

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // دالة للعودة إلى الصفحة السابقة
  const handleBack = () => {
    history.push('/');
  };

  // تحميل بيانات المستخدم وتكوين العجلة
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // تحميل تكوين العجلة
        const wheelConfigRef = ref(database, '/lucky-wheel/config');
        const wheelConfigSnapshot = await get(wheelConfigRef);

        if (wheelConfigSnapshot.exists()) {
          const config = wheelConfigSnapshot.val();
          setWheelConfig(config);
          setRewards(config.rewards || []);

          // التحقق من حالة تفعيل العجلة
          if (config.enabled === false && !isOwner) {
            // إذا كانت العجلة معطلة وليس المستخدم هو المالك، قم بالعودة إلى الصفحة الرئيسية
            toaster.push(
              <Message type="info" closable duration={4000}>
                عجلة الحظ غير متاحة حالياً
              </Message>
            );
            history.push('/');
            return;
          }
        } else if (isOwner) {
          // إنشاء تكوين افتراضي للعجلة بالجوائز المحددة
          const defaultRewards = [
            { id: 1, text: 'لا شيء', color: '#DC143C', probability: 40 },
            { id: 2, text: 'إعادة السحب', color: '#3CB371', probability: 40 },
            { id: 3, text: 'إعلان مجاني', color: '#FF8C00', probability: 8 },
            { id: 4, text: 'غرفة أدمن مجاني', color: '#9370DB', probability: 2 }
          ];

          await set(wheelConfigRef, {
            rewards: defaultRewards,
            enabled: true
          });

          setWheelConfig({ rewards: defaultRewards });
          setRewards(defaultRewards);
        }

        // تحميل المهام اليومية
        const tasksRef = ref(database, '/lucky-wheel/tasks');
        const tasksSnapshot = await get(tasksRef);

        let tasksData = {};
        if (tasksSnapshot.exists()) {
          tasksData = tasksSnapshot.val();
        } else if (isOwner) {
          // إنشاء مهام افتراضية إذا كان المستخدم هو المالك
          const defaultTasks = {
            task1: {
              title: t('dailyLogin'),
              description: t('dailyLoginDesc'),
              reward: 1,
              type: 'login'
            },
            task2: {
              title: t('shareApp'),
              description: t('shareAppDesc'),
              reward: 2,
              type: 'share'
            }
          };

          await set(tasksRef, defaultTasks);
          tasksData = defaultTasks;
        }

        // تحميل بيانات تذاكر المستخدم
        const userTicketsRef = ref(database, `/lucky-wheel/users/${currentUserUid}/tickets`);
        const userTicketsSnapshot = await get(userTicketsRef);

        if (userTicketsSnapshot.exists()) {
          setTickets(userTicketsSnapshot.val());
        } else {
          // إنشاء سجل جديد للمستخدم إذا لم يكن موجودًا
          // تعيين بطاقة واحدة (1) بدلاً من صفر (0) للمستخدمين الجدد
          await set(userTicketsRef, 1);
          setTickets(1);
        }

        // تحميل حالة إكمال المهام للمستخدم
        if (Object.keys(tasksData).length > 0) {
          const userTasksRef = ref(database, `/lucky-wheel/users/${currentUserUid}/tasks`);
          const userTasksSnapshot = await get(userTasksRef);
          const userTasks = userTasksSnapshot.exists() ? userTasksSnapshot.val() : {};

          // دمج بيانات المهام مع حالة إكمال المستخدم
          const tasksWithStatus = Object.keys(tasksData).map(taskId => {
            const task = tasksData[taskId];
            const userTask = userTasks[taskId] || {};
            const today = new Date().toDateString();

            return {
              id: taskId,
              ...task,
              completed: userTask.lastCompleted === today,
              lastCompleted: userTask.lastCompleted || null
            };
          });

          setDailyTasks(tasksWithStatus);

          // إكمال مهمة تسجيل الدخول اليومي تلقائيًا
          const loginTask = tasksWithStatus.find(task => task.type === 'login');
          if (loginTask && !loginTask.completed) {
            completeTask(loginTask.id);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error('Error loading user data:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            حدث خطأ أثناء تحميل البيانات
          </Message>
        );
        setLoading(false);
      }
    };

    loadUserData();

    // الاستماع للتغييرات في تكوين العجلة
    const wheelConfigRef = ref(database, '/lucky-wheel/config');
    onValue(wheelConfigRef, (snapshot) => {
      if (snapshot.exists()) {
        const config = snapshot.val();
        setWheelConfig(config);
        setRewards(config.rewards || []);
      }
    });

    return () => {
      // إلغاء الاستماع عند إلغاء تحميل المكون
      off(wheelConfigRef);
    };
  }, [currentUserUid, isOwner]);

  // معالجة انتهاء تدوير العجلة
  const handleSpinEnd = (selectedSegment) => {
    try {
      console.log("تم انتهاء التدوير، الجائزة المختارة:", selectedSegment);

      // تحديد الجائزة المختارة من قاعدة البيانات
      let selectedReward;

      if (rewards && rewards.length > 0) {
        selectedReward = rewards.find(reward => reward.text === selectedSegment.text);

        // إذا لم يتم العثور على الجائزة في قاعدة البيانات، استخدم الجائزة من العجلة
        if (!selectedReward) {
          const rewardId = selectedSegment.text === 'لا شيء' ? 1 :
                          selectedSegment.text === 'إعادة السحب' ? 2 :
                          selectedSegment.text === 'إعلان مجاني' ? 3 : 4;

          selectedReward = {
            id: rewardId,
            text: selectedSegment.text,
            color: selectedSegment.color,
            probability: parseInt(selectedSegment.probability)
          };
        }
      } else {
        // إذا لم تكن هناك جوائز في قاعدة البيانات، استخدم الجائزة من العجلة
        const rewardId = selectedSegment.text === 'لا شيء' ? 1 :
                        selectedSegment.text === 'إعادة السحب' ? 2 :
                        selectedSegment.text === 'إعلان مجاني' ? 3 : 4;

        selectedReward = {
          id: rewardId,
          text: selectedSegment.text,
          color: selectedSegment.color,
          probability: parseInt(selectedSegment.probability)
        };
      }

      // معالجة خاصة للجوائز
      if (selectedSegment.text === 'إعادة السحب') {
        // إعادة التذكرة للمستخدم فقط (بطاقة واحدة)
        const updatedTickets = tickets + 1; // إضافة بطاقة واحدة فقط للتعويض
        set(ref(database, `/lucky-wheel/users/${currentUserUid}/tickets`), updatedTickets);
        setTickets(updatedTickets);

        // إظهار رسالة للمستخدم
        toaster.push(
          <Message type="success" closable duration={4000}>
            {t('congratulations')}! {t('spinAgainTicketReturned')}
          </Message>
        );
      }

      // إنشاء رمز فريد للجائزة
      const rewardCode = generateRewardCode();

      // حفظ الجائزة في قاعدة البيانات
      const rewardRef = push(ref(database, `/lucky-wheel/users/${currentUserUid}/rewards`));
      set(rewardRef, {
        rewardId: selectedReward.id,
        rewardText: selectedReward.text,
        code: rewardCode,
        timestamp: serverTimestamp(),
        claimed: false
      });

      // عرض الجائزة
      setCurrentReward({
        ...selectedReward,
        code: rewardCode
      });

      // إظهار نافذة الجائزة
      setShowRewardModal(true);
      setSpinning(false);
    } catch (error) {
      console.error("خطأ في معالجة انتهاء التدوير:", error);
      setSpinning(false);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء معالجة الجائزة
        </Message>
      );
    }
  };

  // مرجع للعجلة
  const wheelComponentRef = React.useRef(null);

  // دالة لتدوير العجلة
  const spinWheel = async () => {
    if (spinning || tickets <= 0) return;

    try {
      console.log("بدء تدوير العجلة من الصفحة الرئيسية");

      // خصم تذكرة
      const newTickets = tickets - 1;
      await set(ref(database, `/lucky-wheel/users/${currentUserUid}/tickets`), newTickets);
      setTickets(newTickets);

      // تعيين حالة التدوير إلى true
      setSpinning(true);

      // استدعاء دالة تدوير العجلة في المكون
      if (wheelComponentRef.current) {
        console.log("استدعاء دالة تدوير العجلة في المكون");
        wheelComponentRef.current.spin();
      } else {
        console.error("لم يتم العثور على مرجع العجلة");
      }
    } catch (error) {
      console.error('Error spinning wheel:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تدوير العجلة
        </Message>
      );
      setSpinning(false);
    }
  };

  // دالة لإنشاء رمز فريد للجائزة
  const generateRewardCode = () => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';

    for (let i = 0; i < 8; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      code += characters.charAt(randomIndex);
    }

    return code;
  };

  // دالة لإكمال مهمة
  const completeTask = async (taskId) => {
    try {
      const task = dailyTasks.find(t => t.id === taskId);

      if (!task || task.completed) return;

      const today = new Date().toDateString();

      // تحديث حالة المهمة
      set(ref(database, `/lucky-wheel/users/${currentUserUid}/tasks/${taskId}`), {
        lastCompleted: today
      });

      // إضافة التذاكر - إضافة المزيد من التذاكر للمالك
      let rewardAmount = task.reward;
      if (currentUserUid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || currentUserEmail === '<EMAIL>') {
        rewardAmount = task.reward * 100; // إضافة 100 ضعف للمالك
      }

      const newTickets = tickets + rewardAmount;
      set(ref(database, `/lucky-wheel/users/${currentUserUid}/tickets`), newTickets);

      // تحديث الحالة المحلية
      setTickets(newTickets);
      setDailyTasks(dailyTasks.map(t =>
        t.id === taskId ? { ...t, completed: true, lastCompleted: today } : t
      ));

      toaster.push(
        <Message type="success" closable duration={4000}>
          {t('taskCompletedSuccess')} {rewardAmount} {t('ticket')}
        </Message>
      );
    } catch (error) {
      console.error('Error completing task:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          {t('errorCompletingTask')}
        </Message>
      );
    }
  };

  // دالة لمشاركة التطبيق
  const shareApp = async () => {
    try {
      const shareTask = dailyTasks.find(task => task.type === 'share');

      if (!shareTask || shareTask.completed) return;

      if (navigator.share) {
        await navigator.share({
          title: 'تطبيق الدردشة',
          text: 'انضم إلينا في تطبيق الدردشة الرائع!',
          url: window.location.origin
        });

        // إكمال المهمة بعد المشاركة
        completeTask(shareTask.id);
      } else {
        // نسخ الرابط إلى الحافظة إذا كانت واجهة المشاركة غير متوفرة
        await navigator.clipboard.writeText(window.location.origin);

        toaster.push(
          <Message type="info" closable duration={4000}>
            تم نسخ الرابط إلى الحافظة
          </Message>
        );

        // إكمال المهمة بعد النسخ
        completeTask(shareTask.id);
      }
    } catch (error) {
      console.error('Error sharing app:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء مشاركة التطبيق
        </Message>
      );
    }
  };

  // دالة للانتقال إلى صفحة إدارة العجلة (للمالك فقط)
  const goToWheelAdmin = () => {
    history.push('/lucky-wheel-admin');
  };

  // دالة لترجمة نصوص الجوائز
  const getLocalizedRewardText = (rewardText) => {
    // ترجمة نصوص الجوائز حسب اللغة المختارة
    if (rewardText === 'لا شيء') {
      return t('nothingReward');
    } else if (rewardText === 'إعادة السحب') {
      return t('spinAgainReward');
    } else if (rewardText === 'إعلان مجاني') {
      return t('freeAdReward');
    } else if (rewardText === 'غرفة أدمن') {
      return t('adminRoomReward');
    }
    return rewardText;
  };

  if (loading) {
    return (
      <div className="lucky-wheel-page">
        <Loader center vertical size="md" content={t('loading')} speed="slow" />
      </div>
    );
  }



  return (
    <div className="lucky-wheel-page">
      <div className="lucky-wheel-header">
        <Button appearance="subtle" onClick={handleBack} className="back-button">
          <i className="fas fa-arrow-right"></i> {t('back')}
        </Button>
        <h2>{t('luckyWheel')}</h2>
        {isOwner && (
          <Button appearance="primary" color="violet" onClick={goToWheelAdmin} size="sm">
            {t('manageWheel')}
          </Button>
        )}
      </div>

      <div className="lucky-wheel-content">
        <div className="tickets-counter">
          <i className="fas fa-ticket-alt"></i>
          <span>{t('availableTickets')}: {tickets}</span>
        </div>

        <WheelOfFortune
          ref={wheelComponentRef}
          onSpinEnd={handleSpinEnd}
          isSpinning={spinning}
          setIsSpinning={setSpinning}
        />

        <Button
          appearance="primary"
          color="green"
          size="lg"
          style={{ width: '80%', maxWidth: '250px' }}
          disabled={spinning || tickets <= 0}
          onClick={spinWheel}
          className={`spin-button ${tickets <= 0 ? 'disabled-button' : ''}`}
        >
          {spinning ? t('spinning') : tickets <= 0 ? t('completeTasksForTickets') : t('spin')}
        </Button>

        <Divider style={{ margin: '15px 0', width: '90%' }}>{t('dailyTasks')}</Divider>

        <div className="daily-tasks">
          {dailyTasks.map(task => (
            <div key={task.id} className={`task-card ${task.completed ? 'completed' : ''}`}>
              <div className="task-content">
                <h4>{task.title}</h4>
                <p>{task.description}</p>
                <div className="task-reward">
                  <i className="fas fa-ticket-alt"></i>
                  <span>{task.reward} {t('ticket')}</span>
                </div>
              </div>
              <Button
                appearance="primary"
                color="blue"
                disabled={task.completed}
                onClick={() => task.type === 'share' ? shareApp() : completeTask(task.id)}
                className="task-button"
                size="sm"
              >
                {task.completed ? t('taskCompleted') : t('completeTask')}
              </Button>
            </div>
          ))}
        </div>
      </div>

      <Modal open={showRewardModal} onClose={() => setShowRewardModal(false)} className="reward-modal" size="xs">
        <Modal.Header>
          <Modal.Title>{t('congratulations')} 🎉</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentReward && (
            <div className="reward-details">
              <div className="reward-icon">
                <i className="fas fa-gift"></i>
              </div>
              <h3>{getLocalizedRewardText(currentReward.text)}</h3>

              {currentReward.text === 'إعادة السحب' && (
                <div className="bonus-info" style={{ margin: '10px 0', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px', textAlign: 'center' }}>
                  <p style={{ color: '#28a745', fontWeight: 'bold' }}>
                    <i className="fas fa-redo" style={{ marginLeft: '5px' }}></i>
                    {t('spinAgainTicketReturned')}
                  </p>
                </div>
              )}

              {currentReward.text !== 'لا شيء' && currentReward.text !== 'إعادة السحب' && (
                <div className="reward-code">
                  <p>{t('rewardCode')}:</p>
                  <div className="code-box">{currentReward.code}</div>
                  <p className="code-hint">{t('keepCodeForSupport')}</p>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowRewardModal(false)} appearance="primary" color="green" block>
            {t('close')}
          </Button>
        </Modal.Footer>
      </Modal>

    </div>
  );
};

export default LuckyWheel;
