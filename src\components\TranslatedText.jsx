import React from 'react';
import { useLanguage } from '../context/language.context';

/**
 * مكون لعرض النص المترجم بالتنسيق المناسب للغة الحالية
 * يستخدم دالة الترجمة t() ويضيف فئة CSS مناسبة للغة
 */
const TranslatedText = ({ text, className = '', ...props }) => {
  const { t, currentLanguage, LANGUAGES } = useLanguage();
  
  // تحديد فئة CSS بناءً على اللغة الحالية
  let langClass = '';
  if (currentLanguage === LANGUAGES.AR) {
    langClass = 'ar-text';
  } else if (currentLanguage === LANGUAGES.KU) {
    langClass = 'ku-text';
  }
  
  // دمج الفئات
  const combinedClassName = `${langClass} ${className}`.trim();
  
  return (
    <span className={combinedClassName} {...props}>
      {t(text)}
    </span>
  );
};

export default TranslatedText;
