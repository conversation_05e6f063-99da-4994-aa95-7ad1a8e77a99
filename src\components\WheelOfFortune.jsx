import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';

const WheelOfFortune = forwardRef(({ onSpinEnd, isSpinning, setIsSpinning }, ref) => {
  const canvasRef = useRef(null);
  const wheelRef = useRef(null);

  // تعريف الجوائز
  const segments = [
    { text: 'لا شيء', probability: '30%', color: '#DC143C' },
    { text: 'إعادة السحب', probability: '25%', color: '#3CB371' },
    { text: 'إعلان مجاني', probability: '14%', color: '#FF8C00' },
    { text: 'غرفة أدمن', probability: '1%', color: '#9370DB' }
  ];

  // رسم العجلة
  const drawWheel = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // تحديد حجم العجلة ليناسب شاشات الجوال
    const size = Math.min(window.innerWidth - 40, 300);
    canvas.width = size;
    canvas.height = size;

    const centerX = size / 2;
    const centerY = size / 2;
    const radius = Math.min(centerX, centerY) - 10;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // رسم القطاعات
    for (let i = 0; i < segments.length; i++) {
      const startAngle = (i * Math.PI / 2);
      const endAngle = ((i + 1) * Math.PI / 2);

      // رسم القطاع
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fillStyle = segments[i].color;
      ctx.fill();
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 2;
      ctx.stroke();

      // إضافة النص للقطاع
      const textAngle = startAngle + (Math.PI / 4);
      const textDistance = radius * 0.6; // المسافة من المركز
      const textX = centerX + textDistance * Math.cos(textAngle);
      const textY = centerY + textDistance * Math.sin(textAngle);

      // حفظ حالة الرسم الحالية
      ctx.save();

      // نقل نقطة الأصل إلى موقع النص
      ctx.translate(textX, textY);

      // تدوير النص ليتناسب مع القطاع
      ctx.rotate(textAngle + Math.PI/2);

      // كتابة النص
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // تحديد حجم الخط بناءً على حجم الشاشة
      const fontSize = Math.max(12, Math.min(14, size / 20));
      ctx.font = `bold ${fontSize}px Arial`;

      // إضافة ظل للنص لتحسين القراءة
      ctx.shadowColor = 'rgba(255, 255, 255, 0.7)';
      ctx.shadowBlur = 3;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;

      // كتابة النص بلون أسود
      ctx.fillStyle = '#000000';

      // كتابة النص على سطرين
      ctx.fillText(segments[i].text, 0, -8);
      ctx.fillText(segments[i].probability, 0, 10);

      // استعادة حالة الرسم السابقة
      ctx.restore();
    }

    // رسم دائرة مركزية
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.2, 0, 2 * Math.PI);
    ctx.fillStyle = '#333333';
    ctx.fill();
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;
    ctx.stroke();

    // إضافة نص في المركز
    ctx.textAlign = 'center';
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('عجلة', centerX, centerY - 5);
    ctx.fillText('الحظ', centerX, centerY + 15);

    // رسم المؤشر
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - radius - 10);
    ctx.lineTo(centerX - 10, centerY - radius + 5);
    ctx.lineTo(centerX + 10, centerY - radius + 5);
    ctx.closePath();
    ctx.fillStyle = '#FF0000';
    ctx.fill();
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;
    ctx.stroke();
  };

  // تدوير العجلة
  const spinWheel = () => {
    // إذا كانت العجلة تدور بالفعل، لا تفعل شيئًا
    if (isSpinning && !wheelRef.current.style.transform.includes('rotate(0deg)')) {
      console.log("العجلة تدور بالفعل");
      return;
    }

    console.log("بدء تدوير العجلة من دالة spinWheel");

    // تعيين حالة التدوير إلى true
    if (!isSpinning) {
      setIsSpinning(true);
    }

    // اختيار قطاع عشوائي باستخدام الاحتمالات
    // تحويل النسب المئوية إلى أرقام
    const probabilities = segments.map(segment => {
      return parseInt(segment.probability.replace('%', ''));
    });

    // حساب مجموع الاحتمالات
    const totalProbability = probabilities.reduce((sum, prob) => sum + prob, 0);

    // اختيار قطاع عشوائي بناءً على الاحتمالات
    const randomValue = Math.random() * totalProbability;
    let cumulativeProbability = 0;
    let selectedSegmentIndex = 0;

    for (let i = 0; i < probabilities.length; i++) {
      cumulativeProbability += probabilities[i];
      if (randomValue <= cumulativeProbability) {
        selectedSegmentIndex = i;
        break;
      }
    }

    console.log("القطاع المختار:", segments[selectedSegmentIndex]);

    // حساب زاوية الدوران
    const spinDuration = 5000; // 5 ثوانٍ
    const spinStartTime = Date.now();
    const segmentAngle = 360 / segments.length;

    // حساب الزاوية المستهدفة بحيث يتوقف المؤشر على القطاع المختار
    const targetAngle = (segmentAngle * selectedSegmentIndex);

    // 8 دورات كاملة + موضع القطاع المستهدف
    const totalRotation = 360 * 8 + targetAngle;

    // إعادة تعيين موضع العجلة
    if (wheelRef.current) {
      wheelRef.current.style.transform = 'rotate(0deg)';
    }

    // دالة لتحريك العجلة
    const animateSpin = () => {
      const elapsed = Date.now() - spinStartTime;
      const progress = Math.min(elapsed / spinDuration, 1);

      // تأثير التباطؤ التدريجي للعجلة
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const currentRotation = easedProgress * totalRotation;

      if (wheelRef.current) {
        // تدوير العجلة في اتجاه عقارب الساعة
        wheelRef.current.style.transform = `rotate(${currentRotation}deg)`;
      }

      if (progress < 1) {
        requestAnimationFrame(animateSpin);
      } else {
        // انتهاء التدوير
        console.log("انتهاء التدوير، استدعاء onSpinEnd");
        setTimeout(() => {
          onSpinEnd(segments[selectedSegmentIndex]);
        }, 500);
      }
    };

    // بدء تحريك العجلة
    requestAnimationFrame(animateSpin);
  };

  // إتاحة دالة تدوير العجلة للمكون الأب
  useImperativeHandle(ref, () => ({
    spin: () => {
      spinWheel();
    }
  }));

  // رسم العجلة عند تحميل المكون
  useEffect(() => {
    drawWheel();

    // إعادة رسم العجلة عند تغيير حجم النافذة
    const handleResize = () => {
      drawWheel();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // تدوير العجلة عند تغيير قيمة isSpinning
  useEffect(() => {
    if (isSpinning) {
      console.log("بدء تدوير العجلة من المكون");
      // استدعاء دالة التدوير مباشرة
      spinWheel();
    }
  }, [isSpinning]);

  return (
    <div className="wheel-container" ref={wheelRef}>
      <canvas ref={canvasRef} className="wheel-canvas"></canvas>
    </div>
  );
});

export default WheelOfFortune;
