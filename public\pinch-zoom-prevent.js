// منع التكبير والتصغير باستخدام حركة التقريب فقط
// هذا الملف يمنع حركة التقريب (pinch zoom) مع السماح بالتمرير والنسخ

(function() {
  // تحديد ما إذا كان الجهاز يعمل بنظام iOS
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  
  // منع حركة التقريب فقط
  document.addEventListener('gesturestart', function(event) {
    // منع حركة التقريب فقط
    if (event.scale !== 1) {
      event.preventDefault();
    }
  }, { passive: false });
  
  document.addEventListener('gesturechange', function(event) {
    // منع حركة التقريب فقط
    if (event.scale !== 1) {
      event.preventDefault();
    }
  }, { passive: false });
  
  document.addEventListener('gestureend', function(event) {
    // منع حركة التقريب فقط
    if (event.scale !== 1) {
      event.preventDefault();
    }
  }, { passive: false });
  
  // إضافة معالجات خاصة لأجهزة iOS
  if (isIOS) {
    // منع التكبير عند النقر المزدوج على iOS
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
      const now = Date.now();
      if (now - lastTouchEnd <= 300 && event.touches.length > 1) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, { passive: false });
  }
})();
