<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مشرف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #1DA1F2;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #3a3b3c;
            background-color: #2d2d2d;
            color: #e4e6eb;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: none;
            background-color: #1DA1F2;
            color: white;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #1a91da;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background-color: #2d2d2d;
            display: none;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
        .admin-list {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background-color: #2d2d2d;
        }
        .admin-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            background-color: #3a3b3c;
        }
        .admin-uid {
            font-family: monospace;
            color: #1DA1F2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إضافة مشرف</h1>
        <div class="form-group">
            <label for="email">البريد الإلكتروني للمالك:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="password">كلمة المرور للمالك:</label>
            <input type="password" id="password" placeholder="أدخل كلمة المرور">
        </div>
        <div class="form-group">
            <label for="uid">معرف المستخدم (UID):</label>
            <input type="text" id="uid" placeholder="أدخل معرف المستخدم (UID)" value="HdgvqlLyeagYNofjTtYGefH2wjD3">
        </div>
        <button id="login">تسجيل الدخول وتعيين كمشرف</button>
        <div id="result" class="result"></div>

        <div class="admin-list">
            <h3>المشرفين الحاليين:</h3>
            <div id="adminsList"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const uidInput = document.getElementById('uid');
        const loginButton = document.getElementById('login');
        const resultDiv = document.getElementById('result');
        const adminsListDiv = document.getElementById('adminsList');

        // تحقق مما إذا كان المستخدم مسجل الدخول بالفعل
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                console.log("تم تسجيل الدخول بالفعل:", user.email);
                loadAdmins();
            }
        });

        // تحميل قائمة المشرفين
        function loadAdmins() {
            firebase.database().ref('users').on('value', (snapshot) => {
                adminsListDiv.innerHTML = '';
                if (snapshot.exists()) {
                    const users = snapshot.val();
                    let hasAdmins = false;

                    for (const [uid, userData] of Object.entries(users)) {
                        if (userData.isAdmin) {
                            hasAdmins = true;
                            const adminItem = document.createElement('div');
                            adminItem.className = 'admin-item';
                            adminItem.innerHTML = `
                                <div><strong>${userData.name || 'مستخدم غير معروف'}</strong></div>
                                <div class="admin-uid">${uid}</div>
                            `;
                            adminsListDiv.appendChild(adminItem);
                        }
                    }

                    if (!hasAdmins) {
                        adminsListDiv.innerHTML = '<p>لا يوجد مشرفين حاليًا</p>';
                    }
                } else {
                    adminsListDiv.innerHTML = '<p>لا يوجد مستخدمين في قاعدة البيانات</p>';
                }
            });
        }

        // إضافة مستمع حدث للزر
        loginButton.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            const uid = uidInput.value.trim();

            if (!uid) {
                showResult('يرجى إدخال معرف المستخدم (UID)', false);
                return;
            }

            try {
                // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
                await firebase.auth().signInWithEmailAndPassword(email, password);
                console.log("تم تسجيل الدخول بنجاح");

                // التحقق من وجود المستخدم
                const userSnapshot = await firebase.database().ref(`users/${uid}`).once('value');

                if (!userSnapshot.exists()) {
                    showResult('لم يتم العثور على مستخدم بهذا المعرف', false);
                    return;
                }

                // تعيين المستخدم كمشرف
                await firebase.database().ref(`users/${uid}/isAdmin`).set(true);

                const userData = userSnapshot.val();
                showResult(`تم تعيين المستخدم ${userData.name || uid} كمشرف بنجاح`, true);

                // تحديث قائمة المشرفين
                loadAdmins();
            } catch (error) {
                console.error("خطأ:", error);
                showResult(`خطأ: ${error.message}`, false);
            }
        });

        // عرض نتيجة العملية
        function showResult(message, isSuccess) {
            resultDiv.textContent = message;
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            resultDiv.style.display = 'block';

            // إخفاء الرسالة بعد 5 ثوانٍ
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
