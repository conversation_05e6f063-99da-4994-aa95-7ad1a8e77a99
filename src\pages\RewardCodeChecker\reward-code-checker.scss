.reward-code-checker-page {
  padding: 20px;
  min-height: 100vh;
  background-color: #121212;
  color: #e4e6eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 80px; // للتأكد من عدم تداخل المحتوى مع شريط التنقل السفلي
}

.checker-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    color: #e4e6eb;
  }

  .back-button {
    color: #e4e6eb;

    &:hover, &:focus {
      color: #ffffff;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.checker-content {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.search-section {
  width: 100%;
  margin-bottom: 20px;

  .rs-input-group {
    width: 100%;

    .rs-input {
      background-color: rgba(255, 255, 255, 0.1);
      color: #e4e6eb;
      border-color: rgba(255, 255, 255, 0.2);

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        border-color: #3498ff;
      }
    }

    .rs-input-group-btn {
      background-color: #3498ff;
      color: #ffffff;

      &:hover, &:focus {
        background-color: darken(#3498ff, 10%);
      }
    }
  }

  .mt-3 {
    margin-top: 15px;
  }
}

.result-section {
  width: 100%;
  margin-top: 20px;

  .rs-panel {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);

    .rs-panel-header {
      color: #e4e6eb;
      background-color: rgba(255, 255, 255, 0.1);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .rs-panel-body {
      color: #e4e6eb;
    }
  }
}

.reward-info {
  display: flex;
  flex-direction: column;
  gap: 10px;

  > div {
    padding: 8px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.05);

    strong {
      margin-left: 5px;
      color: #3498ff;
    }
  }

  .reward-code {
    font-weight: bold;
    font-size: 16px;
    letter-spacing: 1px;
  }

  .reward-type {
    color: #ffc107;
  }

  .reward-status {
    .claimed-status {
      color: #4caf50;
    }

    .unclaimed-status {
      color: #f44336;
    }
  }

  .reward-actions {
    margin-top: 5px;

    button {
      font-weight: bold;

      i {
        margin-left: 5px;
      }
    }
  }
}

.all-rewards-list {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .rs-panel {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;

    .rs-panel-header {
      color: #e4e6eb;
      background-color: rgba(255, 255, 255, 0.1);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .rs-panel-body {
      color: #e4e6eb;
    }
  }
}

.no-rewards {
  text-align: center;
  padding: 20px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.5);
}

// تنسيق النافذة المنبثقة
.rs-modal-full {
  .rs-modal-content {
    background-color: #121212;

    .rs-modal-header {
      border-bottom: 1px solid #3a3b3c;

      .rs-modal-title {
        color: #e4e6eb;
      }
    }

    .rs-modal-body {
      color: #e4e6eb;
    }

    .rs-modal-footer {
      border-top: 1px solid #3a3b3c;
    }
  }
}
