import React, { useState, useEffect } from 'react';
import { ref, onValue } from 'firebase/database';
import { database, auth } from '../../../misc/firebase.config';
import { useParams } from 'react-router';

const TypingIndicator = () => {
  const [typingUsers, setTypingUsers] = useState([]);
  const { chatId } = useParams();
  const currentUser = auth.currentUser;

  useEffect(() => {
    if (!chatId) return;

    // مراقبة حالة الكتابة في الغرفة الحالية
    const typingRef = ref(database, `/rooms/${chatId}/typing`);

    const unsubscribe = onValue(typingRef, (snapshot) => {
      if (snapshot.exists()) {
        const typingData = snapshot.val();
        const users = [];
        const now = Date.now();

        // تحويل بيانات الكتابة إلى مصفوفة من المستخدمين
        Object.keys(typingData).forEach(uid => {
          // لا تضف المستخدم الحالي إلى القائمة
          if (uid !== currentUser.uid) {
            // تحقق من الطابع الزمني - إذا كان أقدم من 5 دقائق، تجاهله
            // هذا فقط كإجراء أمان في حالة عدم إزالة حالة الكتابة بشكل صحيح
            const timestamp = typingData[uid].timestamp;
            const typingTime = timestamp ? new Date(timestamp).getTime() : 0;

            // زيادة المدة إلى 5 دقائق (300000 مللي ثانية)
            if (!timestamp || (now - typingTime) < 300000) {
              users.push({
                uid,
                name: typingData[uid].name,
                timestamp: typingTime || 0,
                text: typingData[uid].text || ''
              });
            }
          }
        });

        // ترتيب المستخدمين حسب الطابع الزمني (الأحدث أولاً)
        users.sort((a, b) => {
          return b.timestamp - a.timestamp;
        });

        // تحديد حالة المستخدمين الذين يكتبون
        setTypingUsers(users);
      } else {
        setTypingUsers([]);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [chatId, currentUser]);

  // إذا لم يكن هناك مستخدمون يكتبون، لا تعرض شيئاً
  if (typingUsers.length === 0) {
    return null;
  }

  // عرض أسماء المستخدمين الذين يكتبون (بحد أقصى 4)
  const displayUsers = typingUsers.slice(0, 4);

  // تنسيق النص حسب عدد المستخدمين
  let typingText = '';
  if (displayUsers.length === 1) {
    typingText = `${displayUsers[0].name} يكتب...`;
  } else {
    const names = displayUsers.map(user => user.name);
    typingText = `${names.join(', ')} يكتبون...`;
  }

  return (
    <div className="typing-indicator typing-indicator-top">
      {typingText}
    </div>
  );
};

export default TypingIndicator;
