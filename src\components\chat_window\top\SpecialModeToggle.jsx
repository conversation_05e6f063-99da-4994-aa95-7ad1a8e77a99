import React, { useState, useEffect } from 'react';
import { But<PERSON>, Message, toaster, Toggle } from 'rsuite';
import { ref, get, set } from 'firebase/database';
import { database, auth } from '../../../misc/firebase.config';
import { useParams } from 'react-router';

const SpecialModeToggle = () => {
  const { chatId } = useParams();
  const [isSpecialMode, setIsSpecialMode] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // التحقق مما إذا كان المستخدم هو المالك
  useEffect(() => {
    const checkOwnerStatus = () => {
      if (auth.currentUser) {
        const currentUserId = auth.currentUser.uid;
        const isOwnerUser = currentUserId === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
                           auth.currentUser.email === '<EMAIL>';
        setIsOwner(isOwnerUser);
      }
    };

    checkOwnerStatus();
  }, []);

  // التحقق من حالة الوضع الخاص للمجموعة
  useEffect(() => {
    if (!chatId) return;

    const loadSpecialModeStatus = async () => {
      try {
        const roomRef = ref(database, `/rooms/${chatId}`);
        const snapshot = await get(roomRef);

        if (snapshot.exists()) {
          const roomData = snapshot.val();
          setIsSpecialMode(roomData.isSpecialMode === true);
        }
      } catch (error) {
        console.error('Error loading special mode status:', error);
      }
    };

    loadSpecialModeStatus();
  }, [chatId]);

  // تبديل حالة الوضع الخاص
  const toggleSpecialMode = async () => {
    if (!isOwner) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          فقط مالك الموقع يمكنه تفعيل الوضع الخاص
        </Message>
      );
      return;
    }

    setIsLoading(true);

    try {
      const newStatus = !isSpecialMode;
      await set(ref(database, `/rooms/${chatId}/isSpecialMode`), newStatus);
      setIsSpecialMode(newStatus);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم {newStatus ? 'تفعيل' : 'إلغاء تفعيل'} الوضع الخاص بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error toggling special mode:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تغيير حالة الوضع الخاص: {error.message}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  // إذا لم يكن المستخدم هو المالك، لا تعرض الزر
  if (!isOwner) {
    return null;
  }

  return (
    <div className="special-mode-toggle">
      <Button
        id="special-mode-btn"
        appearance="subtle"
        onClick={toggleSpecialMode}
        disabled={isLoading}
        className={`room-btn ${isSpecialMode ? 'special-mode-active' : ''}`}
      >
        {isSpecialMode ? 'إلغاء الوضع الخاص' : 'تفعيل الوضع الخاص'}
      </Button>
    </div>
  );
};

export default SpecialModeToggle;
