import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;

class WebViewService {
  static const String baseUrl = 'https://toika-369.web.app';
  static WebViewController? _controller;
  static final StreamController<Map<String, dynamic>> _messageStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Get the stream of messages from the WebView
  static Stream<Map<String, dynamic>> get messageStream =>
      _messageStreamController.stream;

  // Initialize WebView controller
  static WebViewController initializeController() {
    // Create a WebView controller
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0x00000000))
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                // Update loading bar
                debugPrint('WebView is loading (progress: $progress%)');
              },
              onPageStarted: (String url) {
                debugPrint('Page started loading: $url');
              },
              onPageFinished: (String url) {
                debugPrint('Page finished loading: $url');
                // Inject JavaScript to listen for notifications
                _injectNotificationListener();
              },
              onWebResourceError: (WebResourceError error) {
                debugPrint('''
Page resource error:
  code: ${error.errorCode}
  description: ${error.description}
  errorType: ${error.errorType}
  isForMainFrame: ${error.isForMainFrame}
          ''');
              },
              onNavigationRequest: (NavigationRequest request) {
                // Handle external links
                if (!request.url.startsWith(baseUrl)) {
                  _launchExternalUrl(request.url);
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
            ),
          )
          ..addJavaScriptChannel(
            'Flutter',
            onMessageReceived: (JavaScriptMessage message) {
              // Handle messages from WebView
              _handleJavaScriptMessage(message.message);
            },
          )
          ..loadRequest(Uri.parse(baseUrl));

    return _controller!;
  }

  // Inject JavaScript to listen for notifications
  static void _injectNotificationListener() {
    if (_controller == null) return;

    // JavaScript code to listen for notifications
    const String script = '''
      // Listen for reply notifications
      document.addEventListener('replyNotification', function(event) {
        // Send notification data to Flutter
        Flutter.postMessage(JSON.stringify({
          type: 'replyNotification',
          data: event.detail
        }));
      });

      // Function to get user ID from the page
      function getUserId() {
        try {
          // Try to get user ID from Firebase Auth
          if (window.firebase && firebase.auth && firebase.auth().currentUser) {
            return firebase.auth().currentUser.uid;
          }

          // Try to get from localStorage
          if (localStorage.getItem('user_id')) {
            return localStorage.getItem('user_id');
          }

          return null;
        } catch (e) {
          console.error('Error getting user ID:', e);
          return null;
        }
      }

      // Function to send user ID to Flutter
      function sendUserIdToFlutter() {
        const userId = getUserId();
        if (userId) {
          Flutter.postMessage(JSON.stringify({
            type: 'userId',
            data: { userId: userId }
          }));
        }

        // Try again in 5 seconds if no user ID found
        if (!userId) {
          setTimeout(sendUserIdToFlutter, 5000);
        }
      }

      // Function to check for new messages
      function checkForNewMessages() {
        // Get user ID
        const userId = getUserId();

        // If user is logged in, check for new messages
        if (userId) {
          // Send user ID to Flutter for token registration
          Flutter.postMessage(JSON.stringify({
            type: 'userId',
            data: { userId: userId }
          }));

          // In a real implementation, you would check for new messages here
          // and trigger notifications when the app is in the background
        }

        // Check again in 30 seconds
        setTimeout(checkForNewMessages, 30000);
      }

      // Start checking for new messages
      checkForNewMessages();

      // Send user ID to Flutter immediately
      sendUserIdToFlutter();
    ''';

    _controller!.runJavaScript(script);
  }

  // Handle messages from JavaScript
  static void _handleJavaScriptMessage(String message) {
    try {
      final Map<String, dynamic> data = json.decode(message);
      _messageStreamController.add(data);

      // Handle different message types
      switch (data['type']) {
        case 'replyNotification':
          debugPrint('Received reply notification: ${data['data']}');
          break;
        case 'userId':
          final userId = data['data']['userId'];
          debugPrint('Received user ID: $userId');
          _handleUserId(userId);
          break;
        default:
          debugPrint('Received unknown message type: ${data['type']}');
      }
    } catch (e) {
      debugPrint('Error handling JavaScript message: $e');
    }
  }

  // Handle user ID from JavaScript
  static Future<void> _handleUserId(String userId) async {
    try {
      // Save user ID to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_id', userId);

      // Get FCM token
      final token = prefs.getString('fcm_token');

      // Register token with server if available
      if (token != null) {
        await _registerTokenWithServer(userId, token);
      }
    } catch (e) {
      debugPrint('Error handling user ID: $e');
    }
  }

  // Register token with server
  static Future<void> _registerTokenWithServer(
    String userId,
    String token,
  ) async {
    try {
      // Prepare the URL for the Firebase Realtime Database
      final url =
          'https://toika-369-default-rtdb.firebaseio.com/device_tokens/$userId.json';

      // Send the token to the server
      final response = await http.put(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'token': token,
          'platform': Platform.isAndroid ? 'android' : 'ios',
          'updated_at': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to register device token: ${response.body}');
      }

      debugPrint('Device token registered successfully with server');
    } catch (e) {
      debugPrint('Error registering device token with server: $e');
    }
  }

  // Launch external URL
  static Future<void> _launchExternalUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $url');
    }
  }

  // Execute JavaScript in WebView
  static Future<void> executeJavaScript(String script) async {
    if (_controller == null) return;
    await _controller!.runJavaScript(script);
  }

  // Get the current WebView controller
  static WebViewController? getController() {
    return _controller;
  }

  // Save user credentials
  static Future<void> saveUserCredentials(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('email', email);
    await prefs.setString('password', password);
  }

  // Get user credentials
  static Future<Map<String, String?>> getUserCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString('email');
    final password = prefs.getString('password');
    return {'email': email, 'password': password};
  }

  // Auto login to the website
  static Future<void> autoLogin() async {
    if (_controller == null) return;

    final credentials = await getUserCredentials();
    final email = credentials['email'];
    final password = credentials['password'];

    if (email != null && password != null) {
      // JavaScript to auto-login
      final script = '''
        // Wait for the login form to be available
        setTimeout(function() {
          // Fill in the email and password fields
          document.querySelector('input[type="email"]').value = '$email';
          document.querySelector('input[type="password"]').value = '$password';

          // Submit the form
          document.querySelector('form').submit();
        }, 2000);
      ''';

      await _controller!.runJavaScript(script);
    }
  }

  // Dispose the service
  static void dispose() {
    _messageStreamController.close();
  }
}
