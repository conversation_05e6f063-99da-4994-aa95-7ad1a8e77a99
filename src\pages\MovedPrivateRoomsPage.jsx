import React, { useEffect, useState } from 'react';
import { ref, get, set, remove, update } from 'firebase/database';
import { database, auth } from '../misc/firebase.config';
import { Container, Content, FlexboxGrid, Panel, List, Button, Message, toaster, Loader, Modal, Header } from 'rsuite';
import TimeAgo from 'timeago-react';
import { useProfile } from '../context/profile.context';
import ProfileAvatar from '../components/ProfileAvatar';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';
import { useHistory } from 'react-router-dom';

const MovedPrivateRoomsPage = () => {
  const { profile } = useProfile();
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [showMessagesModal, setShowMessagesModal] = useState(false);
  const [messages, setMessages] = useState([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const history = useHistory();

  // دالة للعودة إلى الصفحة السابقة
  const handleBack = () => {
    // التحقق من وجود صفحة سابقة في تاريخ التصفح
    if (window.history.length > 1) {
      // إذا كان هناك صفحة سابقة، استخدم history.goBack()
      history.goBack();
    } else {
      // إذا لم يكن هناك صفحة سابقة، انتقل إلى الصفحة الرئيسية
      history.push('/');
    }
  };

  // التحقق مما إذا كان المستخدم مشرفًا
  const isAdmin = profile && (
    profile.email === '<EMAIL>' ||
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2' ||
    profile.isAdmin === true
  );

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // إذا لم يكن المستخدم هو المالك، إعادة توجيهه إلى الصفحة الرئيسية
  useEffect(() => {
    if (!isOwner) {
      window.location.href = '/';
    }
  }, [isOwner]);

  // جلب المجموعات الخاصة المنقولة
  useEffect(() => {
    const fetchMovedPrivateRooms = async () => {
      try {
        setLoading(true);
        const movedRoomsRef = ref(database, 'moved-private-rooms');
        const snapshot = await get(movedRoomsRef);

        if (snapshot.exists()) {
          const roomsData = snapshot.val();
          const roomsArray = Object.keys(roomsData).map(key => ({
            id: key,
            ...roomsData[key]
          }));

          // ترتيب المجموعات حسب تاريخ النقل (الأحدث أولاً)
          roomsArray.sort((a, b) => {
            return new Date(b.movedAt || 0) - new Date(a.movedAt || 0);
          });

          setRooms(roomsArray);
        } else {
          setRooms([]);
        }
      } catch (error) {
        console.error('Error fetching moved private rooms:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في جلب المجموعات الخاصة المنقولة: {error.message}
          </Message>
        );
      } finally {
        setLoading(false);
      }
    };

    if (isOwner) {
      fetchMovedPrivateRooms();
    }
  }, [isOwner]);

  // عرض رسائل المجموعة المحددة
  const handleViewMessages = async (room) => {
    try {
      setSelectedRoom(room);
      setShowMessagesModal(true);
      setLoadingMessages(true);
      setMessages([]);

      // جلب الرسائل من قسم رسائل المجموعات الخاصة المنقولة
      const messagesRef = ref(database, 'moved-private-rooms-messages');
      const snapshot = await get(messagesRef);

      if (snapshot.exists()) {
        const messagesData = snapshot.val();
        const messagesArray = [];

        // البحث عن الرسائل المرتبطة بالمجموعة المحددة
        for (const messageId in messagesData) {
          const message = messagesData[messageId];
          if (message.roomId === room.id) {
            messagesArray.push({
              id: messageId,
              ...message
            });
          }
        }

        // ترتيب الرسائل حسب تاريخ الإنشاء
        messagesArray.sort((a, b) => {
          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);
        });

        setMessages(messagesArray);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في جلب الرسائل: {error.message}
        </Message>
      );
    } finally {
      setLoadingMessages(false);
    }
  };

  // استعادة المجموعة المنقولة
  const handleRestoreRoom = async (room) => {
    try {
      setLoading(true);

      // نقل المجموعة من قسم المجموعات الخاصة المنقولة إلى قسم المجموعات النشطة
      const movedRoomRef = ref(database, `/moved-private-rooms/${room.id}`);
      const snapshot = await get(movedRoomRef);

      if (!snapshot.exists()) {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على بيانات المجموعة المنقولة
          </Message>
        );
        setLoading(false);
        return;
      }

      // الحصول على بيانات المجموعة
      const roomData = snapshot.val();

      // إعادة إنشاء المجموعة في قائمة المجموعات النشطة
      const activeRoomRef = ref(database, `/rooms/${room.id}`);

      // إزالة بعض البيانات التي لا نريد نقلها
      const { movedAt, movedBy, isMovedPrivateRoom, originalId, ...restoreData } = roomData;

      // إضافة تاريخ الاستعادة
      restoreData.restoredAt = new Date().toISOString();
      restoreData.restoredBy = auth.currentUser.uid;

      // حفظ المجموعة في قائمة المجموعات النشطة
      await set(activeRoomRef, restoreData);

      // نقل الرسائل من قسم رسائل المجموعات الخاصة المنقولة إلى قسم الرسائل العادية
      const messagesRef = ref(database, 'moved-private-rooms-messages');
      const messagesSnapshot = await get(messagesRef);

      if (messagesSnapshot.exists()) {
        const messagesData = messagesSnapshot.val();
        const updates = {};

        // البحث عن الرسائل المرتبطة بالمجموعة المحددة ونقلها
        for (const messageId in messagesData) {
          const message = messagesData[messageId];
          if (message.roomId === room.id) {
            // نقل الرسالة إلى قسم الرسائل العادية
            updates[`/messages/${messageId}`] = message;
            // حذف الرسالة من قسم رسائل المجموعات الخاصة المنقولة
            updates[`/moved-private-rooms-messages/${messageId}`] = null;
          }
        }

        // تنفيذ التحديثات
        if (Object.keys(updates).length > 0) {
          await update(ref(database), updates);
        }
      }

      // حذف المجموعة من قسم المجموعات الخاصة المنقولة
      await remove(movedRoomRef);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تمت استعادة المجموعة بنجاح
        </Message>
      );

      // تحديث قائمة المجموعات
      setRooms(rooms.filter(r => r.id !== room.id));

      // إغلاق نافذة عرض الرسائل إذا كانت مفتوحة
      if (showMessagesModal && selectedRoom && selectedRoom.id === room.id) {
        setShowMessagesModal(false);
      }
    } catch (error) {
      console.error('Error restoring room:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في استعادة المجموعة: {error.message}
        </Message>
      );
    } finally {
      setLoading(false);
    }
  };

  // حذف المجموعة المنقولة نهائياً
  const handleDeleteRoom = async (room) => {
    try {
      setLoading(true);

      // حذف المجموعة من قسم المجموعات الخاصة المنقولة
      const movedRoomRef = ref(database, `/moved-private-rooms/${room.id}`);
      await remove(movedRoomRef);

      // حذف الرسائل المرتبطة بالمجموعة
      const messagesRef = ref(database, 'moved-private-rooms-messages');
      const messagesSnapshot = await get(messagesRef);

      if (messagesSnapshot.exists()) {
        const messagesData = messagesSnapshot.val();
        const updates = {};

        // البحث عن الرسائل المرتبطة بالمجموعة المحددة وحذفها
        for (const messageId in messagesData) {
          const message = messagesData[messageId];
          if (message.roomId === room.id) {
            updates[`/moved-private-rooms-messages/${messageId}`] = null;
          }
        }

        // تنفيذ التحديثات
        if (Object.keys(updates).length > 0) {
          await update(ref(database), updates);
        }
      }

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حذف المجموعة نهائياً بنجاح
        </Message>
      );

      // تحديث قائمة المجموعات
      setRooms(rooms.filter(r => r.id !== room.id));

      // إغلاق نافذة عرض الرسائل إذا كانت مفتوحة
      if (showMessagesModal && selectedRoom && selectedRoom.id === room.id) {
        setShowMessagesModal(false);
      }
    } catch (error) {
      console.error('Error deleting room:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في حذف المجموعة: {error.message}
        </Message>
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          <h4 className="header-title">المجموعات الخاصة المنقولة</h4>
        </div>
      </Header>
      <Content className="container-fluid">
        <FlexboxGrid justify="center">
          <FlexboxGrid.Item colspan={22}>
            <Panel className="dark-panel" bordered>
              {loading ? (
                <div className="text-center">
                  <Loader content="جاري التحميل..." />
                </div>
              ) : rooms.length === 0 ? (
                <div className="text-center">
                  <p>لا توجد مجموعات خاصة منقولة</p>
                </div>
              ) : (
                <List hover>
                  {rooms.map(room => (
                    <List.Item key={room.id} className="dark-list-item">
                      <FlexboxGrid align="middle">
                        <FlexboxGrid.Item colspan={4}>
                          <h5>{room.name}</h5>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={4}>
                          <div>
                            <strong>تم النقل بواسطة:</strong>{' '}
                            {room.movedBy}
                          </div>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={4}>
                          <div>
                            <strong>تاريخ النقل:</strong>{' '}
                            <TimeAgo datetime={room.movedAt} />
                          </div>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={4} style={{ textAlign: 'center' }}>
                          <div>
                            حالة البيع: {room.saleSuccessful ? (
                              <span style={{ color: 'green' }}>ناجح ✅</span>
                            ) : (
                              <span style={{ color: 'red' }}>غير ناجح ❌</span>
                            )}
                          </div>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={4} style={{ textAlign: 'center' }}>
                          <Button appearance="primary" color="green" onClick={() => handleRestoreRoom(room)}>
                            استعادة المجموعة
                          </Button>
                        </FlexboxGrid.Item>
                        <FlexboxGrid.Item colspan={4} style={{ textAlign: 'right' }}>
                          <Button appearance="link" onClick={() => handleViewMessages(room)}>
                            عرض الرسائل
                          </Button>
                        </FlexboxGrid.Item>
                      </FlexboxGrid>
                    </List.Item>
                  ))}
                </List>
              )}
            </Panel>
          </FlexboxGrid.Item>
        </FlexboxGrid>
      </Content>

      {/* نافذة عرض الرسائل */}
      <Modal size="lg" open={showMessagesModal} onClose={() => setShowMessagesModal(false)} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">
            {selectedRoom ? selectedRoom.name : 'رسائل المجموعة'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          {loadingMessages ? (
            <div className="text-center">
              <Loader content="جاري تحميل الرسائل..." />
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center">
              <p>لا توجد رسائل في هذه المجموعة</p>
            </div>
          ) : (
            <div className="messages-container">
              {messages.map(message => (
                <div key={message.id} className="message-item">
                  <div className="message-header">
                    <div className="message-author">
                      {message.author && (
                        <>
                          <ProfileAvatar src={message.author.avatar} name={message.author.name} size="sm" />
                          <span className="author-name">{message.author.name}</span>
                        </>
                      )}
                    </div>
                    <div className="message-time">
                      <TimeAgo datetime={message.createdAt} />
                    </div>
                  </div>
                  <div className="message-content">
                    {message.text && <div className="message-text">{message.text}</div>}
                    {message.file && message.file.url && (
                      <div className="message-file">
                        {message.file.contentType.includes('image') ? (
                          <img src={message.file.url} alt="صورة مرفقة" className="img-fluid" />
                        ) : (
                          <a href={message.file.url} target="_blank" rel="noopener noreferrer">
                            تنزيل الملف: {message.file.name}
                          </a>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          {selectedRoom && (
            <>
              <Button onClick={() => handleRestoreRoom(selectedRoom)} color="green" appearance="primary">
                استعادة المجموعة
              </Button>
              <Button onClick={() => handleDeleteRoom(selectedRoom)} color="red" appearance="primary">
                حذف المجموعة نهائياً
              </Button>
            </>
          )}
          <Button onClick={() => setShowMessagesModal(false)} appearance="subtle">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default MovedPrivateRoomsPage;
