import React, { useEffect, useRef, useState } from "react";
import { Divide<PERSON>, But<PERSON> } from "rsuite";
import { <PERSON> } from "react-router-dom";
import CreateRoomBtnModal from "./CreateRoomBtnModal";
import ProfileButton from "./dashboard/ProfileButton";
import ChatRoomList from "./rooms/ChatRoomList";
import AdminManager from "./dashboard/AdminManager";
import { useProfile } from "../context/profile.context";
import { auth } from "../misc/firebase.config";
import TranslatedText from "./TranslatedText";

const Sidebar = () => {
  const topSidebarRef = useRef();
  const [height, setHeight] = useState(0);
  const [showAdminManager, setShowAdminManager] = useState(false);
  const { profile } = useProfile();

  // التحقق من أن المستخدم الحالي هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );



  useEffect(() => {
    if (topSidebarRef.current) {
      setHeight(topSidebarRef.current.scrollHeight);
    }
  }, [topSidebarRef]);

  return (
    <div className="sidebar-container">
      <div ref={topSidebarRef} className="sidebar-header">
        <ProfileButton />

        {/* زر عجلة الحظ */}
        <Link to="/lucky-wheel">
          <Button block color="yellow" appearance="primary" className="mt-1 lucky-wheel-btn">
            <i className="fas fa-dharmachakra"></i> <TranslatedText text="luckyWheel" />
          </Button>
        </Link>
        {/* عرض أزرار المشرفين */}
        {profile && profile.isAdmin && (
          <>
            <CreateRoomBtnModal />
            {/* زر إدارة المشرفين (للمالك فقط) */}
            {isOwner && (
              <>
                <Link to="/records">
                  <Button block color="blue" appearance="primary" className="mt-1">
                    <TranslatedText text="records" />
                  </Button>
                </Link>
                <Button
                  block
                  color="violet"
                  appearance="primary"
                  className="mt-1"
                  onClick={() => setShowAdminManager(true)}
                >
                  <TranslatedText text="adminManagement" />
                </Button>
                <Link to="/lucky-wheel-admin">
                  <Button block color="orange" appearance="primary" className="mt-1">
                    <TranslatedText text="luckyWheelManagement" />
                  </Button>
                </Link>
                <Link to="/reward-code-checker">
                  <Button block color="green" appearance="primary" className="mt-1">
                    <TranslatedText text="checkRewardCodes" />
                  </Button>
                </Link>
              </>
            )}
          </>
        )}
        <Divider><TranslatedText text="availableChatRooms" /></Divider>
      </div>
      <ChatRoomList aboveElHeight={height} />

      {/* مكون إدارة المشرفين */}
      <AdminManager
        open={showAdminManager}
        onClose={() => setShowAdminManager(false)}
      />
    </div>
  );
};

export default Sidebar;
