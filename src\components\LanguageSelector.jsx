import React from 'react';
import { Dropdown, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'rsuite';
import { useLanguage } from '../context/language.context';
import { FaGlobe } from 'react-icons/fa';
import LanguageText from './LanguageText';
import TranslatedText from './TranslatedText';

const LanguageSelector = ({ appearance = 'subtle', size = 'md', block = false, className = '' }) => {
  const { currentLanguage, changeLanguage, t, LANGUAGES } = useLanguage();

  // تحديد اسم اللغة الحالية
  const getCurrentLanguageName = () => {
    switch (currentLanguage) {
      case LANGUAGES.AR:
        return t('arabic');
      case LANGUAGES.EN:
        return t('english');
      case LANGUAGES.KU:
        return t('kurdish');
      default:
        return t('selectLanguage');
    }
  };

  return (
    <Dropdown
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FaGlobe style={{ margin: '0 5px' }} />
          <TranslatedText text="language" />
        </div>
      }
      appearance={appearance}
      size={size}
      block={block}
      className={`language-selector ${className}`}
    >
      <Dropdown.Item
        onSelect={() => changeLanguage(LANGUAGES.AR)}
        active={currentLanguage === LANGUAGES.AR}
      >
        <LanguageText className="ar-text">العربية</LanguageText>
      </Dropdown.Item>
      <Dropdown.Item
        onSelect={() => changeLanguage(LANGUAGES.EN)}
        active={currentLanguage === LANGUAGES.EN}
      >
        <span>English</span>
      </Dropdown.Item>
      <Dropdown.Item
        onSelect={() => changeLanguage(LANGUAGES.KU)}
        active={currentLanguage === LANGUAGES.KU}
      >
        <LanguageText className="ku-text">کوردی سۆرانی</LanguageText>
      </Dropdown.Item>
    </Dropdown>
  );
};

// زر اللغة المصغر للاستخدام في شريط التنقل
export const LanguageButton = () => {
  const { currentLanguage, changeLanguage, t, LANGUAGES } = useLanguage();

  // تبديل اللغة عند النقر
  const toggleLanguage = () => {
    switch (currentLanguage) {
      case LANGUAGES.AR:
        changeLanguage(LANGUAGES.EN);
        break;
      case LANGUAGES.EN:
        changeLanguage(LANGUAGES.KU);
        break;
      case LANGUAGES.KU:
        changeLanguage(LANGUAGES.AR);
        break;
      default:
        changeLanguage(LANGUAGES.AR);
    }
  };

  // الحصول على رمز اللغة الحالية
  const getLanguageCode = () => {
    switch (currentLanguage) {
      case LANGUAGES.AR:
        return 'AR';
      case LANGUAGES.EN:
        return 'EN';
      case LANGUAGES.KU:
        return 'KU';
      default:
        return 'AR';
    }
  };

  return (
    <Whisper
      placement="top"
      trigger="hover"
      speaker={<Tooltip><TranslatedText text="selectLanguage" /></Tooltip>}
    >
      <Button
        appearance="subtle"
        className="language-button"
        onClick={toggleLanguage}
        style={{
          minWidth: '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '5px'
        }}
      >
        <FaGlobe style={{ margin: '0 5px' }} />
        <span>{getLanguageCode()}</span>
      </Button>
    </Whisper>
  );
};

export default LanguageSelector;
