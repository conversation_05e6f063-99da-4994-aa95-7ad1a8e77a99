import React, { createContext, useState, useContext, useEffect, useCallback, useRef } from 'react';
import { ref, onValue, off, query, orderByChild, equalTo, get, update, limitToLast, push, set } from 'firebase/database';
import { database } from '../misc/firebase.config';
import { useProfile } from './profile.context';
import InAppNotification from '../components/InAppNotification';

// إنشاء سياق الإشعارات
const NotificationContext = createContext();

// تخزين آخر وقت تم فيه معالجة الرسائل
let lastProcessedMessageTime = 0;
let lastProcessedPrivateMessageTime = 0;
let processedMessageIds = new Set();

/**
 * مزود سياق الإشعارات
 * يوفر وظائف لإدارة الإشعارات داخل التطبيق
 */
export const NotificationProvider = ({ children }) => {
  const { profile } = useProfile();
  const [notification, setNotification] = useState(null);
  const [notificationQueue, setNotificationQueue] = useState([]);

  // تنظيف قائمة الرسائل المعالجة بشكل دوري
  useEffect(() => {
    // تنظيف قائمة الرسائل المعالجة كل 30 دقيقة
    const cleanupInterval = setInterval(() => {
      console.log('تنظيف قائمة الرسائل المعالجة...');
      console.log('عدد الرسائل المعالجة قبل التنظيف:', processedMessageIds.size);

      // إعادة تعيين قائمة الرسائل المعالجة
      processedMessageIds.clear();

      console.log('تم تنظيف قائمة الرسائل المعالجة');
    }, 30 * 60 * 1000); // 30 دقيقة

    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  // إضافة إشعار جديد إلى قائمة الانتظار وحفظه في قاعدة البيانات
  const addNotification = useCallback(async (newNotification) => {
    console.log('إضافة إشعار جديد إلى قائمة الانتظار:', newNotification.type, 'في:', new Date().toLocaleTimeString());

    // إضافة الإشعار إلى قائمة الانتظار
    setNotificationQueue(prevQueue => [...prevQueue, newNotification]);

    // حفظ الإشعار في قاعدة البيانات إذا كان المستخدم مسجل الدخول
    if (profile && profile.uid) {
      try {
        // إنشاء معرف فريد للإشعار
        const notificationId = `notification_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        console.log('تم إنشاء معرف فريد للإشعار:', notificationId);

        // إضافة حقول إضافية للإشعار
        const currentTime = Date.now();
        const notificationToSave = {
          ...newNotification,
          id: notificationId,
          read: false,
          createdAt: currentTime,
          timestamp: currentTime
        };

        console.log('جاري حفظ الإشعار في قاعدة البيانات:', JSON.stringify(notificationToSave).substring(0, 200) + '...');

        // استخدام مسار مباشر
        const directPath = `notifications/${profile.uid}/${notificationId}`;
        const directRef = ref(database, directPath);

        try {
          // حفظ الإشعار في قاعدة البيانات
          await update(ref(database), {
            [directPath]: notificationToSave
          });

          console.log('تم حفظ الإشعار في قاعدة البيانات بنجاح:', notificationId);

          // التحقق من وجود الإشعار في قاعدة البيانات
          const snapshot = await get(directRef);

          if (snapshot.exists()) {
            console.log('تم التأكد من وجود الإشعار في قاعدة البيانات:', notificationId);
          } else {
            console.warn('لم يتم العثور على الإشعار في قاعدة البيانات بعد الحفظ!');

            // محاولة ثانية باستخدام set
            console.log('محاولة حفظ الإشعار مرة أخرى باستخدام set...');
            await set(directRef, notificationToSave);

            // التحقق مرة أخرى
            const checkSnapshot = await get(directRef);
            if (checkSnapshot.exists()) {
              console.log('تم التأكد من وجود الإشعار بعد المحاولة الثانية');
            } else {
              // محاولة ثالثة باستخدام مسار مختلف
              console.log('محاولة ثالثة باستخدام مسار مختلف...');
              const alternativePath = `user_notifications/${profile.uid}/${notificationId}`;
              const alternativeRef = ref(database, alternativePath);
              await set(alternativeRef, notificationToSave);

              console.log('تمت المحاولة الثالثة لحفظ الإشعار في مسار بديل:', alternativePath);
            }
          }
        } catch (error) {
          console.error('خطأ في حفظ الإشعار:', error);

          // محاولة أخيرة باستخدام طريقة مختلفة
          try {
            console.log('محاولة أخيرة لحفظ الإشعار...');
            // استخدام مسار مختلف تمامًا
            const simplePath = `user_data/${profile.uid}/notifications/${notificationId}`;
            const simpleRef = ref(database, simplePath);
            await set(simpleRef, notificationToSave);
            console.log('تمت المحاولة الأخيرة لحفظ الإشعار في:', simplePath);
          } catch (finalError) {
            console.error('فشل في جميع محاولات حفظ الإشعار:', finalError);
          }
        }
      } catch (error) {
        console.error('خطأ في إنشاء مرجع الإشعار:', error);
      }
    } else {
      console.warn('لا يمكن حفظ الإشعار في قاعدة البيانات: المستخدم غير مسجل الدخول');
    }
  }, [profile]);

  // إزالة الإشعار الحالي
  const removeCurrentNotification = useCallback(() => {
    setNotification(null);

    // إذا كان هناك إشعارات في قائمة الانتظار، اعرض الإشعار التالي
    setNotificationQueue(prevQueue => {
      if (prevQueue.length > 0) {
        const [nextNotification, ...restQueue] = prevQueue;
        setNotification(nextNotification);
        return restQueue;
      }
      return prevQueue;
    });
  }, []);

  // الاستماع للردود على رسائل المستخدم
  useEffect(() => {
    if (!profile || !profile.uid) return;

    console.log('تهيئة مستمع الردود للمستخدم:', profile.uid);

    const handleNewReply = (replyData) => {
      // تحقق مما إذا كانت الرسالة قد تمت معالجتها بالفعل
      const messageUniqueId = `reply_${replyData.id}_${replyData.createdAt}`;
      if (processedMessageIds.has(messageUniqueId)) {
        console.log('تم معالجة هذه الرسالة بالفعل:', messageUniqueId);
        return;
      }

      // إضافة معرف الرسالة إلى قائمة الرسائل المعالجة
      processedMessageIds.add(messageUniqueId);

      console.log('إنشاء إشعار رد جديد من:', replyData.author.name);

      // إنشاء إشعار جديد
      const newNotification = {
        id: `reply-${Date.now()}`,
        type: 'reply',
        title: replyData.author.name,
        body: `رد على رسالتك: ${replyData.text.substring(0, 50)}${replyData.text.length > 50 ? '...' : ''}`,
        link: `/chat/${replyData.roomId}`,
        timestamp: Date.now(),
        data: replyData
      };

      // إضافة الإشعار إلى قائمة الانتظار
      addNotification(newNotification);
    };

    // الاستماع للرسائل الجديدة التي تحتوي على رد على رسائل المستخدم
    const messagesRef = ref(database, 'messages');

    // استخدام limitToLast للحصول على آخر 10 رسائل لتحسين الأداء والاستجابة في الوقت الفعلي
    // زيادة العدد من 1 إلى 10 لضمان عدم فقدان أي رسائل
    const messagesQuery = query(messagesRef, orderByChild('createdAt'), limitToLast(10));

    const unsubscribe = onValue(messagesQuery, (snapshot) => {
      if (!snapshot.exists()) {
        console.log('لا توجد رسائل في قاعدة البيانات');
        return;
      }

      const messages = snapshot.val();
      console.log(`تم استلام ${Object.keys(messages).length} رسائل جديدة`);

      // البحث عن الرسائل التي تحتوي على رد على رسائل المستخدم
      Object.entries(messages).forEach(([messageId, message]) => {
        // تحقق مما إذا كانت الرسالة تحتوي على رد على رسالة المستخدم
        if (message.replyTo && message.replyTo.author && message.replyTo.author.uid === profile.uid) {
          // تحقق مما إذا كانت الرسالة من مستخدم آخر (وليس من المستخدم نفسه)
          if (message.author.uid !== profile.uid) {
            // تحقق من وقت إنشاء الرسالة (يجب أن تكون حديثة - خلال آخر دقيقة)
            const messageTime = message.createdAt || 0;
            const currentTime = Date.now();
            const isRecent = (currentTime - messageTime) < 60000; // 60 ثانية

            if (isRecent) {
              console.log('تم العثور على رد جديد في الوقت الفعلي:', messageId, 'من:', message.author.name);
              handleNewReply({
                ...message,
                id: messageId
              });
            }
          }
        }
      });
    });

    return () => {
      // إلغاء الاشتراك عند إلغاء تحميل المكون
      console.log('إلغاء مستمع الردود');
      off(messagesQuery);
    };
  }, [profile, addNotification]);

  // تخزين آخر رسالة تم معالجتها لكل دردشة
  const lastProcessedMessageIds = useRef(new Map());

  // الاستماع للرسائل الخاصة الجديدة
  useEffect(() => {
    if (!profile || !profile.uid) return;

    console.log('تهيئة مستمع الرسائل الخاصة للمستخدم:', profile.uid);

    const handleNewPrivateMessage = (messageData, chatId, otherUserId) => {
      // إنشاء معرف فريد للرسالة
      const messageUniqueId = `private_${chatId}_${messageData.createdAt}_${messageData.author.uid}`;

      // تحقق مما إذا كانت الرسالة قد تمت معالجتها بالفعل
      if (processedMessageIds.has(messageUniqueId)) {
        console.log('تم معالجة هذه الرسالة الخاصة بالفعل:', messageUniqueId);
        return;
      }

      // إضافة معرف الرسالة إلى قائمة الرسائل المعالجة
      processedMessageIds.add(messageUniqueId);

      console.log('إنشاء إشعار رسالة خاصة جديدة من:', messageData.author.name);

      // إنشاء إشعار جديد
      const newNotification = {
        id: `private-${Date.now()}`,
        type: 'privateMessage',
        title: messageData.author.name,
        body: `رسالة خاصة: ${messageData.text.substring(0, 50)}${messageData.text.length > 50 ? '...' : ''}`,
        link: `/private-chat/${otherUserId}`,
        timestamp: Date.now(),
        data: messageData
      };

      // إضافة الإشعار إلى قائمة الانتظار
      addNotification(newNotification);
    };

    // الاستماع للرسائل الخاصة الجديدة - نستمع لكل دردشة خاصة على حدة
    const privateChatsRef = ref(database, 'private-chats');

    // أولاً، نحصل على قائمة الدردشات الخاصة للمستخدم
    const unsubscribeChats = onValue(privateChatsRef, (snapshot) => {
      if (!snapshot.exists()) {
        console.log('لا توجد دردشات خاصة في قاعدة البيانات');
        return;
      }

      const chats = snapshot.val();
      console.log(`تم العثور على ${Object.keys(chats).length} دردشة خاصة للمستخدم`);

      // البحث عن الدردشات التي يشارك فيها المستخدم
      Object.entries(chats).forEach(([chatId, chat]) => {
        if (chat.members && chat.members[profile.uid]) {
          // الحصول على معرف المستخدم الآخر في الدردشة
          const otherUserId = Object.keys(chat.members).find(uid => uid !== profile.uid);

          if (!otherUserId) {
            console.log('لم يتم العثور على المستخدم الآخر في الدردشة:', chatId);
            return;
          }

          console.log(`الاستماع للرسائل الخاصة في الدردشة ${chatId} مع المستخدم ${otherUserId}`);

          // الاستماع للرسائل الجديدة في هذه الدردشة
          const chatMessagesRef = ref(database, `private-messages/${chatId}`);
          const chatMessagesQuery = query(chatMessagesRef, orderByChild('createdAt'), limitToLast(5));

          // استمع للرسائل الجديدة في هذه الدردشة
          onValue(chatMessagesQuery, (messagesSnapshot) => {
            if (!messagesSnapshot.exists()) {
              console.log(`لا توجد رسائل في الدردشة ${chatId}`);
              return;
            }

            const messages = messagesSnapshot.val();
            console.log(`تم استلام ${Object.keys(messages).length} رسائل في الدردشة ${chatId}`);

            // معالجة الرسائل
            Object.entries(messages).forEach(([messageId, message]) => {
              // تحقق مما إذا كانت الرسالة من المستخدم الآخر وليست من المستخدم الحالي
              if (message.author && message.author.uid !== profile.uid) {
                // تحقق من وقت إنشاء الرسالة (يجب أن تكون حديثة - خلال آخر دقيقة)
                const messageTime = message.createdAt || 0;
                const currentTime = Date.now();
                const isRecent = (currentTime - messageTime) < 60000; // 60 ثانية

                // تحقق مما إذا كانت الرسالة جديدة
                const messageUniqueId = `private_${chatId}_${message.createdAt}_${message.author.uid}`;

                if (isRecent && !processedMessageIds.has(messageUniqueId)) {
                  console.log('تم العثور على رسالة خاصة جديدة في الوقت الفعلي:', messageId, 'من:', message.author.name);
                  handleNewPrivateMessage(message, chatId, otherUserId);
                }
              }
            });
          });
        }
      });
    });

    return () => {
      // إلغاء الاشتراك عند إلغاء تحميل المكون
      console.log('إلغاء مستمع الرسائل الخاصة');
      off(privateChatsRef);
    };
  }, [profile, addNotification]);

  // عرض الإشعار التالي من قائمة الانتظار عندما لا يكون هناك إشعار حالي
  useEffect(() => {
    if (!notification && notificationQueue.length > 0) {
      const [nextNotification, ...restQueue] = notificationQueue;
      setNotification(nextNotification);
      setNotificationQueue(restQueue);
    }
  }, [notification, notificationQueue]);

  // إضافة إشعار اختباري (للاختبار فقط)
  const addTestNotification = useCallback((type = 'reply') => {
    console.log('إضافة إشعار اختباري من النوع:', type);

    let testNotification;

    switch (type) {
      case 'reply':
        testNotification = {
          id: `test-reply-${Date.now()}`,
          type: 'reply',
          title: 'مستخدم اختباري',
          body: 'هذا رد اختباري على رسالتك للتأكد من عمل نظام الإشعارات',
          link: '/',
          timestamp: Date.now(),
          data: { text: 'هذا رد اختباري' }
        };
        break;
      case 'privateMessage':
        testNotification = {
          id: `test-private-${Date.now()}`,
          type: 'privateMessage',
          title: 'مستخدم اختباري',
          body: 'هذه رسالة خاصة اختبارية للتأكد من عمل نظام الإشعارات',
          link: '/',
          timestamp: Date.now(),
          data: { text: 'هذه رسالة خاصة اختبارية' }
        };
        break;
      case 'ownerMessage':
        testNotification = {
          id: `test-owner-${Date.now()}`,
          type: 'ownerMessage',
          title: 'المالك',
          body: 'هذه رسالة اختبارية من المالك للتأكد من عمل نظام الإشعارات',
          link: '/',
          timestamp: Date.now(),
          data: { text: 'هذه رسالة من المالك' }
        };
        break;
      default:
        testNotification = {
          id: `test-${Date.now()}`,
          type: 'info',
          title: 'إشعار اختباري',
          body: 'هذا إشعار اختباري للتأكد من عمل نظام الإشعارات',
          link: '/',
          timestamp: Date.now(),
          data: { text: 'هذا إشعار اختباري' }
        };
    }

    addNotification(testNotification);
  }, [addNotification]);

  return (
    <NotificationContext.Provider
      value={{
        addNotification,
        removeCurrentNotification,
        addTestNotification // إضافة وظيفة الإشعار الاختباري
      }}
    >
      {children}
      {notification && (
        <InAppNotification
          notification={notification}
          onClose={removeCurrentNotification}
        />
      )}
    </NotificationContext.Provider>
  );
};

// هوك مخصص لاستخدام سياق الإشعارات
export const useNotifications = () => useContext(NotificationContext);
