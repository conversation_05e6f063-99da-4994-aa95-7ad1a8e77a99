import React, { useEffect, useState } from 'react';
import { ref, onValue } from 'firebase/database';
import { database } from '../../../misc/firebase.config';
import { useParams } from 'react-router';
import { Toolt<PERSON>, Whisper, Badge } from 'rsuite';
import { useCurrentRoom } from '../../../context/current-room.context';
import UserIcon from '@rsuite/icons/legacy/User';

const RoomUsersCount = () => {
  const { chatId } = useParams();
  const [totalUsers, setTotalUsers] = useState(0);
  const [activeUsers, setActiveUsers] = useState(0);
  const isPrivate = useCurrentRoom(v => v.isPrivate);

  useEffect(() => {
    // لا نعرض عدد المستخدمين في الغرف الخاصة
    if (isPrivate) {
      return;
    }

    // جلب إجمالي عدد المستخدمين في النظام
    const fetchTotalUsers = () => {
      const usersRef = ref(database, 'users');

      return onValue(usersRef, snapshot => {
        if (snapshot.exists()) {
          const usersData = snapshot.val();
          const usersCount = Object.keys(usersData).length;
          console.log('Total users count:', usersCount);
          setTotalUsers(usersCount);
        } else {
          console.log('No users found in database');
          setTotalUsers(0);
        }
      });
    };

    // مراقبة المستخدمين النشطين
    const fetchActiveUsers = () => {
      const statusRef = ref(database, 'status');

      return onValue(statusRef, snapshot => {
        if (snapshot.exists()) {
          const statusData = snapshot.val();
          let activeCount = 0;

          // حساب عدد المستخدمين النشطين
          Object.values(statusData).forEach(status => {
            if (status.state === 'online') {
              activeCount++;
            }
          });

          console.log('Active users count:', activeCount);
          setActiveUsers(activeCount);
        } else {
          console.log('No status data found in database');
          setActiveUsers(0);
        }
      });
    };

    // تنفيذ الاستعلامات
    const unsubscribe1 = fetchTotalUsers();
    const unsubscribe2 = fetchActiveUsers();

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      unsubscribe1 && unsubscribe1();
      unsubscribe2 && unsubscribe2();
    };
  }, [chatId, isPrivate]);

  // لا نعرض شيئًا في الغرف الخاصة
  if (isPrivate) {
    return null;
  }

  return (
    <Whisper
      placement="bottom"
      trigger="hover"
      speaker={
        <Tooltip>
          {`إجمالي المستخدمين: ${totalUsers} | المستخدمين النشطين: ${activeUsers}`}
        </Tooltip>
      }
    >
      <div className="room-users-count">
        <UserIcon className="user-icon" />
        <span>{totalUsers}</span>
        <span className="separator">-</span>
        <span className="active-users">
          <Badge className="online-badge" />
          {activeUsers}
        </span>
      </div>
    </Whisper>
  );
};

export default RoomUsersCount;
