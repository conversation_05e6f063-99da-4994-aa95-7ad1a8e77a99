import React, { useEffect, useState } from 'react';
import { ref, get, remove, set } from 'firebase/database';
import { database, auth } from '../misc/firebase.config';
import {
  Container,
  Content,
  FlexboxGrid,
  Panel,
  List,
  Button,
  Message,
  toaster,
  Loader,
  Modal,
  Nav,
  Header,
  InputGroup,
  Input
} from 'rsuite';
import TimeAgo from 'timeago-react';
import { useProfile } from '../context/profile.context';
import ProfileAvatar from '../components/ProfileAvatar';
import RestoreRoomButton from '../components/dashboard/RestoreRoomButton';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';
import { useHistory } from 'react-router-dom';
import SearchIcon from '@rsuite/icons/Search';

const RecordsPage = () => {
  const [activeTab, setActiveTab] = useState('rooms');
  const [deletedRooms, setDeletedRooms] = useState([]);
  const [hiddenRooms, setHiddenRooms] = useState([]);
  const [hiddenFromHomeRooms, setHiddenFromHomeRooms] = useState([]);
  const [bannedUsers, setBannedUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [showMessagesModal, setShowMessagesModal] = useState(false);
  const [roomMessages, setRoomMessages] = useState([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [roomsType, setRoomsType] = useState('hidden'); // 'hidden', 'deleted', or 'hidden-from-home'
  const { profile } = useProfile();
  const history = useHistory();
  const isOwner = profile && (profile.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || profile.email === '<EMAIL>');

  // دالة للعودة إلى الصفحة السابقة
  const handleBack = () => {
    if (window.history.length > 1) {
      history.goBack();
    } else {
      history.push('/');
    }
  };

  // جلب المجموعات المخفية والمحذوفة
  useEffect(() => {
    const fetchRooms = async () => {
      try {
        if (!auth.currentUser || !isOwner) {
          setLoading(false);
          return;
        }

        setLoading(true);

        // جلب المجموعات المخفية
        const hiddenRoomsRef = ref(database, '/rooms');
        const hiddenSnapshot = await get(hiddenRoomsRef);

        if (hiddenSnapshot.exists()) {
          const allRoomsData = hiddenSnapshot.val();
          // تصفية المجموعات المخفية فقط
          const hiddenRoomsArray = Object.keys(allRoomsData)
            .filter(roomId => allRoomsData[roomId].isHidden)
            .map(roomId => ({
              id: roomId,
              ...allRoomsData[roomId]
            }));

          // ترتيب المجموعات حسب تاريخ الإخفاء (الأحدث أولاً)
          hiddenRoomsArray.sort((a, b) => new Date(b.hiddenAt) - new Date(a.hiddenAt));
          setHiddenRooms(hiddenRoomsArray);
        }

        // جلب المجموعات المخفية من الصفحة الرئيسية
        const userHiddenRoomsRef = ref(database, `/users/${auth.currentUser.uid}/hiddenRooms`);
        const userHiddenSnapshot = await get(userHiddenRoomsRef);

        if (userHiddenSnapshot.exists()) {
          const hiddenRoomIds = Object.keys(userHiddenSnapshot.val());

          // جلب تفاصيل المجموعات المخفية
          const roomsRef = ref(database, '/rooms');
          const roomsSnapshot = await get(roomsRef);

          if (roomsSnapshot.exists()) {
            const roomsData = roomsSnapshot.val();
            const hiddenFromHomeArray = hiddenRoomIds
              .filter(roomId => roomsData[roomId] && !roomsData[roomId].isHidden) // استبعاد المجموعات المخفية بالفعل
              .map(roomId => ({
                id: roomId,
                ...roomsData[roomId],
                hiddenFromHome: true,
                hiddenAt: new Date().toISOString() // تاريخ افتراضي إذا لم يكن موجوداً
              }));

            setHiddenFromHomeRooms(hiddenFromHomeArray);
          }
        }

        // جلب المجموعات المحذوفة
        const deletedRoomsRef = ref(database, '/deleted-rooms');
        const deletedSnapshot = await get(deletedRoomsRef);

        if (deletedSnapshot.exists()) {
          const roomsData = deletedSnapshot.val();
          const roomsArray = Object.keys(roomsData).map(roomId => ({
            id: roomId,
            ...roomsData[roomId]
          }));

          // ترتيب الغرف حسب تاريخ الحذف (الأحدث أولاً)
          roomsArray.sort((a, b) => new Date(b.deletedAt) - new Date(a.deletedAt));
          setDeletedRooms(roomsArray);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching rooms:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في جلب سجلات المجموعات: {error.message}
          </Message>
        );
        setLoading(false);
      }
    };

    if (activeTab === 'rooms') {
      fetchRooms();
    }
  }, [isOwner, activeTab]);

  // جلب المستخدمين المحظورين
  useEffect(() => {
    const fetchBannedUsers = async () => {
      try {
        if (!auth.currentUser || !isOwner) {
          setLoading(false);
          return;
        }

        // جلب قائمة المستخدمين المحظورين
        const bannedUsersRef = ref(database, '/banned-users');
        const snapshot = await get(bannedUsersRef);

        if (snapshot.exists()) {
          const usersData = snapshot.val();
          const usersArray = Object.keys(usersData).map(userId => ({
            id: userId,
            ...usersData[userId]
          }));

          // ترتيب المستخدمين حسب تاريخ الحظر (الأحدث أولاً)
          usersArray.sort((a, b) => new Date(b.bannedAt) - new Date(a.bannedAt));
          setBannedUsers(usersArray);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching banned users:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في جلب قائمة المستخدمين المحظورين: {error.message}
          </Message>
        );
        setLoading(false);
      }
    };

    if (activeTab === 'banned') {
      fetchBannedUsers();
    }
  }, [isOwner, activeTab]);

  // عرض رسائل الغرفة المحذوفة أو المخفية
  const handleViewMessages = async (room) => {
    setSelectedRoom(room);
    setShowMessagesModal(true);
    setLoadingMessages(true);

    try {
      // جلب رسائل الغرفة من قاعدة البيانات
      let messagesRef;

      if (roomsType === 'hidden') {
        // للمجموعات المخفية، نجلب الرسائل من جدول الرسائل العادية
        messagesRef = ref(database, '/messages');
        const snapshot = await get(messagesRef);

        if (snapshot.exists()) {
          const messagesData = snapshot.val();
          const messagesArray = Object.keys(messagesData)
            .filter(messageId => messagesData[messageId].roomId === room.id)
            .map(messageId => ({
              id: messageId,
              ...messagesData[messageId]
            }));

          // ترتيب الرسائل حسب تاريخ الإنشاء
          messagesArray.sort((a, b) => a.createdAt - b.createdAt);
          setRoomMessages(messagesArray);
          setLoadingMessages(false);
          return;
        }
      } else {
        // للمجموعات المحذوفة، نجلب الرسائل من جدول المجموعات المحذوفة
        messagesRef = ref(database, `/deleted-rooms/${room.id}/messages`);
        const snapshot = await get(messagesRef);

        if (snapshot.exists()) {
          const messagesData = snapshot.val();
          const messagesArray = Object.keys(messagesData).map(messageId => ({
            id: messageId,
            ...messagesData[messageId]
          }));

          // ترتيب الرسائل حسب تاريخ الإنشاء
          messagesArray.sort((a, b) => a.createdAt - b.createdAt);
          setRoomMessages(messagesArray);
        } else {
          setRoomMessages([]);
        }
      }

      setLoadingMessages(false);
    } catch (error) {
      console.error('Error fetching room messages:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في جلب رسائل الغرفة: {error.message}
        </Message>
      );
      setLoadingMessages(false);
    }
  };

  // إلغاء حظر مستخدم
  const handleUnbanUser = async (userId) => {
    try {
      console.log('Attempting to unban user:', userId);
      const bannedUserRef = ref(database, `/banned-users/${userId}`);
      const bannedUserSnapshot = await get(bannedUserRef);

      if (bannedUserSnapshot.exists()) {
        await remove(bannedUserRef);
        setBannedUsers(prevUsers => prevUsers.filter(user => user.id !== userId));

        toaster.push(
          <Message type="success" closable duration={4000}>
            تم إلغاء حظر المستخدم بنجاح
          </Message>
        );
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            المستخدم غير موجود في قائمة المحظورين
          </Message>
        );
      }
    } catch (error) {
      console.error('Error unbanning user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إلغاء حظر المستخدم: {error.message}
        </Message>
      );
    }
  };

  // تصفية المستخدمين المحظورين حسب البحث
  const filteredBannedUsers = bannedUsers.filter(user =>
    user.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (user.name && user.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (user.email && user.email.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // تصفية المجموعات المخفية حسب البحث
  const filteredHiddenRooms = hiddenRooms.filter(room =>
    room.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (room.name && room.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (room.description && room.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // تصفية المجموعات المخفية من الصفحة الرئيسية حسب البحث
  const filteredHiddenFromHomeRooms = hiddenFromHomeRooms.filter(room =>
    room.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (room.name && room.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (room.description && room.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // تصفية المجموعات المحذوفة حسب البحث
  const filteredDeletedRooms = deletedRooms.filter(room =>
    room.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (room.name && room.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (room.description && room.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // الحصول على المجموعات المناسبة حسب النوع المحدد
  let currentRooms;
  if (roomsType === 'hidden') {
    currentRooms = filteredHiddenRooms;
  } else if (roomsType === 'deleted') {
    currentRooms = filteredDeletedRooms;
  } else if (roomsType === 'hidden-from-home') {
    currentRooms = filteredHiddenFromHomeRooms;
  }

  // إذا كان المستخدم ليس المالك، عرض رسالة خطأ
  if (!isOwner) {
    return (
      <Container>
        <FlexboxGrid justify="center" align="middle" style={{ height: '100vh' }}>
          <FlexboxGrid.Item colspan={12}>
            <Panel header="غير مصرح" bordered>
              <Message type="error" showIcon>
                لا يمكنك الوصول إلى هذه الصفحة. هذه الصفحة متاحة للمالك فقط.
              </Message>
            </Panel>
          </FlexboxGrid.Item>
        </FlexboxGrid>
      </Container>
    );
  }

  return (
    <Container>
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          <h4 className="header-title">السجلات</h4>
        </div>
      </Header>
      <Content className="mt-3">
        <FlexboxGrid justify="center">
          <FlexboxGrid.Item colspan={20}>
            <Nav appearance="tabs" activeKey={activeTab} onSelect={setActiveTab} style={{ marginBottom: '15px' }}>
              <Nav.Item eventKey="rooms">المجموعات</Nav.Item>
              <Nav.Item eventKey="banned">المستخدمين المحظورين</Nav.Item>
            </Nav>

            {activeTab === 'rooms' && (
              <Nav appearance="subtle" activeKey={roomsType} onSelect={setRoomsType} style={{ marginBottom: '15px' }}>
                <Nav.Item eventKey="hidden">المجموعات المخفية</Nav.Item>
                <Nav.Item eventKey="hidden-from-home">المجموعات المخفية من الرئيسية</Nav.Item>
                <Nav.Item eventKey="deleted">المجموعات المحذوفة</Nav.Item>
              </Nav>
            )}

            <InputGroup inside style={{ marginBottom: '15px' }}>
              <Input
                placeholder={activeTab === 'rooms'
                  ? (roomsType === 'hidden'
                      ? "البحث في المجموعات المخفية..."
                      : roomsType === 'hidden-from-home'
                        ? "البحث في المجموعات المخفية من الرئيسية..."
                        : "البحث في المجموعات المحذوفة...")
                  : "البحث عن مستخدم محظور..."}
                value={searchQuery}
                onChange={setSearchQuery}
              />
              <InputGroup.Button>
                <SearchIcon />
              </InputGroup.Button>
            </InputGroup>

            <Panel bordered className="dark-panel">
              {loading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <Loader size="md" content="جاري التحميل..." />
                </div>
              ) : activeTab === 'rooms' ? (
                // عرض المجموعات المخفية أو المحذوفة
                currentRooms.length === 0 ? (
                  <Message type="info" showIcon>
                    {searchQuery
                      ? 'لا توجد نتائج مطابقة للبحث'
                      : roomsType === 'hidden'
                        ? 'لا توجد مجموعات مخفية.'
                        : 'لا توجد مجموعات محذوفة.'
                    }
                  </Message>
                ) : (
                  <List hover>
                    {currentRooms.map(room => (
                      <List.Item key={room.id}>
                        <FlexboxGrid align="middle">
                          <FlexboxGrid.Item colspan={12}>
                            <h4>{room.name}</h4>
                            <p>{room.description}</p>
                            <small>
                              {roomsType === 'hidden'
                                ? <>تم الإخفاء: <TimeAgo datetime={new Date(room.hiddenAt)} /></>
                                : roomsType === 'hidden-from-home'
                                  ? <>تم الإخفاء من الرئيسية: <TimeAgo datetime={new Date(room.hiddenAt || Date.now())} /></>
                                  : <>تم الحذف: <TimeAgo datetime={new Date(room.deletedAt)} /></>
                              }
                            </small>
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={4} style={{ textAlign: 'center' }}>
                            <div>
                              حالة البيع: {room.saleSuccessful ? (
                                <span style={{ color: 'green' }}>ناجح ✅</span>
                              ) : (
                                <span style={{ color: 'red' }}>غير ناجح ❌</span>
                              )}
                            </div>
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={4} style={{ textAlign: 'center' }}>
                            {roomsType === 'hidden' || roomsType === 'hidden-from-home' ? (
                              <>
                                <Button
                                  appearance="primary"
                                  color="blue"
                                  size="sm"
                                  onClick={() => history.push(`/chat/${room.id}`)}
                                >
                                  الدخول للمجموعة
                                </Button>
                                {roomsType === 'hidden-from-home' && (
                                  <Button
                                    appearance="primary"
                                    color="red"
                                    size="sm"
                                    style={{ marginTop: '5px' }}
                                    onClick={() => {
                                      // إزالة المجموعة من قائمة المجموعات المخفية للمالك
                                      set(ref(database, `/users/${auth.currentUser.uid}/hiddenRooms/${room.id}`), null)
                                        .then(() => {
                                          toaster.push(
                                            <Message type="success" closable duration={4000}>
                                              تم إظهار المجموعة في الصفحة الرئيسية
                                            </Message>
                                          );
                                          // تحديث القائمة
                                          setTimeout(() => window.location.reload(), 1000);
                                        })
                                        .catch(error => {
                                          console.error('Error unhiding room:', error);
                                          toaster.push(
                                            <Message type="error" closable duration={4000}>
                                              خطأ في إظهار المجموعة: {error.message}
                                            </Message>
                                          );
                                        });
                                    }}
                                  >
                                    إظهار في الرئيسية
                                  </Button>
                                )}
                              </>
                            ) : (
                              <RestoreRoomButton room={room} />
                            )}
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={4} style={{ textAlign: 'right' }}>
                            <Button appearance="link" onClick={() => handleViewMessages(room)}>
                              عرض الرسائل
                            </Button>
                          </FlexboxGrid.Item>
                        </FlexboxGrid>
                      </List.Item>
                    ))}
                  </List>
                )
              ) : (
                // عرض المستخدمين المحظورين
                filteredBannedUsers.length === 0 ? (
                  <Message type="info" showIcon>
                    {searchQuery ? 'لا توجد نتائج مطابقة للبحث' : 'لا يوجد مستخدمين محظورين حاليًا'}
                  </Message>
                ) : (
                  <List hover>
                    {filteredBannedUsers.map(user => (
                      <List.Item key={user.id}>
                        <FlexboxGrid align="middle">
                          <FlexboxGrid.Item colspan={2}>
                            <ProfileAvatar
                              src={user.avatar}
                              name={user.name || 'مستخدم'}
                              uid={user.id}
                              showUid={true}
                              size="sm"
                            />
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={10}>
                            <div>
                              <strong>{user.name || 'مستخدم غير معروف'}</strong>
                              <div style={{ fontSize: '12px', color: '#999', direction: 'ltr' }}>
                                {user.id}
                              </div>
                              {user.email && (
                                <div style={{ fontSize: '12px', color: '#999' }}>
                                  {user.email}
                                </div>
                              )}
                            </div>
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={6}>
                            <div>
                              <div>تم الحظر: <TimeAgo datetime={new Date(user.bannedAt)} /></div>
                              {user.bannedBy && (
                                <div style={{ fontSize: '12px' }}>
                                  بواسطة: {user.bannedBy.name || 'غير معروف'}
                                </div>
                              )}
                            </div>
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={6} style={{ textAlign: 'right' }}>
                            <Button
                              appearance="primary"
                              color="green"
                              size="sm"
                              onClick={() => handleUnbanUser(user.id)}
                            >
                              إلغاء الحظر
                            </Button>
                          </FlexboxGrid.Item>
                        </FlexboxGrid>
                      </List.Item>
                    ))}
                  </List>
                )
              )}
            </Panel>
          </FlexboxGrid.Item>
        </FlexboxGrid>
      </Content>

      {/* نافذة عرض رسائل الغرفة المحذوفة */}
      <Modal
        open={showMessagesModal}
        onClose={() => setShowMessagesModal(false)}
        size="full"
        className="dark-theme deleted-messages-modal"
        full
      >
        <Modal.Header>
          <Modal.Title>
            {selectedRoom && (
              <div>
                رسائل الغرفة: {selectedRoom.name}
                <div style={{ fontSize: '12px', opacity: 0.7 }}>
                  {roomsType === 'hidden' ? (
                    <>تم الإخفاء: <TimeAgo datetime={new Date(selectedRoom.hiddenAt)} /></>
                  ) : roomsType === 'hidden-from-home' ? (
                    <>تم الإخفاء من الرئيسية: <TimeAgo datetime={new Date(selectedRoom.hiddenAt || Date.now())} /></>
                  ) : (
                    <>تم الحذف: <TimeAgo datetime={new Date(selectedRoom.deletedAt)} /></>
                  )}
                  {' | '}
                  حالة البيع: {selectedRoom.saleSuccessful ? (
                    <span style={{ color: 'green' }}>ناجح ✅</span>
                  ) : (
                    <span style={{ color: 'red' }}>غير ناجح ❌</span>
                  )}
                </div>
              </div>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loadingMessages ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Loader size="md" content="جاري تحميل الرسائل..." />
            </div>
          ) : roomMessages.length === 0 ? (
            <Message type="info" showIcon>
              لا توجد رسائل في هذه الغرفة.
            </Message>
          ) : (
            <List>
              {roomMessages.map(message => (
                <List.Item key={message.id} className="message-item">
                  <div className="message-header">
                    <div className="message-author">
                      {message.author && (
                        <>
                          <ProfileAvatar
                            src={message.author.avatar}
                            name={message.author.name}
                            size="xs"
                          />
                          <span className="author-name">{message.author.name}</span>
                        </>
                      )}
                    </div>
                    <div className="message-time">
                      <TimeAgo datetime={new Date(message.createdAt)} />
                    </div>
                  </div>
                  <div className="message-content">
                    {message.content}
                  </div>
                </List.Item>
              ))}
            </List>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowMessagesModal(false)} appearance="primary">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default RecordsPage;
