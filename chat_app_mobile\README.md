# تطبيق دردشة للجوال (Chat App Mobile)

تطبيق Flutter WebView مع دعم الإشعارات لعرض موقع الدردشة [https://toika-369.web.app](https://toika-369.web.app).

## الميزات

- عرض موقع الويب في تطبيق جوال
- دعم الإشعارات حتى عندما يكون التطبيق مغلقًا
- تسجيل الدخول التلقائي
- واجهة مستخدم بسيطة وأنيقة

## متطلبات النظام

- Flutter SDK (الإصدار 3.7.2 أو أعلى)
- Dart SDK (الإصدار 3.0.0 أو أعلى)
- Android Studio / VS Code
- جهاز Android (Android 5.0 أو أعلى) أو iOS (iOS 12.0 أو أعلى)

## التثبيت

1. استنساخ المستودع:
```bash
git clone https://github.com/yourusername/chat-app.git
cd chat-app/chat_app_mobile
```

2. تثبيت التبعيات:
```bash
flutter pub get
```

3. تشغيل التطبيق:
```bash
flutter run
```

## اختبار الإشعارات

لاختبار الإشعارات، يمكنك استخدام واجهة Firebase Cloud Messaging (FCM) أو إرسال طلب HTTP إلى FCM API:

### باستخدام cURL

```bash
curl -X POST -H "Authorization: key=YOUR_SERVER_KEY" -H "Content-Type: application/json" -d '{
  "to": "DEVICE_TOKEN",
  "notification": {
    "title": "عنوان الإشعار",
    "body": "محتوى الإشعار"
  },
  "data": {
    "type": "message",
    "id": "123"
  }
}' https://fcm.googleapis.com/fcm/send
```

استبدل `YOUR_SERVER_KEY` بمفتاح الخادم الخاص بك من Firebase و `DEVICE_TOKEN` برمز الجهاز.

## هيكل المشروع

```
lib/
├── main.dart                  # نقطة الدخول الرئيسية للتطبيق
├── screens/                   # شاشات التطبيق
│   ├── splash_screen.dart     # شاشة البداية
│   └── home_screen.dart       # الشاشة الرئيسية مع WebView
├── services/                  # خدمات التطبيق
│   ├── firebase_service.dart  # خدمة Firebase
│   ├── notification_service.dart # خدمة الإشعارات
│   └── webview_service.dart   # خدمة WebView
└── widgets/                   # عناصر واجهة المستخدم المشتركة
    └── loading_indicator.dart # مؤشر التحميل
```
