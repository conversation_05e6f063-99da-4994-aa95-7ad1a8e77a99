import React, { useEffect, useState } from 'react';
import { But<PERSON>, Message, toaster } from 'rsuite';
import { ref, set, get } from 'firebase/database';
import { database, auth } from '../../misc/firebase.config';

const SetAdminScript = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isDone, setIsDone] = useState(false);

  // تعيين مستخدم كمشرف في جميع الغرف
  const makeUserRoomAdmin = async (uid) => {
    try {
      // الحصول على جميع الغرف
      const roomsRef = ref(database, 'rooms');
      const roomsSnapshot = await get(roomsRef);

      if (roomsSnapshot.exists()) {
        const rooms = roomsSnapshot.val();
        let successCount = 0;
        let promises = [];

        // تعيين المستخدم كمشرف في كل غرفة
        for (const [roomId, roomData] of Object.entries(rooms)) {
          try {
            // إضافة وعد لتحديث حالة المشرف في الغرفة
            const promise = set(ref(database, `rooms/${roomId}/admins/${uid}`), true)
              .then(() => {
                console.log(`User ${uid} made admin in room ${roomId}`);
                successCount++;
              })
              .catch(roomError => {
                console.error(`Error making user admin in room ${roomId}:`, roomError);
              });

            promises.push(promise);
          } catch (error) {
            console.error(`Error creating promise for room ${roomId}:`, error);
          }
        }

        // انتظار جميع الوعود
        await Promise.allSettled(promises);

        console.log(`User ${uid} made admin in ${successCount} rooms out of ${Object.keys(rooms).length}`);
        return successCount;
      } else {
        console.log('No rooms found');
        return 0;
      }
    } catch (error) {
      console.error('Error making user room admin:', error);
      return 0;
    }
  };

  const makeUserAdmin = async () => {
    const uid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';

    setIsLoading(true);
    try {
      // التحقق من وجود المستخدم
      const userRef = ref(database, `users/${uid}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        // تعيين المستخدم كمشرف
        await set(ref(database, `users/${uid}/isAdmin`), true);

        // تعيين المستخدم كمشرف في جميع الغرف
        const roomCount = await makeUserRoomAdmin(uid);

        toaster.push(
          <Message type="success" closable duration={4000}>
            تم تعيين المستخدم كمشرف بنجاح وتم تعيينه كمشرف في {roomCount} غرفة
          </Message>
        );
        setIsDone(true);
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على مستخدم بهذا المعرف
          </Message>
        );
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في تعيين المستخدم كمشرف: {error.message}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h4>تعيين المستخدم كمشرف</h4>
      <p>UID: HdgvqlLyeagYNofjTtYGefH2wjD3</p>
      <Button
        appearance="primary"
        color="green"
        onClick={makeUserAdmin}
        disabled={isLoading || isDone}
      >
        {isLoading ? 'جاري التعيين...' : isDone ? 'تم التعيين بنجاح' : 'تعيين كمشرف'}
      </Button>
    </div>
  );
};

export default SetAdminScript;
