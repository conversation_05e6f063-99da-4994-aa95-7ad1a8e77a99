<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الخروج وإعادة تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #1DA1F2;
        }
        .info {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-right: 4px solid #1DA1F2;
        }
        .steps {
            background-color: #3a3b3c;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .step {
            margin-bottom: 10px;
            padding-right: 20px;
            position: relative;
        }
        .step:before {
            content: "";
            position: absolute;
            right: 0;
            top: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #1DA1F2;
        }
        button {
            width: 100%;
            padding: 15px;
            border-radius: 5px;
            border: none;
            background-color: #1DA1F2;
            color: white;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
            font-size: 16px;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #2d2d2d;
            display: none;
        }
        .success {
            border-right: 4px solid #4CAF50;
            color: #4CAF50;
        }
        .error {
            border-right: 4px solid #f44336;
            color: #f44336;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تسجيل الخروج وإعادة تسجيل الدخول</h1>
        
        <div class="info">
            لتفعيل صلاحيات المشرف، يجب عليك تسجيل الخروج وإعادة تسجيل الدخول. اتبع الخطوات التالية:
        </div>
        
        <div class="steps">
            <div class="step">انقر على زر "تسجيل الخروج" أدناه</div>
            <div class="step">انتظر حتى يتم تسجيل الخروج بنجاح</div>
            <div class="step">انقر على زر "إعادة تسجيل الدخول" للانتقال إلى صفحة تسجيل الدخول</div>
            <div class="step">قم بتسجيل الدخول باستخدام نفس الحساب</div>
            <div class="step">ستظهر صلاحيات المشرف الجديدة بعد إعادة تسجيل الدخول</div>
        </div>
        
        <button id="logoutBtn">تسجيل الخروج</button>
        <button id="loginBtn" style="display: none;">إعادة تسجيل الدخول</button>
        
        <div id="result" class="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "300804286264",
            appId: "1:300804286264:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const logoutBtn = document.getElementById('logoutBtn');
        const loginBtn = document.getElementById('loginBtn');
        const resultDiv = document.getElementById('result');
        
        // تسجيل الخروج
        async function logout() {
            logoutBtn.disabled = true;
            logoutBtn.innerHTML = '<span class="loading"></span> جاري تسجيل الخروج...';
            
            try {
                await firebase.auth().signOut();
                showResult('تم تسجيل الخروج بنجاح', true);
                
                // إظهار زر إعادة تسجيل الدخول
                logoutBtn.style.display = 'none';
                loginBtn.style.display = 'block';
            } catch (error) {
                console.error('Error signing out:', error);
                showResult(`خطأ في تسجيل الخروج: ${error.message}`, false);
                
                // إعادة تمكين زر تسجيل الخروج
                logoutBtn.disabled = false;
                logoutBtn.textContent = 'تسجيل الخروج';
            }
        }
        
        // إعادة تسجيل الدخول
        function redirectToLogin() {
            // الانتقال إلى صفحة تسجيل الدخول
            window.location.href = 'https://toika-369.web.app';
        }
        
        // عرض نتيجة العملية
        function showResult(message, isSuccess) {
            resultDiv.textContent = message;
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            resultDiv.style.display = 'block';
        }
        
        // إضافة مستمعي الأحداث للأزرار
        logoutBtn.addEventListener('click', logout);
        loginBtn.addEventListener('click', redirectToLogin);
        
        // التحقق من حالة المصادقة عند تحميل الصفحة
        firebase.auth().onAuthStateChanged(user => {
            if (!user) {
                // إذا لم يكن هناك مستخدم مسجل الدخول، إظهار زر إعادة تسجيل الدخول
                logoutBtn.style.display = 'none';
                loginBtn.style.display = 'block';
            }
        });
    </script>
</body>
</html>
