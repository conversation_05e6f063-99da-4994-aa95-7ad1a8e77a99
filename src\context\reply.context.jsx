import React, { createContext, useContext, useState } from 'react';

const ReplyContext = createContext();

export const ReplyProvider = ({ children }) => {
  const [replyMessage, setReplyMessage] = useState(null);

  const clearReply = () => {
    setReplyMessage(null);
  };

  const setReply = (message) => {
    setReplyMessage(message);
  };

  return (
    <ReplyContext.Provider value={{ replyMessage, setReply, clearReply }}>
      {children}
    </ReplyContext.Provider>
  );
};

export const useReply = () => useContext(ReplyContext);
