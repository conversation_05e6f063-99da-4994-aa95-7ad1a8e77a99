import React, { useState, useEffect } from 'react';
import { Button, Input, InputNumber, Modal, Form, Message, toaster, Divider, Loader } from 'rsuite';
import { useHistory } from 'react-router';
import { database, auth } from '../../misc/firebase.config';
import { ref, get, set, remove, onValue, off } from 'firebase/database';
import { useProfile } from '../../context/profile.context';
import '../LuckyWheel/luckywheel.scss';

const LuckyWheelAdmin = () => {
  const history = useHistory();
  const { profile } = useProfile();
  const [loading, setLoading] = useState(true);
  const [rewards, setRewards] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [currentReward, setCurrentReward] = useState(null);
  const [currentTask, setCurrentTask] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [rewardCodes, setRewardCodes] = useState([]);
  const [showCodesModal, setShowCodesModal] = useState(false);
  const [wheelEnabled, setWheelEnabled] = useState(true);

  // التحقق مما إذا كان المستخدم هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // دالة للعودة إلى صفحة العجلة
  const handleBack = () => {
    history.push('/lucky-wheel');
  };

  // التحقق من صلاحيات المستخدم
  useEffect(() => {
    if (!isOwner) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          ليس لديك صلاحية للوصول إلى هذه الصفحة
        </Message>
      );
      history.push('/');
    }
  }, [isOwner, history]);

  // تحميل بيانات العجلة
  useEffect(() => {
    if (!isOwner) return;

    const loadWheelData = async () => {
      try {
        // تحميل تكوين العجلة
        const wheelConfigRef = ref(database, '/lucky-wheel/config');

        onValue(wheelConfigRef, (snapshot) => {
          if (snapshot.exists()) {
            const config = snapshot.val();
            setRewards(config.rewards || []);
            // تحميل حالة تفعيل العجلة
            if (config.enabled !== undefined) {
              setWheelEnabled(config.enabled);
            }
          } else {
            setRewards([]);
          }
        });

        // تحميل المهام
        const tasksRef = ref(database, '/lucky-wheel/tasks');

        onValue(tasksRef, (snapshot) => {
          if (snapshot.exists()) {
            const tasksData = snapshot.val();
            const tasksArray = Object.keys(tasksData).map(taskId => ({
              id: taskId,
              ...tasksData[taskId]
            }));
            setTasks(tasksArray);
          } else {
            setTasks([]);
          }
        });

        // تحميل رموز الجوائز
        loadRewardCodes();

        setLoading(false);
      } catch (error) {
        console.error('Error loading wheel data:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            حدث خطأ أثناء تحميل البيانات
          </Message>
        );
        setLoading(false);
      }
    };

    loadWheelData();

    return () => {
      // إلغاء الاستماع عند إلغاء تحميل المكون
      off(ref(database, '/lucky-wheel/config'));
      off(ref(database, '/lucky-wheel/tasks'));
    };
  }, [isOwner, history]);

  // تحميل رموز الجوائز
  const loadRewardCodes = async () => {
    try {
      const usersRef = ref(database, '/lucky-wheel/users');
      const usersSnapshot = await get(usersRef);

      if (usersSnapshot.exists()) {
        const users = usersSnapshot.val();
        const allCodes = [];

        // جمع جميع رموز الجوائز من جميع المستخدمين
        Object.keys(users).forEach(userId => {
          const userData = users[userId];
          if (userData.rewards) {
            Object.keys(userData.rewards).forEach(rewardId => {
              const reward = userData.rewards[rewardId];
              allCodes.push({
                userId,
                rewardId,
                code: reward.code,
                rewardText: reward.rewardText,
                timestamp: reward.timestamp,
                claimed: reward.claimed
              });
            });
          }
        });

        // ترتيب الرموز حسب الوقت (الأحدث أولاً)
        allCodes.sort((a, b) => {
          if (!a.timestamp || !b.timestamp) return 0;
          return b.timestamp - a.timestamp;
        });

        setRewardCodes(allCodes);
      } else {
        setRewardCodes([]);
      }
    } catch (error) {
      console.error('Error loading reward codes:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تحميل رموز الجوائز
        </Message>
      );
    }
  };

  // فتح نافذة إضافة/تعديل جائزة
  const openRewardModal = (reward = null) => {
    setCurrentReward(reward || { text: '', color: '#FF8C00', probability: 10 });
    setIsEditing(!!reward);
    setShowRewardModal(true);
  };

  // فتح نافذة إضافة/تعديل مهمة
  const openTaskModal = (task = null) => {
    setCurrentTask(task || { title: '', description: '', reward: 1, type: 'custom' });
    setIsEditing(!!task);
    setShowTaskModal(true);
  };

  // حفظ الجائزة
  const saveReward = async () => {
    try {
      const updatedRewards = [...rewards];

      if (isEditing) {
        // تعديل جائزة موجودة
        const index = updatedRewards.findIndex(r => r.id === currentReward.id);
        if (index !== -1) {
          updatedRewards[index] = currentReward;
        }
      } else {
        // إضافة جائزة جديدة
        const newId = Math.max(0, ...updatedRewards.map(r => r.id)) + 1;
        updatedRewards.push({ ...currentReward, id: newId });
      }

      // حفظ التغييرات في قاعدة البيانات
      await set(ref(database, '/lucky-wheel/config/rewards'), updatedRewards);

      setShowRewardModal(false);
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حفظ الجائزة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error saving reward:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء حفظ الجائزة
        </Message>
      );
    }
  };

  // حذف جائزة
  const deleteReward = async (rewardId) => {
    try {
      const updatedRewards = rewards.filter(r => r.id !== rewardId);

      // حفظ التغييرات في قاعدة البيانات
      await set(ref(database, '/lucky-wheel/config/rewards'), updatedRewards);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حذف الجائزة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error deleting reward:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء حذف الجائزة
        </Message>
      );
    }
  };

  // حفظ المهمة
  const saveTask = async () => {
    try {
      if (isEditing) {
        // تعديل مهمة موجودة
        await set(ref(database, `/lucky-wheel/tasks/${currentTask.id}`), {
          title: currentTask.title,
          description: currentTask.description,
          reward: currentTask.reward,
          type: currentTask.type
        });
      } else {
        // إضافة مهمة جديدة
        const taskId = `task${Date.now()}`;
        await set(ref(database, `/lucky-wheel/tasks/${taskId}`), {
          title: currentTask.title,
          description: currentTask.description,
          reward: currentTask.reward,
          type: currentTask.type
        });
      }

      setShowTaskModal(false);
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حفظ المهمة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error saving task:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء حفظ المهمة
        </Message>
      );
    }
  };

  // حذف مهمة
  const deleteTask = async (taskId) => {
    try {
      await remove(ref(database, `/lucky-wheel/tasks/${taskId}`));

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حذف المهمة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error deleting task:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء حذف المهمة
        </Message>
      );
    }
  };

  // تحديث حالة الجائزة (مطالبة/غير مطالب بها)
  const toggleRewardClaimed = async (userId, rewardId, claimed) => {
    try {
      await set(ref(database, `/lucky-wheel/users/${userId}/rewards/${rewardId}/claimed`), !claimed);

      // تحديث القائمة المحلية
      setRewardCodes(rewardCodes.map(code => {
        if (code.userId === userId && code.rewardId === rewardId) {
          return { ...code, claimed: !claimed };
        }
        return code;
      }));

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تحديث حالة الجائزة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error updating reward status:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تحديث حالة الجائزة
        </Message>
      );
    }
  };

  if (loading) {
    return (
      <div className="wheel-admin-page">
        <Loader center vertical size="md" content="جاري التحميل..." speed="slow" />
      </div>
    );
  }



  return (
    <div className="wheel-admin-page" style={{ height: '100vh', position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1000, background: '#121212', overflowY: 'auto' }}>
      <div className="admin-header">
        <Button appearance="subtle" onClick={handleBack} className="back-button">
          <i className="fas fa-arrow-right"></i> العودة
        </Button>
        <h2>إدارة عجلة الحظ</h2>
        <div className="admin-actions">
          <Button
            appearance="primary"
            color={wheelEnabled ? "red" : "green"}
            onClick={async () => {
              try {
                // تحديث حالة تفعيل العجلة في قاعدة البيانات
                await set(ref(database, '/lucky-wheel/config/enabled'), !wheelEnabled);
                setWheelEnabled(!wheelEnabled);
                toaster.push(
                  <Message type="success" closable duration={4000}>
                    تم {wheelEnabled ? 'إيقاف' : 'تفعيل'} عجلة الحظ بنجاح
                  </Message>
                );
              } catch (error) {
                console.error('Error toggling wheel state:', error);
                toaster.push(
                  <Message type="error" closable duration={4000}>
                    حدث خطأ أثناء {wheelEnabled ? 'إيقاف' : 'تفعيل'} عجلة الحظ
                  </Message>
                );
              }
            }}
            style={{ marginLeft: '10px' }}
          >
            {wheelEnabled ? 'إيقاف عجلة الحظ' : 'تفعيل عجلة الحظ'}
          </Button>
          <Button
            appearance="primary"
            color="blue"
            onClick={() => setShowCodesModal(true)}
            style={{ marginRight: '10px' }}
          >
            رموز الجوائز
          </Button>
        </div>
      </div>

      {/* <div className="admin-section">
        <h3>الجوائز</h3>
        <div className="rewards-list">
          {rewards.map(reward => (
            <div key={reward.id} className="reward-item">
              <div className="color-preview" style={{ backgroundColor: reward.color }}></div>
              <div className="reward-details">
                <div className="reward-text">{reward.text}</div>
                <div className="reward-probability">الاحتمالية: {reward.probability}%</div>
              </div>
              <div className="reward-actions">
                <Button appearance="ghost" size="sm" onClick={() => openRewardModal(reward)}>
                  تعديل
                </Button>
                <Button appearance="ghost" color="red" size="sm" onClick={() => deleteReward(reward.id)}>
                  حذف
                </Button>
              </div>
            </div>
          ))}
        </div>
        <Button appearance="primary" block onClick={() => openRewardModal()} className="mt-3">
          إضافة جائزة جديدة
        </Button>
      </div> */}

      <div className="admin-section">
        <h3>المهام اليومية</h3>
        <div className="tasks-list">
          {tasks.map(task => (
            <div key={task.id} className="task-item">
              <div className="task-details">
                <div className="task-info">
                  <div className="task-title">{task.title}</div>
                  <div className="task-description">{task.description}</div>
                  <div className="task-reward">
                    <i className="fas fa-ticket-alt"></i>
                    <span>{task.reward} تذكرة</span>
                  </div>
                </div>
                <div className="task-actions">
                  <Button appearance="ghost" size="sm" onClick={() => openTaskModal(task)}>
                    تعديل
                  </Button>
                  <Button appearance="ghost" color="red" size="sm" onClick={() => deleteTask(task.id)}>
                    حذف
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
        <Button appearance="primary" block onClick={() => openTaskModal()} className="mt-3">
          إضافة مهمة جديدة
        </Button>
      </div>

      {/* نافذة إضافة/تعديل جائزة */}
      <Modal open={showRewardModal} onClose={() => setShowRewardModal(false)}>
        <Modal.Header>
          <Modal.Title>{isEditing ? 'تعديل جائزة' : 'إضافة جائزة جديدة'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form fluid>
            <Form.Group>
              <Form.ControlLabel>نص الجائزة</Form.ControlLabel>
              <Form.Control
                name="text"
                value={currentReward?.text || ''}
                onChange={value => setCurrentReward({ ...currentReward, text: value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>لون الجائزة</Form.ControlLabel>
              <Form.Control
                name="color"
                value={currentReward?.color || '#FF8C00'}
                onChange={value => setCurrentReward({ ...currentReward, color: value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>احتمالية الفوز (%)</Form.ControlLabel>
              <Form.Control
                name="probability"
                accepter={InputNumber}
                min={1}
                max={100}
                value={currentReward?.probability || 10}
                onChange={value => setCurrentReward({ ...currentReward, probability: value })}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowRewardModal(false)} appearance="subtle">
            إلغاء
          </Button>
          <Button onClick={saveReward} appearance="primary">
            حفظ
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة إضافة/تعديل مهمة */}
      <Modal open={showTaskModal} onClose={() => setShowTaskModal(false)}>
        <Modal.Header>
          <Modal.Title>{isEditing ? 'تعديل مهمة' : 'إضافة مهمة جديدة'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form fluid>
            <Form.Group>
              <Form.ControlLabel>عنوان المهمة</Form.ControlLabel>
              <Form.Control
                name="title"
                value={currentTask?.title || ''}
                onChange={value => setCurrentTask({ ...currentTask, title: value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>وصف المهمة</Form.ControlLabel>
              <Form.Control
                name="description"
                value={currentTask?.description || ''}
                onChange={value => setCurrentTask({ ...currentTask, description: value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>عدد التذاكر</Form.ControlLabel>
              <Form.Control
                name="reward"
                accepter={InputNumber}
                min={1}
                max={10}
                value={currentTask?.reward || 1}
                onChange={value => setCurrentTask({ ...currentTask, reward: value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>نوع المهمة</Form.ControlLabel>
              <Form.Control
                name="type"
                value={currentTask?.type || 'custom'}
                onChange={value => setCurrentTask({ ...currentTask, type: value })}
              />
              <Form.HelpText>
                أنواع المهام: login (تسجيل الدخول)، share (مشاركة)، custom (مخصص)
              </Form.HelpText>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowTaskModal(false)} appearance="subtle">
            إلغاء
          </Button>
          <Button onClick={saveTask} appearance="primary">
            حفظ
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة رموز الجوائز */}
      <Modal open={showCodesModal} onClose={() => setShowCodesModal(false)} size="lg">
        <Modal.Header>
          <Modal.Title>رموز الجوائز</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="reward-codes-list">
            {rewardCodes.length === 0 ? (
              <p className="text-center">لا توجد رموز جوائز حتى الآن</p>
            ) : (
              rewardCodes.map((code, index) => (
                <div key={index} className={`reward-code-item ${code.claimed ? 'claimed' : ''}`}>
                  <div className="code-info">
                    <div className="code-value">{code.code}</div>
                    <div className="code-details">
                      <span className="code-reward">{code.rewardText}</span>
                      <span className="code-user">المستخدم: {code.userId}</span>
                      <span className="code-date">
                        {code.timestamp ? new Date(code.timestamp).toLocaleString('ar-SA') : 'غير معروف'}
                      </span>
                    </div>
                  </div>
                  <Button
                    appearance={code.claimed ? 'ghost' : 'primary'}
                    color={code.claimed ? 'green' : 'blue'}
                    size="sm"
                    onClick={() => toggleRewardClaimed(code.userId, code.rewardId, code.claimed)}
                  >
                    {code.claimed ? 'تم الصرف' : 'صرف الجائزة'}
                  </Button>
                </div>
              ))
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowCodesModal(false)} appearance="primary">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>

    </div>
  );
};

export default LuckyWheelAdmin;
