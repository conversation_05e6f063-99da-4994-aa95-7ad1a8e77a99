import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getDatabase } from "firebase/database";
import { getStorage } from "firebase/storage";
import { getAnalytics } from "firebase/analytics";

const config = {
  apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
  authDomain: "toika-369.firebaseapp.com",
  databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
  projectId: "toika-369",
  storageBucket: "toika-369.appspot.com",
  messagingSenderId: "************",
  appId: "1:************:web:29304874a53644e25403cd",
  measurementId: "G-W46F5TY408"
};

const app = initializeApp(config);
export const auth = getAuth(app);

// تكوين مزود مصادقة Google
export const googleProvider = new GoogleAuthProvider();
// إضافة نطاقات الوصول الضرورية
googleProvider.addScope('email');
googleProvider.addScope('profile');
// تعيين معلمات مخصصة بسيطة
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

export const database = getDatabase(app);
export const storage = getStorage(app);
export const analytics = getAnalytics(app);
