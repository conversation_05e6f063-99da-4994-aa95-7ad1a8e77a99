import 'dart:convert';
import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  static AndroidNotificationChannel? _channel;

  // Initialize notification service
  static Future<void> initialize() async {
    // Define Android notification channel
    _channel = const AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      description: 'This channel is used for important notifications.',
      importance: Importance.high,
    );

    // Create the Android notification channel
    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(_channel!);

    // Initialize settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
          onDidReceiveLocalNotification: (id, title, body, payload) async {
            // Handle iOS local notification
          },
        );

    final InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        print('Notification tapped: ${response.payload}');
      },
    );
  }

  // Show local notification
  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    String? icon,
  }) async {
    await _notificationsPlugin.show(
      DateTime.now().millisecond,
      title,
      body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          _channel!.id,
          _channel!.name,
          channelDescription: _channel!.description,
          importance: Importance.high,
          priority: Priority.high,
          icon: icon ?? '@mipmap/ic_launcher',
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: payload,
    );
  }

  // Handle Firebase message
  static Future<void> handleFirebaseMessage(RemoteMessage message) async {
    RemoteNotification? notification = message.notification;

    if (notification != null) {
      // Get notification details
      String? androidIcon;
      if (Platform.isAndroid && message.notification?.android != null) {
        androidIcon = message.notification?.android?.smallIcon;
      }

      await showNotification(
        title: notification.title ?? 'New Notification',
        body: notification.body ?? '',
        payload: json.encode(message.data),
        icon: androidIcon,
      );
    } else if (message.data.isNotEmpty) {
      // Handle data-only messages
      await showNotification(
        title: message.data['title'] ?? 'New Message',
        body: message.data['body'] ?? 'You have a new message',
        payload: json.encode(message.data),
      );
    }
  }

  // Register device token with server
  static Future<void> registerDeviceToken(String token, String userId) async {
    try {
      // Save token and user ID to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
      await prefs.setString('user_id', userId);

      // Send token to Firebase Realtime Database
      final url =
          'https://toika-369-default-rtdb.firebaseio.com/device_tokens/$userId.json';

      final response = await http.put(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'token': token,
          'platform': Platform.isAndroid ? 'android' : 'ios',
          'updated_at': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to register device token: ${response.body}');
      }

      print('Device token registered successfully');
    } catch (e) {
      print('Error registering device token: $e');
    }
  }

  // Get saved device token
  static Future<String?> getSavedDeviceToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('fcm_token');
  }

  // Get saved user ID
  static Future<String?> getSavedUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_id');
  }

  // Clear notification
  static Future<void> clearNotification(int id) async {
    await _notificationsPlugin.cancel(id);
  }

  // Clear all notifications
  static Future<void> clearAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }
}
