import React, { useCallback } from "react";
import { Drawer, Message, toaster, Whisper, Tooltip } from "rsuite";
import { useMediaQuery, useModalState } from "../../misc/custom-hooks";
import Dashboard from ".";
import { auth, database } from "../../misc/firebase.config";
import { ref, set } from "firebase/database";
import { isOfflineForDatabase } from "../../context/profile.context";
import { useProfile } from "../../context/profile.context";
import ProfileAvatar from "../ProfileAvatar";

const ProfileButton = () => {
  const { isOpen, close, open } = useModalState();
  const isMobile = useMediaQuery("(max-width: 992px)");
  const { profile } = useProfile();

  const onSignOut = useCallback(() => {
    set(ref(database, `/status/${auth.currentUser.uid}`), isOfflineForDatabase)
      .then(() => {
        // إغلاق النافذة أولاً
        close();

        // إزالة حالة المصادقة من التخزين المحلي قبل تسجيل الخروج
        localStorage.removeItem('authState');

        // تسجيل الخروج
        auth.signOut();

        toaster.push(
          <Message type="info" closable duration={4000}>
            تم تسجيل الخروج
          </Message>
        );

        // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
        window.location.href = '/signin';
      })
      .catch((err) => {
        toaster.push(
          <Message type="error" closable duration={4000}>
            {err.message}
          </Message>
        );
      });
  }, [close]);

  return (
    <>
      <Whisper
        placement="top"
        trigger="hover"
        speaker={<Tooltip>الملف الشخصي</Tooltip>}
      >
        <div className="profile-button" onClick={open}>
          <div className="profile-button-avatar">
            <ProfileAvatar
              src={profile.avatar}
              name={profile.name}
              className="profile-avatar"
            />
          </div>
          <div className="profile-button-info">
            <div className="profile-button-name">{profile.name}</div>
            <div className="profile-button-email">{profile.email}</div>
          </div>
        </div>
      </Whisper>

      <Drawer
        size={isMobile ? "full" : "xs"}
        open={isOpen}
        onClose={close}
        placement="right"
        className="dark-drawer"
      >
        <Dashboard onSignOut={onSignOut} />
      </Drawer>
    </>
  );
};

export default ProfileButton;
