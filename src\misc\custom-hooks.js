import { useCallback, useEffect, useRef, useState } from "react";
import { database } from './firebase.config';
import { off, onValue, ref } from 'firebase/database';

export function useModalState(defaultValue = false) {
  const [isOpen, setIsOpen] = useState(defaultValue);

  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);

  return { isOpen, open, close };
}

export const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(
    () => window.matchMedia(query).matches
  );

  useEffect(() => {
    const queryList = window.matchMedia(query);
    setMatches(queryList.matches);

    // استخدام addEventListener بدلاً من addListener (الذي تم إهماله)
    const listener = (evt) => setMatches(evt.matches);

    // استخدام الطريقة الحديثة للإضافة وإزالة المستمعين
    queryList.addEventListener('change', listener);
    return () => queryList.removeEventListener('change', listener);
  }, [query]);

  return matches;
};

export function usePresence(uid) {
  const [presence, setPresence] = useState(null);

  useEffect(() => {
    const userStatusRef = ref(database, `/status/${uid}`);

    onValue(userStatusRef, snap => {
      if (snap.exists()) {
        const data = snap.val();

        setPresence(data);
      }
    });

    return () => {
      off(userStatusRef);
    };
  }, [uid]);

  return presence;
}

export function useHover() {
  const [isHovered, setIsHovered] = useState(false);

  const elementRef = useRef(null);

  const handleMouseOver = () => setIsHovered(true);
  const handleMouseOut = () => setIsHovered(false);

  useEffect(() => {
    const node = elementRef.current;
    if (node) {
      node.addEventListener('mouseover', handleMouseOver);
      node.addEventListener('mouseout', handleMouseOut);

      // تنظيف عند إلغاء تحميل المكون
      return () => {
        node.removeEventListener('mouseover', handleMouseOver);
        node.removeEventListener('mouseout', handleMouseOut);
      };
    }
    // تجنب استخدام elementRef.current كتبعية مباشرة
  }, [handleMouseOver, handleMouseOut]);

  return [elementRef, isHovered];
}
