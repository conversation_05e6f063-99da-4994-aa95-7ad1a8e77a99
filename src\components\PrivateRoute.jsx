import React, { useEffect, useState } from "react";
import { Redirect, Route } from "react-router";
import { Container, Loader } from "rsuite";
import { useProfile } from "../context/profile.context";

const PrivateRoute = ({ children, ...routeProps }) => {
  const { profile, isLoading } = useProfile();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // التحقق من حالة المصادقة في التخزين المحلي
  useEffect(() => {
    const checkLocalAuth = () => {
      const savedAuthState = localStorage.getItem('authState');
      if (savedAuthState === 'authenticated') {
        console.log('Found authenticated state in localStorage');
        setIsAuthenticated(true);
      }
      setIsCheckingAuth(false);
    };

    checkLocalAuth();
  }, []);

  if (isLoading || isCheckingAuth) {
    return (
      <Container>
        <Loader center vertical size="md" content="جاري التحميل..." speed="slow" />
      </Container>
    );
  }

  // إعادة التوجيه إذا كان المستخدم غير مصادق (لا من السياق ولا من التخزين المحلي)
  if (!profile && !isAuthenticated && !isLoading) {
    console.log('User is not authenticated, redirecting to sign in page');
    // مسح أي بيانات مصادقة قديمة
    localStorage.removeItem('authState');
    return <Redirect to={"/signin"} />;
  }

  return <Route {...routeProps}>{children}</Route>;
};

export default PrivateRoute;
