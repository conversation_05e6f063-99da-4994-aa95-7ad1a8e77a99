import React, { useEffect, useState } from 'react';
import { Redirect } from 'react-router';
import { Container, Loader, Message } from 'rsuite';
import { auth } from '../misc/firebase.config';

const AuthBridge = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // التحقق من وجود مستخدم مسجل الدخول
        if (auth.currentUser) {
          console.log('User is already signed in:', auth.currentUser.email);
          localStorage.setItem('authState', 'authenticated');
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }

        // التحقق من وجود بيانات مصادقة في التخزين المحلي
        const savedAuthState = localStorage.getItem('authState');
        if (savedAuthState === 'authenticated') {
          console.log('Found saved auth state');
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }

        // إذا لم يكن هناك مستخدم مسجل الدخول، انتظر لمدة ثانية واحدة ثم تحقق مرة أخرى
        setTimeout(() => {
          if (auth.currentUser) {
            console.log('User signed in after delay:', auth.currentUser.email);
            localStorage.setItem('authState', 'authenticated');
            setIsAuthenticated(true);
          } else {
            console.log('No user found after delay');
            setError('لم يتم العثور على مستخدم مسجل الدخول. يرجى المحاولة مرة أخرى.');
          }
          setIsLoading(false);
        }, 2000);
      } catch (err) {
        console.error('Auth bridge error:', err);
        setError(`خطأ في التحقق من المصادقة: ${err.message}`);
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  if (isLoading) {
    return (
      <Container>
        <div style={{
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          padding: '20px'
        }}>
          <h3>جاري التحقق من المصادقة...</h3>
          <Loader center size="lg" content="يرجى الانتظار" speed="slow" />
          <p style={{ marginTop: '20px' }}>
            إذا استمرت هذه الشاشة لفترة طويلة، يرجى النقر على زر العودة والمحاولة مرة أخرى.
          </p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <div style={{
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          padding: '20px'
        }}>
          <Message type="error" header="خطأ في المصادقة">
            {error}
          </Message>
          <button
            onClick={() => window.location.href = '/signin'}
            style={{
              marginTop: '20px',
              padding: '10px 20px',
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            العودة إلى صفحة تسجيل الدخول
          </button>
        </div>
      </Container>
    );
  }

  if (isAuthenticated) {
    // التوجيه مباشرة إلى الصفحة الرئيسية بدون إعادة تحميل
    console.log('Redirecting to home page directly');
    return <Redirect to="/" />;
  }

  return <Redirect to="/signin" />;
};

export default AuthBridge;
