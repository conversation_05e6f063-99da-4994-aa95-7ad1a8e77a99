const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

// دالة لتعيين مستخدم كمشرف
exports.makeAdmin = functions.https.onRequest(async (req, res) => {
  // تمكين CORS
  res.set('Access-Control-Allow-Origin', '*');
  
  // معرف المستخدم المراد تعيينه كمشرف
  const uid = req.query.uid || 'HdgvqlLyeagYNofjTtYGefH2wjD3';
  
  try {
    // التحقق من وجود المستخدم
    const userRef = admin.database().ref(`users/${uid}`);
    const snapshot = await userRef.once('value');
    
    if (snapshot.exists()) {
      const userData = snapshot.val();
      
      // التحقق من حالة المشرف الحالية
      const isAdmin = userData.isAdmin === true;
      
      if (isAdmin) {
        return res.status(200).json({ 
          success: true, 
          message: `المستخدم ${userData.name || uid} مشرف بالفعل` 
        });
      } else {
        // تعيين المستخدم كمشرف
        await userRef.update({ isAdmin: true });
        
        // التحقق من نجاح العملية
        const updatedSnapshot = await userRef.once('value');
        const updatedUserData = updatedSnapshot.val();
        const updatedIsAdmin = updatedUserData.isAdmin === true;
        
        if (updatedIsAdmin) {
          return res.status(200).json({ 
            success: true, 
            message: `تم تعيين المستخدم ${userData.name || uid} كمشرف بنجاح` 
          });
        } else {
          return res.status(500).json({ 
            success: false, 
            error: `فشل في تعيين المستخدم ${userData.name || uid} كمشرف` 
          });
        }
      }
    } else {
      return res.status(404).json({ 
        success: false, 
        error: `لم يتم العثور على المستخدم ${uid}` 
      });
    }
  } catch (error) {
    console.error('Error making user admin:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// دالة لإزالة صلاحية المشرف من مستخدم
exports.removeAdmin = functions.https.onRequest(async (req, res) => {
  // تمكين CORS
  res.set('Access-Control-Allow-Origin', '*');
  
  // معرف المستخدم المراد إزالة صلاحية المشرف منه
  const uid = req.query.uid;
  
  // التحقق من وجود معرف المستخدم
  if (!uid) {
    return res.status(400).json({ 
      success: false, 
      error: 'يجب توفير معرف المستخدم (uid)' 
    });
  }
  
  // التحقق من أن المستخدم ليس المالك
  if (uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1') {
    return res.status(403).json({ 
      success: false, 
      error: 'لا يمكن إزالة صلاحية المشرف من مالك الموقع' 
    });
  }
  
  try {
    // التحقق من وجود المستخدم
    const userRef = admin.database().ref(`users/${uid}`);
    const snapshot = await userRef.once('value');
    
    if (snapshot.exists()) {
      const userData = snapshot.val();
      
      // التحقق من حالة المشرف الحالية
      const isAdmin = userData.isAdmin === true;
      
      if (!isAdmin) {
        return res.status(200).json({ 
          success: true, 
          message: `المستخدم ${userData.name || uid} ليس مشرفًا بالفعل` 
        });
      } else {
        // إزالة صلاحية المشرف
        await userRef.update({ isAdmin: false });
        
        return res.status(200).json({ 
          success: true, 
          message: `تم إزالة صلاحية المشرف من المستخدم ${userData.name || uid} بنجاح` 
        });
      }
    } else {
      return res.status(404).json({ 
        success: false, 
        error: `لم يتم العثور على المستخدم ${uid}` 
      });
    }
  } catch (error) {
    console.error('Error removing admin:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});
