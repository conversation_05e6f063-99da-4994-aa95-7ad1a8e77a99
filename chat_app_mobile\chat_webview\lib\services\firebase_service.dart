import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Define a top-level function to handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase if not already initialized
  await Firebase.initializeApp();
  debugPrint('Handling a background message: ${message.messageId}');
  
  // Show notification for background message
  _showNotificationFromMessage(message);
}

// Show notification from message
void _showNotificationFromMessage(RemoteMessage message) {
  if (message.notification != null) {
    debugPrint('Message notification: ${message.notification!.title}');
  }
}

class FirebaseService {
  static FirebaseMessaging? _firebaseMessaging;
  static String? _fcmToken;

  // Initialize Firebase
  static Future<void> initialize() async {
    try {
      // Initialize Firebase Messaging
      _firebaseMessaging = FirebaseMessaging.instance;
      
      // Set up background message handler
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
      
      // Request permission
      await _requestPermission();
      
      // Set up foreground notification handlers
      _setupForegroundNotificationHandlers();
      
      // Get FCM token
      await getFcmToken();
      
      debugPrint('Firebase initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Firebase: $e');
    }
  }
  
  // Request permission for notifications
  static Future<void> _requestPermission() async {
    final settings = await _firebaseMessaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
    );
    
    debugPrint('User granted permission: ${settings.authorizationStatus}');
  }
  
  // Set up foreground notification handlers
  static void _setupForegroundNotificationHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Message data: ${message.data}');
      
      if (message.notification != null) {
        debugPrint('Message also contained a notification: ${message.notification}');
        
        // Show notification
        _showNotificationFromMessage(message);
      }
    });
  }
  
  // Get FCM token
  static Future<String?> getFcmToken() async {
    if (_fcmToken != null) {
      return _fcmToken;
    }

    try {
      _fcmToken = await _firebaseMessaging!.getToken();
      debugPrint('FCM Token: $_fcmToken');

      // Save token to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', _fcmToken!);

      // Get user ID from shared preferences
      final userId = prefs.getString('user_id');
      
      // Register token with server if user ID is available
      if (userId != null && _fcmToken != null) {
        await registerTokenWithServer(userId, _fcmToken!);
      }

      // Set up token refresh listener
      FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
        _fcmToken = newToken;
        debugPrint('FCM Token refreshed: $_fcmToken');

        // Save refreshed token to shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', _fcmToken!);

        // Get user ID from shared preferences
        final userId = prefs.getString('user_id');
        
        // Register refreshed token with server if user ID is available
        if (userId != null) {
          await registerTokenWithServer(userId, _fcmToken!);
        }
      });

      return _fcmToken;
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }
  
  // Register token with server
  static Future<void> registerTokenWithServer(String userId, String token) async {
    try {
      // Prepare the URL for the Firebase Realtime Database
      final url = 'https://toika-369-default-rtdb.firebaseio.com/device_tokens/$userId.json';
      
      // Send the token to the server
      final response = await http.put(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'token': token,
          'platform': Platform.isAndroid ? 'android' : 'ios',
          'updated_at': DateTime.now().toIso8601String(),
        }),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to register device token: ${response.body}');
      }
      
      debugPrint('Device token registered successfully with server');
    } catch (e) {
      debugPrint('Error registering device token with server: $e');
    }
  }
  
  // Subscribe to a topic
  static Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging!.subscribeToTopic(topic);
    debugPrint('Subscribed to topic: $topic');
  }
  
  // Unsubscribe from a topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging!.unsubscribeFromTopic(topic);
    debugPrint('Unsubscribed from topic: $topic');
  }
}
