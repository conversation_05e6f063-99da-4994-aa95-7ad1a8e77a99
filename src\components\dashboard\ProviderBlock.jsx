import React, { useState } from "react";
import { Message, Tag, toaster, But<PERSON>, Tooltip, Whisper } from "rsuite";
import { auth } from "../../misc/firebase.config";
import CopyIcon from "@rsuite/icons/Copy";

const ProviderBlock = () => {
  const [copied, setCopied] = useState(false);
  const uid = auth.currentUser.uid;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(uid).then(() => {
      setCopied(true);
      toaster.push(
        <Message type="success" closable duration={2000}>
          تم نسخ معرف المستخدم بنجاح
        </Message>
      );

      // إعادة تعيين حالة النسخ بعد ثانيتين
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    });
  };

  return (
    <div className="user-uid-container">
      <Tag color="blue" appearance="primary" className="uid-tag">
        <span className="uid-label">معرف المستخدم (UID):</span>
        <span className="uid-value">{uid}</span>
        <Whisper
          placement="top"
          trigger="hover"
          speaker={<Tooltip>{copied ? "تم النسخ!" : "نسخ المعرف"}</Tooltip>}
        >
          <Button
            appearance="subtle"
            className="copy-uid-btn"
            onClick={copyToClipboard}
            style={{ padding: '0 5px', marginRight: '5px' }}
          >
            <CopyIcon />
          </Button>
        </Whisper>
      </Tag>
    </div>
  );
};

export default ProviderBlock;
