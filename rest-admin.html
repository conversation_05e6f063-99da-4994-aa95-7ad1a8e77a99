<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>تعيين مشرف باستخدام REST API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #1DA1F2;
            margin-bottom: 20px;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        .log {
            background-color: #3a3b3c;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
            text-align: left;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #1DA1F2;
        }
        button {
            background-color: #1DA1F2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعيين مشرف باستخدام REST API</h1>
        <div class="status" id="status">
            انقر على الزر لتعيين المستخدم كمشرف
        </div>
        <button id="makeAdminBtn">تعيين كمشرف</button>
        <div class="log" id="log"></div>
    </div>

    <script>
        // الحصول على عناصر DOM
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');
        const makeAdminBtn = document.getElementById('makeAdminBtn');

        // إضافة رسالة إلى السجل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // تعيين المستخدم كمشرف
        async function makeAdmin() {
            const uid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';
            const databaseURL = 'https://toika-369-default-rtdb.firebaseio.com';
            const authToken = ''; // يجب الحصول على رمز المصادقة من Firebase
            
            makeAdminBtn.disabled = true;
            
            try {
                log(`بدء العملية: تعيين المستخدم ${uid} كمشرف...`);
                
                // التحقق من وجود المستخدم
                log(`التحقق من وجود المستخدم ${uid}...`);
                const getUserResponse = await fetch(`${databaseURL}/users/${uid}.json`);
                const userData = await getUserResponse.json();
                
                if (userData) {
                    const userName = userData.name || uid;
                    log(`تم العثور على المستخدم: ${userName}`, 'success');
                    
                    // التحقق من حالة المشرف الحالية
                    const isAdmin = userData.isAdmin === true;
                    log(`حالة المشرف الحالية: ${isAdmin ? 'مشرف' : 'مستخدم عادي'}`);
                    
                    if (isAdmin) {
                        log('المستخدم مشرف بالفعل', 'info');
                        statusElement.innerHTML = `<div class="success">المستخدم ${userName} مشرف بالفعل</div>`;
                    } else {
                        // تعيين المستخدم كمشرف
                        log('تعيين المستخدم كمشرف...');
                        
                        const updateResponse = await fetch(`${databaseURL}/users/${uid}/isAdmin.json`, {
                            method: 'PUT',
                            body: JSON.stringify(true)
                        });
                        
                        if (updateResponse.ok) {
                            log('تم تعيين المستخدم كمشرف بنجاح', 'success');
                            statusElement.innerHTML = `<div class="success">تم تعيين المستخدم ${userName} كمشرف بنجاح</div>`;
                        } else {
                            const errorData = await updateResponse.json();
                            log(`فشل في تعيين المستخدم كمشرف: ${JSON.stringify(errorData)}`, 'error');
                            statusElement.innerHTML = `<div class="error">فشل في تعيين المستخدم ${userName} كمشرف</div>`;
                        }
                    }
                } else {
                    log(`لم يتم العثور على المستخدم ${uid}`, 'error');
                    statusElement.innerHTML = `<div class="error">لم يتم العثور على المستخدم ${uid}</div>`;
                }
            } catch (error) {
                log(`خطأ: ${error.message}`, 'error');
                statusElement.innerHTML = `<div class="error">خطأ: ${error.message}</div>`;
            } finally {
                makeAdminBtn.disabled = false;
            }
        }
        
        // إضافة مستمع حدث للزر
        makeAdminBtn.addEventListener('click', makeAdmin);
    </script>
</body>
</html>
