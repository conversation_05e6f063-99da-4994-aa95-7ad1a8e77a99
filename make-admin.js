const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// تهيئة Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://toika-369-default-rtdb.firebaseio.com'
});

// معرف المستخدم المراد تعيينه كمشرف
const uid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';

// تعيين المستخدم كمشرف
async function makeAdmin() {
  try {
    console.log(`بدء العملية: تعيين المستخدم ${uid} كمشرف...`);
    
    // التحقق من وجود المستخدم
    console.log(`التحقق من وجود المستخدم ${uid}...`);
    const userRef = admin.database().ref(`users/${uid}`);
    const snapshot = await userRef.once('value');
    
    if (snapshot.exists()) {
      const userData = snapshot.val();
      console.log(`تم العثور على المستخدم: ${userData.name || uid}`);
      
      // التحقق من حالة المشرف الحالية
      const isAdmin = userData.isAdmin === true;
      console.log(`حالة المشرف الحالية: ${isAdmin ? 'مشرف' : 'مستخدم عادي'}`);
      
      if (isAdmin) {
        console.log('المستخدم مشرف بالفعل');
      } else {
        // تعيين المستخدم كمشرف
        console.log('تعيين المستخدم كمشرف...');
        await userRef.update({ isAdmin: true });
        
        // التحقق من نجاح العملية
        const updatedSnapshot = await userRef.once('value');
        const updatedUserData = updatedSnapshot.val();
        const updatedIsAdmin = updatedUserData.isAdmin === true;
        
        if (updatedIsAdmin) {
          console.log('تم تعيين المستخدم كمشرف بنجاح');
        } else {
          console.log('فشل في تعيين المستخدم كمشرف');
        }
      }
    } else {
      console.log(`لم يتم العثور على المستخدم ${uid}`);
    }
  } catch (error) {
    console.error(`خطأ: ${error.message}`);
  } finally {
    // إنهاء التطبيق
    process.exit(0);
  }
}

// تنفيذ العملية
makeAdmin();
