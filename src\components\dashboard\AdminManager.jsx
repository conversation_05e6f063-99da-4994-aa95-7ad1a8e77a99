import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  Input,
  InputGroup,
  List,
  FlexboxGrid,
  IconButton,
  Message,
  toaster,
  Loader
} from 'rsuite';
import { ref, get, set, onValue, off } from 'firebase/database';
import { database, auth } from '../../misc/firebase.config';
import CloseIcon from '@rsuite/icons/Close';
import SearchIcon from '@rsuite/icons/Search';
import ProfileAvatar from '../ProfileAvatar';

const AdminManager = ({ open, onClose }) => {
  const [adminUid, setAdminUid] = useState('');
  const [admins, setAdmins] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  // التحقق من أن المستخدم الحالي هو المالك
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );

  // جلب قائمة المشرفين
  useEffect(() => {
    if (!open) return;

    const fetchAdmins = async () => {
      setIsLoading(true);
      try {
        // التحقق من أن المستخدم الحالي هو المالك
        if (!isOwner) {
          setAdmins([]);
          setIsLoading(false);
          return;
        }

        // استخدام onValue للاستماع للتغييرات في قائمة المشرفين
        const adminsRef = ref(database, 'admins');

        // إنشاء مستمع للتغييرات
        const unsubscribe = onValue(adminsRef, (snapshot) => {
          if (snapshot.exists()) {
            const adminsData = snapshot.val();
            const adminsList = [];

            // جلب بيانات كل مشرف
            const fetchAdminDetails = async () => {
              for (const [uid, isAdmin] of Object.entries(adminsData)) {
                if (isAdmin) {
                  try {
                    // جلب بيانات المستخدم
                    const userRef = ref(database, `users/${uid}`);
                    const userSnapshot = await get(userRef);

                    if (userSnapshot.exists()) {
                      const userData = userSnapshot.val();
                      adminsList.push({
                        uid,
                        name: userData.name || 'مستخدم غير معروف',
                        avatar: userData.avatar || '',
                        email: userData.email || '',
                        createdAt: userData.createdAt || null,
                      });
                    } else {
                      // إضافة المستخدم بدون بيانات إضافية
                      adminsList.push({
                        uid,
                        name: 'مستخدم غير معروف',
                        avatar: '',
                        email: '',
                      });
                    }
                  } catch (error) {
                    console.error(`Error fetching details for admin ${uid}:`, error);
                  }
                }
              }

              // إضافة المالك دائمًا إلى القائمة
              const ownerUid = 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1';
              if (!adminsList.some(admin => admin.uid === ownerUid)) {
                try {
                  const ownerRef = ref(database, `users/${ownerUid}`);
                  const ownerSnapshot = await get(ownerRef);

                  if (ownerSnapshot.exists()) {
                    const ownerData = ownerSnapshot.val();
                    adminsList.push({
                      uid: ownerUid,
                      name: ownerData.name || 'المالك',
                      avatar: ownerData.avatar || '',
                      email: ownerData.email || '<EMAIL>',
                      isOwner: true,
                    });
                  } else {
                    adminsList.push({
                      uid: ownerUid,
                      name: 'المالك',
                      avatar: '',
                      email: '<EMAIL>',
                      isOwner: true,
                    });
                  }
                } catch (error) {
                  console.error('Error fetching owner details:', error);
                  adminsList.push({
                    uid: ownerUid,
                    name: 'المالك',
                    avatar: '',
                    email: '<EMAIL>',
                    isOwner: true,
                  });
                }
              }

              setAdmins(adminsList);
              setIsLoading(false);
            };

            fetchAdminDetails();
          } else {
            // إذا لم تكن هناك قائمة مشرفين، أضف المالك فقط
            const fetchOwner = async () => {
              const ownerUid = 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1';
              try {
                const ownerRef = ref(database, `users/${ownerUid}`);
                const ownerSnapshot = await get(ownerRef);

                if (ownerSnapshot.exists()) {
                  const ownerData = ownerSnapshot.val();
                  setAdmins([{
                    uid: ownerUid,
                    name: ownerData.name || 'المالك',
                    avatar: ownerData.avatar || '',
                    email: ownerData.email || '<EMAIL>',
                    isOwner: true,
                  }]);
                } else {
                  setAdmins([{
                    uid: ownerUid,
                    name: 'المالك',
                    avatar: '',
                    email: '<EMAIL>',
                    isOwner: true,
                  }]);
                }
              } catch (error) {
                console.error('Error fetching owner details:', error);
                setAdmins([{
                  uid: ownerUid,
                  name: 'المالك',
                  avatar: '',
                  email: '<EMAIL>',
                  isOwner: true,
                }]);
              } finally {
                setIsLoading(false);
              }
            };

            fetchOwner();
          }
        }, (error) => {
          console.error('Error fetching admins:', error);
          toaster.push(
            <Message type="error" closable duration={4000}>
              خطأ في جلب قائمة المشرفين: {error.message || 'خطأ غير معروف'}
            </Message>
          );
          setIsLoading(false);
        });

        // إلغاء الاشتراك عند إغلاق النافذة
        return () => {
          off(adminsRef);
        };
      } catch (error) {
        console.error('Error setting up admin listener:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في إعداد مستمع المشرفين: {error.message || 'خطأ غير معروف'}
          </Message>
        );
        setIsLoading(false);
      }
    };

    fetchAdmins();
  }, [open, isOwner]);

  // البحث عن مستخدم بواسطة UID
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toaster.push(
        <Message type="warning" closable duration={4000}>
          الرجاء إدخال معرف المستخدم (UID) للبحث
        </Message>
      );
      return;
    }

    setIsSearching(true);
    setSearchResults([]);

    try {
      const userRef = ref(database, `users/${searchQuery.trim()}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        const userData = snapshot.val();
        setSearchResults([{
          uid: searchQuery.trim(),
          name: userData.name || 'مستخدم غير معروف',
          avatar: userData.avatar || '',
          email: userData.email || '',
          isAdmin: userData.isAdmin || false,
        }]);
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على مستخدم بهذا المعرف
          </Message>
        );
      }
    } catch (error) {
      console.error('Error searching for user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في البحث عن المستخدم: {error.message}
        </Message>
      );
    } finally {
      setIsSearching(false);
    }
  };

  // تعيين مستخدم كمشرف في جميع الغرف
  const makeUserRoomAdmin = async (uid) => {
    try {
      // التحقق من أن المستخدم الحالي هو المالك
      if (!isOwner) {
        console.error('Only the owner can make users room admins');
        return 0;
      }

      // الحصول على جميع الغرف
      const roomsRef = ref(database, 'rooms');
      const roomsSnapshot = await get(roomsRef);

      if (roomsSnapshot.exists()) {
        const rooms = roomsSnapshot.val();
        let successCount = 0;
        let promises = [];

        // تعيين المستخدم كمشرف في كل غرفة
        for (const [roomId, roomData] of Object.entries(rooms)) {
          try {
            // إضافة وعد لتحديث حالة المشرف في الغرفة
            const promise = set(ref(database, `rooms/${roomId}/admins/${uid}`), true)
              .then(() => {
                console.log(`User ${uid} made admin in room ${roomId}`);
                successCount++;
              })
              .catch(roomError => {
                console.error(`Error making user admin in room ${roomId}:`, roomError);
              });

            promises.push(promise);
          } catch (error) {
            console.error(`Error creating promise for room ${roomId}:`, error);
          }
        }

        // انتظار جميع الوعود
        await Promise.allSettled(promises);

        console.log(`User ${uid} made admin in ${successCount} rooms out of ${Object.keys(rooms).length}`);
        return successCount;
      } else {
        console.log('No rooms found');
        return 0;
      }
    } catch (error) {
      console.error('Error making user room admin:', error);
      return 0;
    }
  };

  // تعيين مستخدم كمشرف
  const handleMakeAdmin = async (uid) => {
    if (!uid || !uid.trim()) {
      toaster.push(
        <Message type="warning" closable duration={4000}>
          الرجاء إدخال معرف المستخدم (UID)
        </Message>
      );
      return;
    }

    if (!isOwner) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          فقط مالك الموقع يمكنه تعيين مشرفين
        </Message>
      );
      return;
    }

    setIsProcessing(true);

    try {
      // التحقق من وجود المستخدم
      const userRef = ref(database, `users/${uid.trim()}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        const userData = snapshot.val();

        // التحقق مما إذا كان المستخدم هو المالك
        if (uid.trim() === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1') {
          toaster.push(
            <Message type="info" closable duration={4000}>
              هذا المستخدم هو مالك الموقع ومشرف بالفعل
            </Message>
          );
          setIsProcessing(false);
          return;
        }

        // تعيين المستخدم كمشرف في قائمة المشرفين
        await set(ref(database, `admins/${uid.trim()}`), true);

        // تعيين المستخدم كمشرف في جدول المستخدمين
        await set(ref(database, `users/${uid.trim()}/isAdmin`), true);

        // تعيين المستخدم كمشرف في جميع الغرف
        const roomCount = await makeUserRoomAdmin(uid.trim());

        toaster.push(
          <Message type="success" closable duration={4000}>
            تم تعيين المستخدم {userData.name || uid.trim()} كمشرف بنجاح وتم تعيينه كمشرف في {roomCount} غرفة
          </Message>
        );

        // مسح حقل الإدخال
        setSearchQuery('');
        setAdminUid('');
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على مستخدم بهذا المعرف: {uid.trim()}
          </Message>
        );
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في تعيين المستخدم كمشرف: {error.message || 'خطأ غير معروف'}
        </Message>
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // إزالة صلاحية المشرف من مستخدم
  const handleRemoveAdmin = async (uid) => {
    if (!isOwner) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          فقط مالك الموقع يمكنه إزالة المشرفين
        </Message>
      );
      return;
    }

    // لا يمكن إزالة صلاحية المشرف من المالك
    if (uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1') {
      toaster.push(
        <Message type="error" closable duration={4000}>
          لا يمكن إزالة صلاحية المشرف من مالك الموقع
        </Message>
      );
      return;
    }

    setIsProcessing(true);

    try {
      // إزالة المستخدم من قائمة المشرفين
      await set(ref(database, `admins/${uid}`), null);

      // إزالة صلاحية المشرف من جدول المستخدمين
      await set(ref(database, `users/${uid}/isAdmin`), false);

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم إزالة صلاحية المشرف بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error removing admin:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إزالة صلاحية المشرف: {error.message}
        </Message>
      );
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} size="md" className="dark-modal">
      <Modal.Header className="dark-modal-header">
        <Modal.Title className="dark-modal-title">إدارة المشرفين</Modal.Title>
      </Modal.Header>
      <Modal.Body className="dark-modal-body">
        {!isOwner ? (
          <Message type="error" showIcon>
            فقط مالك الموقع يمكنه الوصول إلى هذه الصفحة
          </Message>
        ) : (
          <>
            <div className="mb-4">
              <h5 className="mb-3 admin-section-title">إضافة مشرف جديد</h5>
              <div className="admin-panel-box">
                <p className="mb-3">أدخل معرف المستخدم (UID) لتعيينه كمشرف:</p>
                <InputGroup className="admin-input-group">
                  <Input
                    placeholder="أدخل معرف المستخدم (UID)"
                    value={adminUid}
                    onChange={setAdminUid}
                    dir="ltr"
                    className="admin-input"
                  />
                  <InputGroup.Button
                    onClick={() => handleMakeAdmin(adminUid.trim())}
                    disabled={isProcessing || !adminUid.trim()}
                    className="admin-add-btn"
                  >
                    {isProcessing ? <Loader size="xs" /> : 'تعيين كمشرف'}
                  </InputGroup.Button>
                </InputGroup>
              </div>
            </div>

            {searchResults.length > 0 && (
              <div className="mb-3">
                <h5 className="mb-2">نتائج البحث</h5>
                <div style={{ backgroundColor: '#2d2d2d', padding: '15px', borderRadius: '5px' }}>
                  <List hover>
                    {searchResults.map(user => (
                      <List.Item key={user.uid}>
                        <FlexboxGrid align="middle">
                          <FlexboxGrid.Item colspan={4}>
                            <ProfileAvatar
                              src={user.avatar}
                              name={user.name}
                              uid={user.uid}
                              showUid={false}
                              size="sm"
                            />
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={12}>
                            <div>
                              <strong className={user.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ? 'admin-owner-name' : user.isAdmin ? 'admin-name' : ''}>
                                {user.name}
                                {user.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && ' (المالك)'}
                                {user.isAdmin && user.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && <span className="admin-badge ml-1">مشرف</span>}
                              </strong>
                              <div style={{ fontSize: '12px', color: '#999', direction: 'ltr' }}>
                                {user.uid}
                              </div>
                            </div>
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={8} style={{ textAlign: 'right' }}>
                            {user.isAdmin ? (
                              <Button appearance="ghost" color="red" size="sm" onClick={() => handleRemoveAdmin(user.uid)} disabled={isProcessing}>
                                إزالة المشرف
                              </Button>
                            ) : (
                              <Button appearance="ghost" color="green" size="sm" onClick={() => handleMakeAdmin(user.uid)} disabled={isProcessing}>
                                تعيين كمشرف
                              </Button>
                            )}
                          </FlexboxGrid.Item>
                        </FlexboxGrid>
                      </List.Item>
                    ))}
                  </List>
                </div>
              </div>
            )}

            <div className="mt-4">
              <h5 className="mb-3 admin-section-title">المشرفين الحاليين</h5>
              <div className="admin-panel-box">
                {isLoading ? (
                  <div className="text-center p-4">
                    <Loader size="md" content="جاري تحميل قائمة المشرفين..." />
                  </div>
                ) : admins.length === 0 ? (
                  <Message type="info" showIcon>
                    لا يوجد مشرفين حاليًا
                  </Message>
                ) : (
                  <List hover className="admin-list">
                    {admins.map(admin => (
                      <List.Item key={admin.uid} className="admin-list-item">
                        <FlexboxGrid align="middle">
                          <FlexboxGrid.Item colspan={4}>
                            <ProfileAvatar
                              src={admin.avatar}
                              name={admin.name}
                              uid={admin.uid}
                              showUid={false}
                              size="sm"
                            />
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={12}>
                            <div>
                              <strong className={admin.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ? 'admin-owner-name' : 'admin-name'}>
                                {admin.name}
                                {admin.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && ' (المالك)'}
                                {admin.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && <span className="admin-badge ml-1">مشرف</span>}
                              </strong>
                              <div className="admin-uid">
                                {admin.uid}
                              </div>
                              {admin.email && (
                                <div className="admin-email">
                                  {admin.email}
                                </div>
                              )}
                            </div>
                          </FlexboxGrid.Item>
                          <FlexboxGrid.Item colspan={8} style={{ textAlign: 'right' }}>
                            {admin.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && (
                              <Button
                                appearance="ghost"
                                className="admin-remove-btn"
                                size="sm"
                                onClick={() => handleRemoveAdmin(admin.uid)}
                                disabled={isProcessing}
                              >
                                إزالة المشرف
                              </Button>
                            )}
                          </FlexboxGrid.Item>
                        </FlexboxGrid>
                      </List.Item>
                    ))}
                  </List>
                )}
              </div>
            </div>
          </>
        )}
      </Modal.Body>
      <Modal.Footer className="dark-modal-footer">
        <Button block onClick={onClose} appearance="subtle" className="dark-close-btn">
          إغلاق
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AdminManager;
