import React, { useState, useEffect } from 'react';
import { Button, Avatar, Modal, Message, toaster } from 'rsuite';
import { database, auth } from '../../../misc/firebase.config';
import { ref, get, remove } from 'firebase/database';
import ProfileInfoBtnModal from './ProfileInfoBtnModal';
import TimeAgo from 'timeago-react';
import { useHistory } from 'react-router';
import { useParams } from 'react-router-dom';

const SpecialMessageItem = ({ message }) => {
  const { specialData, author, createdAt } = message;
  const { imageUrl, description, targetUid } = specialData;
  const [targetProfile, setTargetProfile] = useState(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [messageId, setMessageId] = useState(null);
  const history = useHistory();
  const { chatId } = useParams();

  // التحقق مما إذا كان المستخدم هو المالك
  useEffect(() => {
    const checkOwnerStatus = () => {
      if (auth.currentUser) {
        const currentUserId = auth.currentUser.uid;
        const isOwnerUser = currentUserId === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
                           auth.currentUser.email === '<EMAIL>';
        setIsOwner(isOwnerUser);
      }
    };

    // البحث عن معرف الرسالة
    const findMessageId = async () => {
      try {
        const messagesRef = ref(database, `messages`);
        const snapshot = await get(messagesRef);

        if (snapshot.exists()) {
          const messagesData = snapshot.val();

          // البحث عن الرسالة بناءً على البيانات
          for (const msgId in messagesData) {
            const msg = messagesData[msgId];
            if (
              msg.isSpecialMessage &&
              msg.specialData &&
              msg.specialData.description === description &&
              msg.specialData.targetUid === targetUid &&
              msg.createdAt === createdAt
            ) {
              setMessageId(msgId);
              break;
            }
          }
        }
      } catch (error) {
        console.error('Error finding message ID:', error);
      }
    };

    checkOwnerStatus();
    findMessageId();
  }, [description, targetUid, createdAt]);

  // جلب معلومات المستخدم المستهدف وفتح ملفه الشخصي في نافذة منبثقة
  const fetchTargetProfile = async () => {
    try {
      const userRef = ref(database, `/users/${targetUid}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        const userData = snapshot.val();

        // تعيين بيانات المستخدم وفتح النافذة المنبثقة
        setTargetProfile({
          ...userData,
          uid: targetUid
        });
        setIsProfileModalOpen(true);
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على معلومات المستخدم
          </Message>
        );
      }
    } catch (error) {
      console.error('Error fetching target profile:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء محاولة فتح ملف المستخدم
        </Message>
      );
    }
  };

  // فتح نافذة الصورة المكبرة
  const openImageModal = () => {
    setIsImageModalOpen(true);
  };

  // حذف الرسالة الخاصة (للمالك فقط)
  const handleDeleteMessage = async () => {
    if (!isOwner || !messageId) return;

    try {
      // حذف الرسالة من قاعدة البيانات
      await remove(ref(database, `messages/${messageId}`));

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حذف الإعلان بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error deleting special message:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء حذف الإعلان: {error.message}
        </Message>
      );
    }
  };

  return (
    <div className="special-message-container">
      <div className="special-message-header">
        {/* إخفاء معلومات المالك في الوضع الخاص */}
        {author.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && (
          <div className="special-message-author">
            <Avatar src={author.avatar} alt={author.name} circle size="sm" />
            <span className="author-name">{author.name}</span>
            <TimeAgo
              datetime={createdAt}
              className="message-time"
              locale="ar"
            />
          </div>
        )}

        {/* زر حذف الإعلان للمالك فقط */}
        {isOwner && messageId && (
          <Button
            appearance="subtle"
            color="red"
            size="xs"
            className="special-message-delete-btn"
            onClick={handleDeleteMessage}
          >
            <i className="fa fa-trash"></i> حذف الإعلان
          </Button>
        )}
      </div>

      <div className="special-message-content">
        <div className="special-message-image" onClick={openImageModal}>
          <img src={imageUrl} alt={description} />
        </div>

        <div className="special-message-description">
          {description}
        </div>

        <Button
          appearance="primary"
          color="green"
          className="special-message-target-btn"
          onClick={fetchTargetProfile}
        >
          اضغط للتواصل
        </Button>
      </div>

      {/* نافذة عرض الصورة المكبرة */}
      <Modal
        open={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        className="dark-modal image-preview-modal"
      >
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">معاينة الصورة</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <div className="image-preview-container">
            <img src={imageUrl} alt={description} className="full-size-image" />
          </div>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button onClick={() => setIsImageModalOpen(false)} appearance="subtle">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة عرض الملف الشخصي للمستخدم المستهدف */}
      {targetProfile && (
        <ProfileInfoBtnModal
          profile={targetProfile}
          open={isProfileModalOpen}
          close={() => setIsProfileModalOpen(false)}
        />
      )}
    </div>
  );
};

export default SpecialMessageItem;
