<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>إدارة المشرفين في Realtime Database</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #1DA1F2;
            margin-bottom: 20px;
        }
        .login-form {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #3a3b3c;
            background-color: #3a3b3c;
            color: #e4e6eb;
            box-sizing: border-box;
        }
        .admin-panel {
            display: none;
        }
        .section {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1DA1F2;
        }
        .user-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .user-item {
            background-color: #3a3b3c;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-email {
            font-size: 14px;
            color: #999;
            margin-bottom: 5px;
        }
        .user-uid {
            font-family: monospace;
            font-size: 12px;
            color: #999;
        }
        .user-actions {
            display: flex;
            gap: 10px;
        }
        .room-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .room-item {
            background-color: #3a3b3c;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .room-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .room-id {
            font-family: monospace;
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }
        .room-admins {
            font-size: 14px;
            margin-top: 10px;
        }
        .admin-badge {
            display: inline-block;
            background-color: #1DA1F2;
            color: white;
            padding: 2px 5px;
            border-radius: 3px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 12px;
        }
        button {
            background-color: #1DA1F2;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        .btn-success {
            background-color: #4CAF50;
        }
        .btn-success:hover {
            background-color: #388E3C;
        }
        .log {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #1DA1F2;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            background-color: #2d2d2d;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom: 2px solid #1DA1F2;
            color: #1DA1F2;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إدارة المشرفين في Realtime Database</h1>
        
        <div class="login-form" id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password">
            </div>
            <button id="loginBtn">تسجيل الدخول</button>
        </div>
        
        <div class="admin-panel" id="adminPanel">
            <div class="tabs">
                <div class="tab active" data-tab="users">المستخدمين</div>
                <div class="tab" data-tab="rooms">الغرف</div>
                <div class="tab" data-tab="add-admin">إضافة مشرف</div>
            </div>
            
            <div class="tab-content active" id="usersTab">
                <div class="section">
                    <div class="section-title">قائمة المستخدمين</div>
                    <div class="user-list" id="userList">
                        جاري تحميل المستخدمين...
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="roomsTab">
                <div class="section">
                    <div class="section-title">قائمة الغرف</div>
                    <div class="room-list" id="roomList">
                        جاري تحميل الغرف...
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="addAdminTab">
                <div class="section">
                    <div class="section-title">إضافة مشرف جديد</div>
                    <div class="form-group">
                        <label for="adminUid">معرف المستخدم (UID):</label>
                        <input type="text" id="adminUid" placeholder="أدخل معرف المستخدم">
                    </div>
                    <button id="addAdminBtn">تعيين كمشرف</button>
                </div>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const loginForm = document.getElementById('loginForm');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const adminPanel = document.getElementById('adminPanel');
        const userList = document.getElementById('userList');
        const roomList = document.getElementById('roomList');
        const adminUidInput = document.getElementById('adminUid');
        const addAdminBtn = document.getElementById('addAdminBtn');
        const logElement = document.getElementById('log');
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // إضافة رسالة إلى السجل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // تسجيل الدخول
        loginBtn.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            
            if (!email || !password) {
                log('الرجاء إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري تسجيل الدخول...';
            
            try {
                // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
                await firebase.auth().signInWithEmailAndPassword(email, password);
                log(`تم تسجيل الدخول بنجاح كـ ${email}`, 'success');
                
                // إخفاء نموذج تسجيل الدخول وإظهار لوحة الإدارة
                loginForm.style.display = 'none';
                adminPanel.style.display = 'block';
                
                // تحميل البيانات
                loadUsers();
                loadRooms();
            } catch (error) {
                log(`خطأ في تسجيل الدخول: ${error.message}`, 'error');
                loginBtn.disabled = false;
                loginBtn.textContent = 'تسجيل الدخول';
            }
        });
        
        // تحميل المستخدمين
        async function loadUsers() {
            try {
                log('جاري تحميل المستخدمين...');
                
                // الحصول على جميع المستخدمين
                const usersRef = firebase.database().ref('users');
                const snapshot = await usersRef.once('value');
                
                if (snapshot.exists()) {
                    const users = snapshot.val();
                    const usersArray = [];
                    
                    // تحويل المستخدمين إلى مصفوفة
                    for (const [uid, userData] of Object.entries(users)) {
                        usersArray.push({
                            uid,
                            name: userData.name || 'مستخدم غير معروف',
                            email: userData.email || '',
                            isAdmin: userData.isAdmin === true
                        });
                    }
                    
                    // عرض المستخدمين
                    if (usersArray.length > 0) {
                        userList.innerHTML = '';
                        
                        usersArray.forEach(user => {
                            const userElement = document.createElement('div');
                            userElement.className = 'user-item';
                            userElement.innerHTML = `
                                <div class="user-info">
                                    <div class="user-name">${user.name} ${user.isAdmin ? '<span style="color: #4CAF50;">(مشرف)</span>' : ''}</div>
                                    <div class="user-email">${user.email}</div>
                                    <div class="user-uid">${user.uid}</div>
                                </div>
                                <div class="user-actions">
                                    ${user.isAdmin ? 
                                        `<button class="btn-danger remove-admin-btn" data-uid="${user.uid}">إلغاء المشرف</button>` : 
                                        `<button class="btn-success make-admin-btn" data-uid="${user.uid}">تعيين كمشرف</button>`
                                    }
                                    <button class="make-room-admin-btn" data-uid="${user.uid}">تعيين كمشرف في الغرف</button>
                                </div>
                            `;
                            userList.appendChild(userElement);
                        });
                        
                        // إضافة مستمعي الأحداث للأزرار
                        document.querySelectorAll('.make-admin-btn').forEach(btn => {
                            btn.addEventListener('click', () => {
                                makeUserAdmin(btn.dataset.uid);
                            });
                        });
                        
                        document.querySelectorAll('.remove-admin-btn').forEach(btn => {
                            btn.addEventListener('click', () => {
                                removeUserAdmin(btn.dataset.uid);
                            });
                        });
                        
                        document.querySelectorAll('.make-room-admin-btn').forEach(btn => {
                            btn.addEventListener('click', () => {
                                makeUserRoomAdmin(btn.dataset.uid);
                            });
                        });
                    } else {
                        userList.textContent = 'لا يوجد مستخدمين';
                    }
                    
                    log(`تم تحميل ${usersArray.length} مستخدم`, 'success');
                } else {
                    userList.textContent = 'لا يوجد مستخدمين';
                    log('لا يوجد مستخدمين', 'info');
                }
            } catch (error) {
                console.error('Error loading users:', error);
                log(`خطأ في تحميل المستخدمين: ${error.message}`, 'error');
                userList.textContent = 'خطأ في تحميل المستخدمين';
            }
        }
        
        // تحميل الغرف
        async function loadRooms() {
            try {
                log('جاري تحميل الغرف...');
                
                // الحصول على جميع الغرف
                const roomsRef = firebase.database().ref('rooms');
                const snapshot = await roomsRef.once('value');
                
                if (snapshot.exists()) {
                    const rooms = snapshot.val();
                    const roomsArray = [];
                    
                    // تحويل الغرف إلى مصفوفة
                    for (const [roomId, roomData] of Object.entries(rooms)) {
                        roomsArray.push({
                            id: roomId,
                            name: roomData.name || 'غرفة بدون اسم',
                            admins: roomData.admins || {}
                        });
                    }
                    
                    // عرض الغرف
                    if (roomsArray.length > 0) {
                        roomList.innerHTML = '';
                        
                        roomsArray.forEach(room => {
                            const roomElement = document.createElement('div');
                            roomElement.className = 'room-item';
                            
                            // الحصول على قائمة المشرفين
                            const adminUids = Object.keys(room.admins || {});
                            
                            roomElement.innerHTML = `
                                <div class="room-name">${room.name}</div>
                                <div class="room-id">${room.id}</div>
                                <div class="room-admins">
                                    <strong>المشرفين (${adminUids.length}):</strong>
                                    <div id="room-${room.id}-admins">
                                        ${adminUids.map(uid => `<span class="admin-badge">${uid}</span>`).join('')}
                                    </div>
                                </div>
                            `;
                            roomList.appendChild(roomElement);
                        });
                        
                        log(`تم تحميل ${roomsArray.length} غرفة`, 'success');
                    } else {
                        roomList.textContent = 'لا توجد غرف';
                    }
                } else {
                    roomList.textContent = 'لا توجد غرف';
                    log('لا توجد غرف', 'info');
                }
            } catch (error) {
                console.error('Error loading rooms:', error);
                log(`خطأ في تحميل الغرف: ${error.message}`, 'error');
                roomList.textContent = 'خطأ في تحميل الغرف';
            }
        }
        
        // تعيين مستخدم كمشرف
        async function makeUserAdmin(uid) {
            try {
                log(`جاري تعيين المستخدم ${uid} كمشرف...`);
                
                // التحقق من وجود المستخدم
                const userRef = firebase.database().ref(`users/${uid}`);
                const snapshot = await userRef.once('value');
                
                if (snapshot.exists()) {
                    // تعيين المستخدم كمشرف
                    await userRef.child('isAdmin').set(true);
                    
                    log(`تم تعيين المستخدم ${uid} كمشرف بنجاح`, 'success');
                    
                    // تحديث قائمة المستخدمين
                    loadUsers();
                } else {
                    log(`لم يتم العثور على المستخدم ${uid}`, 'error');
                }
            } catch (error) {
                console.error('Error making user admin:', error);
                log(`خطأ في تعيين المستخدم كمشرف: ${error.message}`, 'error');
            }
        }
        
        // إلغاء مشرف من مستخدم
        async function removeUserAdmin(uid) {
            try {
                log(`جاري إلغاء المشرف من المستخدم ${uid}...`);
                
                // التحقق من وجود المستخدم
                const userRef = firebase.database().ref(`users/${uid}`);
                const snapshot = await userRef.once('value');
                
                if (snapshot.exists()) {
                    // إلغاء المشرف من المستخدم
                    await userRef.child('isAdmin').remove();
                    
                    log(`تم إلغاء المشرف من المستخدم ${uid} بنجاح`, 'success');
                    
                    // تحديث قائمة المستخدمين
                    loadUsers();
                } else {
                    log(`لم يتم العثور على المستخدم ${uid}`, 'error');
                }
            } catch (error) {
                console.error('Error removing user admin:', error);
                log(`خطأ في إلغاء المشرف من المستخدم: ${error.message}`, 'error');
            }
        }
        
        // تعيين مستخدم كمشرف في جميع الغرف
        async function makeUserRoomAdmin(uid) {
            try {
                log(`جاري تعيين المستخدم ${uid} كمشرف في جميع الغرف...`);
                
                // الحصول على جميع الغرف
                const roomsRef = firebase.database().ref('rooms');
                const snapshot = await roomsRef.once('value');
                
                if (snapshot.exists()) {
                    const rooms = snapshot.val();
                    let successCount = 0;
                    let promises = [];
                    
                    // تعيين المستخدم كمشرف في كل غرفة
                    for (const [roomId, roomData] of Object.entries(rooms)) {
                        try {
                            // إضافة وعد لتحديث حالة المشرف في الغرفة
                            const promise = firebase.database().ref(`rooms/${roomId}/admins/${uid}`).set(true)
                                .then(() => {
                                    successCount++;
                                })
                                .catch(roomError => {
                                    console.error(`Error making user admin in room ${roomId}:`, roomError);
                                });
                            
                            promises.push(promise);
                        } catch (error) {
                            console.error(`Error creating promise for room ${roomId}:`, error);
                        }
                    }
                    
                    // انتظار جميع الوعود
                    await Promise.allSettled(promises);
                    
                    log(`تم تعيين المستخدم ${uid} كمشرف في ${successCount} غرفة من أصل ${Object.keys(rooms).length} غرفة`, 'success');
                    
                    // تحديث قائمة الغرف
                    loadRooms();
                } else {
                    log('لا توجد غرف لتعيين المستخدم كمشرف فيها', 'info');
                }
            } catch (error) {
                console.error('Error making user room admin:', error);
                log(`خطأ في تعيين المستخدم كمشرف في الغرف: ${error.message}`, 'error');
            }
        }
        
        // إضافة مشرف جديد
        addAdminBtn.addEventListener('click', async () => {
            const uid = adminUidInput.value.trim();
            
            if (!uid) {
                log('الرجاء إدخال معرف المستخدم (UID)', 'error');
                return;
            }
            
            addAdminBtn.disabled = true;
            addAdminBtn.textContent = 'جاري التعيين...';
            
            try {
                // تعيين المستخدم كمشرف
                await makeUserAdmin(uid);
                
                // تعيين المستخدم كمشرف في جميع الغرف
                await makeUserRoomAdmin(uid);
                
                // مسح حقل الإدخال
                adminUidInput.value = '';
            } catch (error) {
                log(`خطأ في تعيين المستخدم كمشرف: ${error.message}`, 'error');
            } finally {
                addAdminBtn.disabled = false;
                addAdminBtn.textContent = 'تعيين كمشرف';
            }
        });
        
        // التبديل بين علامات التبويب
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // إزالة الفئة النشطة من جميع علامات التبويب
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // إضافة الفئة النشطة إلى علامة التبويب المحددة
                tab.classList.add('active');
                document.getElementById(`${tab.dataset.tab}Tab`).classList.add('active');
            });
        });
        
        // التحقق من حالة المصادقة عند تحميل الصفحة
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                // المستخدم مسجل الدخول بالفعل
                log(`تم تسجيل الدخول تلقائيًا كـ ${user.email}`, 'success');
                
                // إخفاء نموذج تسجيل الدخول وإظهار لوحة الإدارة
                loginForm.style.display = 'none';
                adminPanel.style.display = 'block';
                
                // تحميل البيانات
                loadUsers();
                loadRooms();
            }
        });
    </script>
</body>
</html>
