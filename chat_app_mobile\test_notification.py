#!/usr/bin/env python3
"""
Script to test Firebase Cloud Messaging (FCM) notifications.
This script sends a test notification to a device using FCM.

Usage:
    python test_notification.py --token DEVICE_TOKEN --key SERVER_KEY

Requirements:
    - Python 3.6+
    - requests library (pip install requests)
"""

import argparse
import json
import requests

FCM_URL = "https://fcm.googleapis.com/fcm/send"

def send_notification(device_token, server_key, title="Test Notification", body="This is a test notification", data=None):
    """
    Send a notification to a device using FCM.
    
    Args:
        device_token (str): The FCM token of the device to send the notification to.
        server_key (str): The server key from Firebase project settings.
        title (str, optional): The title of the notification. Defaults to "Test Notification".
        body (str, optional): The body of the notification. Defaults to "This is a test notification".
        data (dict, optional): Additional data to send with the notification. Defaults to None.
    
    Returns:
        dict: The response from FCM.
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"key={server_key}"
    }
    
    payload = {
        "to": device_token,
        "notification": {
            "title": title,
            "body": body,
            "sound": "default"
        }
    }
    
    if data:
        payload["data"] = data
    
    response = requests.post(FCM_URL, headers=headers, data=json.dumps(payload))
    return response.json()

def main():
    parser = argparse.ArgumentParser(description="Send a test notification using FCM")
    parser.add_argument("--token", required=True, help="Device token to send the notification to")
    parser.add_argument("--key", required=True, help="Server key from Firebase project settings")
    parser.add_argument("--title", default="Test Notification", help="Notification title")
    parser.add_argument("--body", default="This is a test notification", help="Notification body")
    parser.add_argument("--data", help="Additional data in JSON format")
    
    args = parser.parse_args()
    
    data = None
    if args.data:
        try:
            data = json.loads(args.data)
        except json.JSONDecodeError:
            print("Error: Invalid JSON format for data")
            return
    
    print(f"Sending notification to device: {args.token}")
    response = send_notification(args.token, args.key, args.title, args.body, data)
    
    print("\nResponse from FCM:")
    print(json.dumps(response, indent=2))
    
    if "success" in response and response["success"] == 1:
        print("\nNotification sent successfully!")
    else:
        print("\nFailed to send notification. Check the response for details.")

if __name__ == "__main__":
    main()
