import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Modal, Badge, Message, toaster, Dropdown, SelectPicker } from 'rsuite';
import { useModalState } from '../../../misc/custom-hooks';
import ProfileAvatar from '../../ProfileAvatar';
import { useCurrentRoom } from '../../../context/current-room.context';
import { useProfile } from '../../../context/profile.context';
import { database, auth } from '../../../misc/firebase.config';
import { ref, set, get, push, serverTimestamp, remove } from 'firebase/database';
import { useHistory, useParams } from 'react-router-dom';
import SendIcon from '@rsuite/icons/Send';

const ProfileInfoBtnModal = ({ profile, children, roomId: propRoomId, ...btnProps }) => {
  const { isOpen, close, open } = useModalState();
  const isAdmin = useCurrentRoom(v => v.isAdmin);
  const admins = useCurrentRoom(v => v.admins);
  const isDirectMessage = useCurrentRoom(v => v.isDirectMessage);
  const { profile: currentUserProfile } = useProfile();
  const [isBanning, setIsBanning] = useState(false);
  const [isMuting, setIsMuting] = useState(false);
  const [selectedMuteDuration, setSelectedMuteDuration] = useState('5');
  const [isMuted, setIsMuted] = useState(false);
  const [muteEndTime, setMuteEndTime] = useState(null);
  const [muteTimeRemaining, setMuteTimeRemaining] = useState('');
  const [isUserBlocked, setIsUserBlocked] = useState(false);
  const [isBlockingUser, setIsBlockingUser] = useState(false);
  const [blockedByCurrentUser, setBlockedByCurrentUser] = useState(false);
  const [blockedByOtherUser, setBlockedByOtherUser] = useState(false);
  const contextRoomId = useCurrentRoom(v => v.id);
  const { chatId } = useParams();
  const history = useHistory();

  // استخدام معرف الغرفة المرسل كخاصية إذا كان متوفراً، وإلا استخدام معرف الغرفة من السياق
  const roomId = propRoomId || contextRoomId;

  const { name, avatar, createdAt, uid, email } = profile;
  const isProfileAdmin = admins && admins[uid] === true;
  // التحقق مما إذا كان المستخدم الحالي مشرف
  const isCurrentUserAdmin = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>' ||
    (currentUserProfile && currentUserProfile.isAdmin)
  );

  // خيارات مدة الكتم
  const muteDurationOptions = [
    { label: '5 دقائق', value: '5' },
    { label: '10 دقائق', value: '10' },
    { label: '1 ساعة', value: '60' },
    { label: '24 ساعة', value: '1440' },
  ];

  const shortName = profile.name.split(' ')[0];

  const memberSince = new Date(createdAt).toLocaleDateString();

  // التحقق من حالة كتم الصوت وحالة الحظر عند فتح النافذة
  useEffect(() => {
    if (isOpen && uid) {
      const checkMuteStatus = async () => {
        try {
          // التحقق من وجود معرف الغرفة
          if (!roomId) {
            console.log('No room ID available for mute check');
            setIsMuted(false);
            setMuteEndTime(null);
            setMuteTimeRemaining('');
            return;
          }

          const muteRef = ref(database, `/rooms/${roomId}/muted-users/${uid}`);
          const muteSnapshot = await get(muteRef);

          if (muteSnapshot.exists()) {
            const muteData = muteSnapshot.val();
            const endTime = new Date(muteData.endTime);
            const now = new Date();

            if (endTime > now) {
              // المستخدم ما زال مكتوم الصوت
              setIsMuted(true);
              setMuteEndTime(endTime);

              // حساب الوقت المتبقي
              const remainingMs = endTime - now;
              const remainingMins = Math.floor(remainingMs / 60000);
              const remainingHours = Math.floor(remainingMins / 60);

              if (remainingHours > 0) {
                setMuteTimeRemaining(`${remainingHours} ساعة و ${remainingMins % 60} دقيقة`);
              } else {
                setMuteTimeRemaining(`${remainingMins} دقيقة`);
              }
            } else {
              // انتهت مدة كتم الصوت، إزالة السجل
              await set(muteRef, null);
              setIsMuted(false);
              setMuteEndTime(null);
              setMuteTimeRemaining('');
            }
          } else {
            setIsMuted(false);
            setMuteEndTime(null);
            setMuteTimeRemaining('');
          }
        } catch (error) {
          console.error('Error checking mute status:', error);
          setIsMuted(false);
          setMuteEndTime(null);
          setMuteTimeRemaining('');
        }
      };

      // التحقق من حالة الحظر في الدردشة الخاصة
      const checkBlockStatus = async () => {
        try {
          // التحقق مما إذا كانت الدردشة خاصة
          if (!isDirectMessage || !chatId) {
            setIsUserBlocked(false);
            setBlockedByCurrentUser(false);
            setBlockedByOtherUser(false);
            return;
          }

          const currentUserId = auth.currentUser.uid;

          // التحقق مما إذا كان المستخدم الحالي قد حظر المستخدم الآخر
          const blockedByMeRef = ref(database, `/private-blocks/${currentUserId}/${uid}`);
          const blockedByMeSnapshot = await get(blockedByMeRef);

          // التحقق مما إذا كان المستخدم الآخر قد حظر المستخدم الحالي
          const blockedByThemRef = ref(database, `/private-blocks/${uid}/${currentUserId}`);
          const blockedByThemSnapshot = await get(blockedByThemRef);

          const isBlockedByMe = blockedByMeSnapshot.exists();
          const isBlockedByThem = blockedByThemSnapshot.exists();

          setBlockedByCurrentUser(isBlockedByMe);
          setBlockedByOtherUser(isBlockedByThem);
          setIsUserBlocked(isBlockedByMe || isBlockedByThem);

        } catch (error) {
          console.error('Error checking block status:', error);
          setIsUserBlocked(false);
          setBlockedByCurrentUser(false);
          setBlockedByOtherUser(false);
        }
      };

      checkMuteStatus();
      checkBlockStatus();
    }
  }, [isOpen, uid, roomId, chatId, isDirectMessage]);

  // كتم صوت المستخدم
  const handleMuteUser = async () => {
    if (uid === auth.currentUser.uid) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          لا يمكنك كتم صوت نفسك
        </Message>
      );
      return;
    }

    if (!roomId) {
      console.error('No room ID available for muting user');
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ: لم يتم العثور على معرف الغرفة. يمكن كتم صوت المستخدمين فقط داخل غرف الدردشة.
        </Message>
      );
      return;
    }

    try {
      setIsMuting(true);

      // حساب وقت انتهاء الكتم
      const durationInMinutes = parseInt(selectedMuteDuration, 10);
      const now = new Date();
      const endTime = new Date(now.getTime() + durationInMinutes * 60000);

      // إضافة المستخدم إلى قائمة المكتومين في الغرفة الحالية
      await set(ref(database, `/rooms/${roomId}/muted-users/${uid}`), {
        name: profile.name,
        email: profile.email || '',
        avatar: profile.avatar || '',
        mutedAt: now.toISOString(),
        endTime: endTime.toISOString(),
        durationMinutes: durationInMinutes,
        mutedBy: {
          uid: auth.currentUser.uid,
          name: currentUserProfile.name,
          email: currentUserProfile.email || '',
        }
      });

      // عرض رسالة نجاح
      let durationText = '';
      if (durationInMinutes < 60) {
        durationText = `${durationInMinutes} دقيقة`;
      } else if (durationInMinutes === 60) {
        durationText = 'ساعة واحدة';
      } else {
        durationText = `${durationInMinutes / 60} ساعة`;
      }

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم كتم صوت المستخدم {profile.name} لمدة {durationText}
        </Message>
      );

      // تحديث حالة الكتم في الواجهة
      setIsMuted(true);
      setMuteEndTime(endTime);

      // حساب الوقت المتبقي
      const remainingMins = durationInMinutes;
      const remainingHours = Math.floor(remainingMins / 60);

      if (remainingHours > 0) {
        setMuteTimeRemaining(`${remainingHours} ساعة و ${remainingMins % 60} دقيقة`);
      } else {
        setMuteTimeRemaining(`${remainingMins} دقيقة`);
      }

      setIsMuting(false);
    } catch (error) {
      console.error('Error muting user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في كتم صوت المستخدم: {error.message}
        </Message>
      );
      setIsMuting(false);
    }
  };

  // إلغاء كتم صوت المستخدم
  const handleUnmuteUser = async () => {
    if (!roomId) {
      console.error('No room ID available for unmuting user');
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ: لم يتم العثور على معرف الغرفة. يمكن إلغاء كتم صوت المستخدمين فقط داخل غرف الدردشة.
        </Message>
      );
      return;
    }

    try {
      setIsMuting(true);

      // إزالة المستخدم من قائمة المكتومين في الغرفة الحالية
      await set(ref(database, `/rooms/${roomId}/muted-users/${uid}`), null);

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم إلغاء كتم صوت المستخدم {profile.name}
        </Message>
      );

      // تحديث حالة الكتم في الواجهة
      setIsMuted(false);
      setMuteEndTime(null);
      setMuteTimeRemaining('');

      setIsMuting(false);
    } catch (error) {
      console.error('Error unmuting user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إلغاء كتم صوت المستخدم: {error.message}
        </Message>
      );
      setIsMuting(false);
    }
  };

  // حظر المستخدم (حظر عام)
  const handleBanUser = async () => {
    if (uid === auth.currentUser.uid) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          لا يمكنك حظر نفسك
        </Message>
      );
      return;
    }

    try {
      setIsBanning(true);

      // إضافة المستخدم إلى قائمة المحظورين
      await set(ref(database, `/banned-users/${uid}`), {
        name: profile.name,
        email: profile.email || '',
        avatar: profile.avatar || '',
        bannedAt: new Date().toISOString(),
        bannedBy: {
          uid: auth.currentUser.uid,
          name: currentUserProfile.name,
          email: currentUserProfile.email || '',
        }
      });

      // إضافة علامة لتسجيل خروج المستخدم المحظور فوراً
      await set(ref(database, `/force-logout/${uid}`), {
        timestamp: new Date().toISOString(),
        reason: 'banned',
        bannedBy: auth.currentUser.uid,
        bannedByName: currentUserProfile.name
      });

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حظر المستخدم بنجاح وسيتم منعه من الدخول إلى التطبيق
        </Message>
      );

      setIsBanning(false);
      close();
    } catch (error) {
      console.error('Error banning user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في حظر المستخدم: {error.message}
        </Message>
      );
      setIsBanning(false);
    }
  };

  // حظر المستخدم في الدردشة الخاصة
  const handleBlockUser = async () => {
    if (uid === auth.currentUser.uid) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          لا يمكنك حظر نفسك
        </Message>
      );
      return;
    }

    try {
      setIsBlockingUser(true);
      const currentUserId = auth.currentUser.uid;

      // إضافة المستخدم إلى قائمة المحظورين في الدردشة الخاصة
      await set(ref(database, `/private-blocks/${currentUserId}/${uid}`), {
        name: profile.name,
        email: profile.email || '',
        avatar: profile.avatar || '',
        blockedAt: new Date().toISOString(),
        roomId: chatId || null
      });

      // إضافة رسالة نظام في الدردشة
      if (chatId) {
        const messagesRef = ref(database, `messages/${chatId}`);
        const newMessageRef = push(messagesRef);

        await set(newMessageRef, {
          content: `قام ${currentUserProfile.name} بحظر ${profile.name}. لن يتمكن أي منكما من إرسال رسائل في هذه الدردشة.`,
          createdAt: Date.now(),
          author: {
            uid: 'system',
            name: 'النظام',
            avatar: ''
          },
          isSystemMessage: true
        });
      }

      // تحديث حالة الحظر في الواجهة
      setBlockedByCurrentUser(true);
      setIsUserBlocked(true);

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم حظر المستخدم {profile.name} في هذه الدردشة
        </Message>
      );

      setIsBlockingUser(false);
    } catch (error) {
      console.error('Error blocking user in private chat:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في حظر المستخدم: {error.message}
        </Message>
      );
      setIsBlockingUser(false);
    }
  };

  // إلغاء حظر المستخدم في الدردشة الخاصة
  const handleUnblockUser = async () => {
    try {
      setIsBlockingUser(true);
      const currentUserId = auth.currentUser.uid;

      // إزالة المستخدم من قائمة المحظورين في الدردشة الخاصة
      await remove(ref(database, `/private-blocks/${currentUserId}/${uid}`));

      // إضافة رسالة نظام في الدردشة
      if (chatId) {
        const messagesRef = ref(database, `messages/${chatId}`);
        const newMessageRef = push(messagesRef);

        await set(newMessageRef, {
          content: `قام ${currentUserProfile.name} بإلغاء حظر ${profile.name}. يمكنكما الآن التواصل مجدداً.`,
          createdAt: Date.now(),
          author: {
            uid: 'system',
            name: 'النظام',
            avatar: ''
          },
          isSystemMessage: true
        });
      }

      // تحديث حالة الحظر في الواجهة
      setBlockedByCurrentUser(false);
      setIsUserBlocked(blockedByOtherUser); // لا يزال محظوراً إذا كان الطرف الآخر قد حظره

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم إلغاء حظر المستخدم {profile.name} في هذه الدردشة
        </Message>
      );

      setIsBlockingUser(false);
    } catch (error) {
      console.error('Error unblocking user in private chat:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إلغاء حظر المستخدم: {error.message}
        </Message>
      );
      setIsBlockingUser(false);
    }
  };

  // إنشاء مجموعة خاصة بين المستخدمين أو الانتقال إلى المجموعة الموجودة
  const handleSendPrivateMessage = async () => {
    if (uid === auth.currentUser.uid) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          لا يمكنك إرسال رسالة لنفسك
        </Message>
      );
      return;
    }

    try {
      // تعيين علامة إنشاء الدردشة
      sessionStorage.setItem('creating_chat', 'true');

      const currentUserId = auth.currentUser.uid;

      // البحث عن دردشة خاصة موجودة بين المستخدمين
      const roomsRef = ref(database, 'rooms');
      const roomsSnapshot = await get(roomsRef);

      let existingRoomId = null;

      if (roomsSnapshot.exists()) {
        const roomsData = roomsSnapshot.val();

        // البحث عن غرفة خاصة موجودة بين المستخدمين
        for (const roomId in roomsData) {
          const room = roomsData[roomId];

          // التحقق مما إذا كانت الغرفة دردشة خاصة بين المستخدمين
          if (
            room.isDirectMessage &&
            room.members &&
            room.members[currentUserId] &&
            room.members[uid]
          ) {
            // وجدنا دردشة خاصة موجودة
            existingRoomId = roomId;
            break;
          }
        }
      }

      // إذا وجدنا دردشة خاصة موجودة، ننتقل إليها
      if (existingRoomId) {
        // عرض رسالة نجاح
        toaster.push(
          <Message type="info" closable duration={4000}>
            الانتقال إلى الدردشة الخاصة مع {profile.name}
          </Message>
        );

        // إغلاق النافذة
        close();

        // الانتقال إلى المجموعة الموجودة
        setTimeout(() => {
          history.push(`/chat/${existingRoomId}`);

          // إزالة علامة إنشاء الدردشة بعد التوجيه
          setTimeout(() => {
            sessionStorage.removeItem('creating_chat');
          }, 3000);
        }, 1000);

        return;
      }

      // إذا لم نجد دردشة خاصة موجودة، ننشئ واحدة جديدة
      // إنشاء اسم للمجموعة الخاصة
      const roomName = `دردشة خاصة مع ${profile.name}`;

      // إنشاء مرجع للمجموعة الجديدة
      const newRoomRef = push(ref(database, 'rooms'));
      const roomId = newRoomRef.key;

      // إضافة المستخدمين إلى المجموعة
      const members = {};
      members[currentUserId] = true;
      members[uid] = true;

      // إنشاء بيانات المجموعة
      const roomData = {
        name: roomName,
        description: `دردشة خاصة بين ${currentUserProfile.name} و ${profile.name}`,
        createdAt: Date.now(),
        createdBy: currentUserId,
        isPrivate: true,
        isSpecial: true, // علامة خاصة للدردشات الخاصة بين شخصين
        isDirectMessage: true, // علامة للدردشة المباشرة بين شخصين
        members,
        // لا نضيف أي مشرفين للدردشة الخاصة
        admins: {},
        // إضافة علامة لإخفاء المجموعة عن الطرف الآخر حتى يتم إرسال أول رسالة
        visibleTo: {
          [currentUserId]: true
        },
        // إضافة علامة تشير إلى أن المجموعة لم يتم إرسال أي رسائل فيها بعد
        hasMessages: false
      };

      // إضافة المجموعة إلى قاعدة البيانات
      await set(newRoomRef, roomData);

      // إضافة رسالة ترحيبية
      const messagesRef = ref(database, `messages/${roomId}`);
      const newMessageRef = push(messagesRef);

      await set(newMessageRef, {
        content: 'مرحباً بكما في الدردشة الخاصة! يمكنكما الآن التواصل بشكل خاص.',
        createdAt: Date.now(),
        author: {
          uid: 'system',
          name: 'النظام',
          avatar: ''
        }
      });

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم إنشاء دردشة خاصة مع {profile.name} بنجاح
        </Message>
      );

      // إغلاق النافذة
      close();

      // إضافة تأخير قبل الانتقال للتأكد من تحديث قائمة الغرف
      setTimeout(() => {
        // الانتقال إلى المجموعة الجديدة
        history.push(`/chat/${roomId}`);

        // إزالة علامة إنشاء الدردشة بعد التوجيه
        setTimeout(() => {
          sessionStorage.removeItem('creating_chat');
        }, 3000);
      }, 1000);
    } catch (error) {
      console.error('Error handling private message:', error);

      // عرض رسالة خطأ
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إنشاء/الانتقال إلى الدردشة الخاصة: {error.message}
        </Message>
      );

      // إزالة علامة إنشاء الدردشة في حالة الخطأ
      sessionStorage.removeItem('creating_chat');
    }
  };

  return (
    <>
      <Button {...btnProps} onClick={open}>
        {btnProps.className && btnProps.className.includes('avatar-profile-btn') ? (
          <div className="small-profile-avatar-wrapper">
            <ProfileAvatar
              src={avatar}
              name={name}
              uid={uid}
              showUid={false}
              size="xs"
              circle
              className="small-avatar"
            />
          </div>
        ) : (
          shortName
        )}
      </Button>
      <Modal open={isOpen} onClose={close} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">الملف الشخصي لـ {shortName}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center dark-modal-body">
          <div className="profile-avatar-wrapper large-profile-wrapper">
            <ProfileAvatar
              src={avatar}
              name={name}
              uid={uid}
              showUid={false}
              className="width-200 height-200 img-fullsize font-huge"
            />
          </div>

          <h4 className={`mt-2 dark-modal-name ${
            email === '<EMAIL>' || uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1'
              ? 'owner-name'
              : isProfileAdmin
                ? 'admin-name'
                : ''
          }`}>
            {name}
            {isProfileAdmin && uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' && email !== '<EMAIL>' && (
              <Badge content="مشرف" className="admin-badge ml-1" />
            )}
          </h4>

          <div className="user-id-container dark-uid-container">
            <div className="uid-label">معرف المستخدم (UID)</div>
            <div className="uid-value">{uid}</div>
          </div>

          <p className="user-email dark-modal-email">{email || 'لا يوجد بريد إلكتروني'}</p>
          <p className="dark-modal-text">عضو منذ {memberSince}</p>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          {children}

          {/* عرض حالة كتم الصوت إذا كان المستخدم مكتوم */}
          {isMuted && (
            <div className="mute-status-container mb-2">
              <Badge content="مكتوم" color="yellow" className="mute-badge" />
              <span className="mute-time-remaining">متبقي: {muteTimeRemaining}</span>
            </div>
          )}

          {/* أزرار كتم الصوت للمشرفين والمالك */}
          {isCurrentUserAdmin && uid !== auth.currentUser.uid &&
           uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' &&
           email !== '<EMAIL>' &&
           // منع المشرفين من حظر أو كتم مشرفين آخرين
           (!isProfileAdmin || (isProfileAdmin && (auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.currentUser.email === '<EMAIL>'))) && (
            <>
              {isMuted ? (
                <Button
                  block
                  color="yellow"
                  appearance="primary"
                  onClick={handleUnmuteUser}
                  disabled={isMuting}
                  className="mb-2"
                >
                  {isMuting ? 'جاري إلغاء الكتم...' : 'إلغاء كتم الصوت'}
                </Button>
              ) : (
                <div className="mute-controls mb-2">
                  <SelectPicker
                    data={muteDurationOptions}
                    value={selectedMuteDuration}
                    onChange={value => setSelectedMuteDuration(value)}
                    cleanable={false}
                    searchable={false}
                    className="mute-duration-select"
                    block
                    size="md"
                  />
                  <Button
                    block
                    color="yellow"
                    appearance="primary"
                    onClick={handleMuteUser}
                    disabled={isMuting}
                    className="mt-2"
                  >
                    {isMuting ? 'جاري الكتم...' : 'كتم الصوت'}
                  </Button>
                </div>
              )}

              <Button
                block
                color="red"
                appearance="primary"
                onClick={handleBanUser}
                disabled={isBanning}
                className="mb-2"
              >
                {isBanning ? 'جاري الحظر...' : 'حظر المستخدم'}
              </Button>
            </>
          )}

          {/* زر إرسال رسالة خاصة - يظهر فقط إذا لم تكن في دردشة خاصة بالفعل */}
          {uid !== auth.currentUser.uid && !isDirectMessage && (
            <Button
              block
              color="green"
              appearance="primary"
              onClick={handleSendPrivateMessage}
              className="mb-2"
            >
              <SendIcon style={{ marginLeft: '5px' }} /> إنشاء غرفة خاصة
            </Button>
          )}

          {/* زر حظر المستخدم في الدردشة الخاصة - يظهر فقط في الدردشات الخاصة */}
          {uid !== auth.currentUser.uid && isDirectMessage && !isBlockingUser && !blockedByCurrentUser && (
            <Button
              block
              appearance="primary"
              color="red"
              onClick={handleBlockUser}
              className="mb-2"
            >
              حظر المستخدم في هذه الدردشة
            </Button>
          )}

          {/* زر إلغاء حظر المستخدم في الدردشة الخاصة - يظهر فقط في الدردشات الخاصة إذا كان المستخدم محظوراً */}
          {uid !== auth.currentUser.uid && isDirectMessage && !isBlockingUser && blockedByCurrentUser && (
            <Button
              block
              appearance="primary"
              color="green"
              onClick={handleUnblockUser}
              className="mb-2"
            >
              إلغاء حظر المستخدم
            </Button>
          )}

          {/* رسالة إذا كان المستخدم الآخر قد حظر المستخدم الحالي */}
          {uid !== auth.currentUser.uid && isDirectMessage && blockedByOtherUser && (
            <div className="text-center mb-2 text-red">
              <strong>قام هذا المستخدم بحظرك في هذه الدردشة</strong>
            </div>
          )}

          <Button block onClick={close} appearance="subtle" className="dark-close-btn">
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ProfileInfoBtnModal;