import { ref, get, push, set } from 'firebase/database';
import { database } from './firebase.config';

/**
 * معالج الإشارة للمستخدمين باستخدام @
 * يستخدم للبحث عن الإشارات في النص وإرسال إشعارات للمستخدمين المشار إليهم
 */

/**
 * البحث عن الإشارات في النص
 * @param {string} text - النص المراد البحث فيه
 * @returns {object} - كائن يحتوي على معرفات المستخدمين المشار إليهم وما إذا كان هناك إشارة للجميع
 */
export const findMentions = (text) => {
  // البحث عن النمط @UID في النص
  const mentionRegex = /@([a-zA-Z0-9]{28})/g;
  const mentions = [];
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1]);
  }

  // البحث عن @All في النص (للإشارة إلى جميع المستخدمين)
  const mentionAll = text.includes('@All');

  return {
    userIds: mentions,
    mentionAll
  };
};

/**
 * التحقق مما إذا كان المستخدم هو المالك
 * @param {object} profile - ملف المستخدم
 * @returns {boolean} - إذا كان المستخدم هو المالك
 */
export const isOwner = (profile) => {
  return (
    profile &&
    (profile.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
     profile.email === '<EMAIL>')
  );
};

/**
 * إرسال إشعار للمستخدم المشار إليه
 * @param {string} uid - معرف المستخدم المشار إليه
 * @param {object} message - بيانات الرسالة
 * @param {object} author - بيانات المؤلف
 * @param {string} roomId - معرف الغرفة
 */
export const sendMentionNotification = async (uid, message, author, roomId) => {
  try {
    // التحقق من وجود المستخدم
    const userRef = ref(database, `users/${uid}`);
    const userSnapshot = await get(userRef);

    if (!userSnapshot.exists()) {
      console.log(`المستخدم ${uid} غير موجود`);
      return;
    }

    // إنشاء إشعار جديد
    const notificationsRef = ref(database, `notifications/${uid}`);
    const newNotificationRef = push(notificationsRef);

    // بيانات الإشعار
    const notification = {
      type: 'mention',
      title: author.name,
      body: `تمت الإشارة إليك في رسالة: ${message.text.substring(0, 50)}${message.text.length > 50 ? '...' : ''}`,
      link: `/chat/${roomId}`,
      timestamp: Date.now(),
      read: false,
      createdAt: Date.now(),
      data: {
        authorUid: author.uid,
        authorName: author.name,
        messageText: message.text,
        roomId
      }
    };

    // حفظ الإشعار في قاعدة البيانات
    await set(newNotificationRef, notification);
    console.log(`تم إرسال إشعار إشارة للمستخدم ${uid}`);
  } catch (error) {
    console.error('خطأ في إرسال إشعار الإشارة:', error);
  }
};

/**
 * إرسال إشعار لجميع المستخدمين
 * @param {object} message - بيانات الرسالة
 * @param {object} author - بيانات المؤلف
 * @param {string} roomId - معرف الغرفة
 */
export const sendMentionAllNotification = async (message, author, roomId) => {
  try {
    console.log('جاري إرسال إشعار لجميع المستخدمين');

    // الحصول على قائمة المستخدمين
    const usersRef = ref(database, 'users');
    const usersSnapshot = await get(usersRef);

    if (!usersSnapshot.exists()) {
      console.log('لا يوجد مستخدمين');
      return;
    }

    const users = usersSnapshot.val();
    const userIds = Object.keys(users);

    console.log(`تم العثور على ${userIds.length} مستخدم`);

    // إرسال إشعار لكل مستخدم (باستثناء المؤلف)
    for (const uid of userIds) {
      // تجاهل المؤلف نفسه
      if (uid === author.uid) {
        continue;
      }

      // إنشاء إشعار جديد
      const notificationsRef = ref(database, `notifications/${uid}`);
      const newNotificationRef = push(notificationsRef);

      // بيانات الإشعار
      const notification = {
        type: 'mention',
        title: `${author.name} (إشعار للجميع)`,
        body: `رسالة للجميع: ${message.text.substring(0, 50)}${message.text.length > 50 ? '...' : ''}`,
        link: `/chat/${roomId}`,
        timestamp: Date.now(),
        read: false,
        createdAt: Date.now(),
        data: {
          authorUid: author.uid,
          authorName: author.name,
          messageText: message.text,
          roomId,
          isGlobal: true
        }
      };

      // حفظ الإشعار في قاعدة البيانات
      await set(newNotificationRef, notification);
    }

    console.log('تم إرسال إشعار لجميع المستخدمين بنجاح');
  } catch (error) {
    console.error('خطأ في إرسال إشعار للجميع:', error);
  }
};

/**
 * معالجة الإشارات في الرسالة
 * @param {string} text - نص الرسالة
 * @param {object} profile - ملف المستخدم
 * @param {object} message - بيانات الرسالة
 * @param {string} roomId - معرف الغرفة
 * @returns {Promise<void>}
 */
export const processMentions = async (text, profile, message, roomId) => {
  // التحقق مما إذا كان المستخدم هو المالك
  if (!isOwner(profile)) {
    return;
  }

  // البحث عن الإشارات في النص
  const { userIds, mentionAll } = findMentions(text);

  // إذا كان هناك إشارة للجميع (@All)
  if (mentionAll) {
    console.log('تم العثور على إشارة للجميع (@All)');
    await sendMentionAllNotification(message, profile, roomId);
  }

  // إرسال إشعارات للمستخدمين المشار إليهم بشكل فردي
  for (const uid of userIds) {
    await sendMentionNotification(uid, message, profile, roomId);
  }
};
