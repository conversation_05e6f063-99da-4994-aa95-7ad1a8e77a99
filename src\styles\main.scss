@import 'override';
@import 'utility';
@import 'utility_colors';
@import 'rtl-support';
@import 'special-message';
@import 'message-views';
@import 'like-badge';
@import 'dictionary-manager';
@import 'notifications';
@import 'notification_dot';

// متغيرات الألوان للوضع الداكن
$dark-bg: #121212;
$dark-secondary-bg: #1e1e1e;
$dark-text: #e4e6eb;
$dark-border: #3a3b3c;
$dark-hover: #2d2d2d;
$dark-message-self: #0084ff;
$dark-message-other: #3a3b3c;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* تحسين تجربة المستخدم على الأجهزة المحمولة */
  -webkit-overflow-scrolling: touch; /* تحسين التمرير على iOS */

  background-color: #121212;
  background-image: linear-gradient(to bottom, #121212, #1a1a1a);
  color: $dark-text;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: -1;
  }
}

html,
body,
#root,
#root > div {
  height: 100%;
}

.custom-scroll {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #ddd;
  }

  &::-webkit-scrollbar-thumb {
    background: darken(#ddd, 20%);
  }
}

.img-fullsize {
  * {
    width: auto !important;
    height: auto !important;
  }
}

.chat-top,
.chat-middle,
.chat-bottom {
  margin: 0;
  padding: 0;
}

.chat-top,
.chat-bottom {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

$chat-t: 75px;
$chat-b: 65px;

.chat-top {
  height: $chat-t;
}

.chat-bottom {
  height: $chat-b;
}

.chat-middle {
  height: calc(100% - #{$chat-t} - #{$chat-b});
}

.msg-list {
  padding: 0;
  margin: 0;
  height: 100%;
  overflow-y: auto;
  list-style-type: none;
  li {
    &:last-child {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

.animate-blink {
  animation: blink normal 1.5s infinite ease-in-out;
  color: red !important;
  @keyframes blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 1;
    }
  }
}

// تصميم الشريط السفلي
.bottom-navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $dark-secondary-bg;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  z-index: 100;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-nav-container {
  display: flex;
  width: 100%;
  justify-content: space-around;
  align-items: center;
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6px 0;
  color: $dark-text;

  &:hover, &.rs-nav-item-active {
    color: #3498ff;
  }

  span {
    font-size: 11px;
    margin-top: 3px;
  }
}

// تصميم الدردشة على غرار ماسنجر
.msg-list {
  padding: 8px;

  li {
    margin-bottom: 8px; // تقليل المسافة العامة بين الرسائل
    position: relative;
    display: flex;
    align-items: flex-start;

    // تنسيق رسائل النظام
    &.msg-system {
      justify-content: center;
      margin: 15px 0;

      .system-message {
        background-color: rgba(255, 0, 0, 0.1);
        border: 1px solid rgba(255, 0, 0, 0.3);
        border-radius: 10px;
        padding: 10px 15px;
        max-width: 90%;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

        .system-message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 5px;

          .system-message-author {
            font-weight: bold;
            color: #ff5555;
            font-size: 12px;
          }

          .system-message-time {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .system-message-content {
          .system-message-text {
            color: #ffcccc;
            font-size: 13px;
            white-space: pre-wrap;
            margin: 0;
            font-family: inherit;
            line-height: 1.5;
          }
        }
      }
    }

    // تقليل المسافة بين رسائل نفس المستخدم
    &.same-author {
      margin-top: -2px; // تقليل المسافة إلى 6px (8px - 2px)

      // إخفاء معلومات المؤلف للرسائل المتتالية من نفس المستخدم
      .msg-author {
        display: none;
      }

      // تعديل شكل الفقاعة للرسائل المتتالية
      &.msg-self .msg-bubble {
        border-radius: 18px 4px 18px 18px;
      }

      &.msg-other .msg-bubble {
        border-radius: 4px 18px 18px 18px;
      }
    }

    &.msg-self {
      justify-content: flex-end;

      .msg-bubble {
        background-color: rgba(45, 45, 45, 0.9);
        backdrop-filter: blur(5px);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 18px 4px 18px 18px; // شكل فقاعة مميز للمرسل
        margin-left: 5px; // تقليل المساحة للصورة الشخصية
        margin-right: 5px; // تقليل المساحة للأزرار

        .msg-text {
          color: white;
        }
      }
    }

    &.msg-other {
      justify-content: flex-start;

      .msg-bubble {
        background-color: rgba(58, 59, 60, 0.7);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.05);
        border-radius: 4px 18px 18px 18px; // شكل فقاعة مميز للمستقبل
        margin-left: 5px; // تقليل المساحة للصورة الشخصية
        margin-right: 5px; // تقليل المساحة للأزرار

        .msg-text {
          color: $dark-text;
        }
      }
    }
  }
}

.msg-bubble {
  padding: 10px 14px;
  position: relative;
  display: inline-block; // لجعل العرض يتناسب مع المحتوى
  max-width: 80%; // الحد الأقصى للعرض
  min-width: 50px; // الحد الأدنى للعرض
  word-wrap: break-word;
  word-break: break-word; // لضمان التفاف النص بشكل صحيح
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 0; // إزالة الهامش السفلي لإفساح المجال للأزرار
}

.msg-author {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 3px;

  .author-info {
    display: flex;
    flex-direction: column;

    span {
      display: flex;
      align-items: center;
    }

    .admin-badge {
      font-size: 8px;
      padding: 0 4px;
      margin-right: 5px;
      background-color: rgba(29, 161, 242, 0.2); // تغيير لون خلفية الشارة إلى اللون الأزرق
      color: #1DA1F2; // تغيير لون نص الشارة إلى اللون الأزرق
      border-radius: 4px;
      height: 14px;
      line-height: 14px;
      display: inline-block; // إضافة عرض كتلة مضمنة للشارة

      &.ml-1 {
        margin-right: 5px;
        font-size: 10px;
        height: 16px;
        line-height: 16px;
      }
    }

    // تنسيق اسم المالك
    .owner-name {
      color: #FF3A3A !important;
      font-weight: bold;
      position: relative;
    }

    // تنسيق اسم المشرف
    .admin-name {
      color: #1DA1F2 !important; // تغيير لون اسم المشرف إلى اللون الأزرق
      font-weight: bold;
      position: relative;

      // إضافة علامة صح زرقاء
      &::after {
        content: "✓";
        display: inline-block;
        background-color: #1DA1F2;
        color: white;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        font-size: 8px;
        line-height: 12px;
        text-align: center;
        position: absolute;
        top: -4px;
        right: -4px;
        border: 1px solid white;
      }
    }

    .author-email, .author-uid {
      font-size: 8px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: normal;
      margin-top: 1px;
      direction: ltr;
      text-align: left;
    }
  }
}

// تنسيق البريد الإلكتروني في الملف الشخصي
.user-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: -5px;
  margin-bottom: 10px;
}

.msg-text {
  font-size: 15px; // تكبير حجم الخط أكثر
  line-height: 1.5;
  white-space: pre-wrap; // للحفاظ على تنسيق النص
  overflow-wrap: break-word; // لضمان التفاف النص الطويل
  word-break: break-word; // لتقسيم الكلمات الطويلة
  letter-spacing: 0.2px; // تحسين المسافة بين الحروف
  padding: 2px 0; // إضافة مساحة داخلية للنص
}

.msg-time {
  font-size: 9px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
  display: block;
}

.msg-actions {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: 6px; // مسافة بين نهاية مربع الرسالة والأزرار
  padding: 0;
  z-index: 5;
  opacity: 0.8; // جعل الأزرار شفافة قليلاً

  // تنسيق الأزرار لتكون أصغر وأكثر أناقة
  .rs-btn-subtle {
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    padding: 2px 6px;
    font-size: 10px;
    margin-right: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.9);
    }
  }

  // تصغير أيقونات الأزرار
  .rs-icon {
    font-size: 12px;
  }

  // تنسيق شارة عدد الإعجابات
  .rs-badge-content {
    font-size: 7px;
    padding: 0 1px;
    min-width: 8px;
    height: 8px;
    line-height: 8px;
  }

  // تنسيق خاص لشارة الإعجابات
  .like-badge .rs-badge-content {
    transform: translateX(0) translateY(-15px) scale(0.8);
    position: absolute;
    right: 0;
    top: -5px;
  }

  // تأثير التحويم
  &:hover {
    opacity: 1;
  }
}

// محاذاة الأزرار حسب نوع الرسالة
.msg-other .msg-actions {
  justify-content: flex-end; // محاذاة الأزرار إلى اليمين لرسائل الآخرين
}

.msg-self .msg-actions {
  justify-content: flex-start; // محاذاة الأزرار إلى اليسار لرسائل المستخدم نفسه
  flex-direction: row-reverse; // عكس ترتيب الأزرار ليكون منطقيًا

  // تعديل هوامش الأزرار عند عكس الترتيب
  .rs-btn-subtle {
    margin-right: 0;
    margin-left: 2px;
  }
}

// تعديل ارتفاع المحتوى الرئيسي لإفساح المجال للشريط السفلي
.content-with-bottom-navbar {
  padding-bottom: 80px; // زيادة المساحة في الأسفل لإفساح المجال للشريط السفلي
}

// أنماط صفحة تأكيد البريد الإلكتروني
.email-verification-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  direction: rtl;
  margin: 0;
  background-color: #121212;
}

.email-verification-box {
  background-color: #1e1e1e;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.6);
  text-align: center;
  width: 360px;
}

.verify-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.checkmark {
  width: 40px;
  height: 40px;
  background-color: #4caf50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 22px;
}

.verify-text {
  font-size: 18px;
  color: #4caf50;
  font-weight: bold;
}

.verification-message {
  font-size: 17px;
  margin-top: 10px;
  line-height: 1.6;
  color: #ccc;
}

// أنماط مربع الرد على الرسائل
.reply-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  margin-bottom: 6px;
  background-color: rgba(30, 30, 30, 0.7);
  border-right: 2px solid rgba(0, 132, 255, 0.7);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);

  .reply-preview-content {
    flex: 1;
    overflow: hidden;
  }

  .reply-author {
    font-size: 10px;
    font-weight: bold;
    color: rgba(0, 132, 255, 0.9);
    margin-bottom: 2px;
  }

  .reply-text {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .reply-close-btn {
    margin-right: 6px;
    color: rgba(255, 255, 255, 0.6);

    &:hover {
      color: white;
    }
  }
}

// أنماط الرد على الرسائل في فقاعة الدردشة
.replied-message {
  padding: 4px 8px;
  margin-bottom: 6px;
  background-color: rgba(0, 0, 0, 0.15);
  border-right: 2px solid rgba(0, 132, 255, 0.7);
  border-radius: 6px;
  font-size: 11px;
  backdrop-filter: blur(3px);

  .replied-message-text {
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    padding-right: 10px; // إضافة مساحة للنص
    display: flex;
    align-items: center;

    .reply-icon {
      color: rgba(0, 132, 255, 0.9);
      margin-right: 6px; // تغيير موضع أيقونة الرد لتكون قبل النص
      margin-left: 0;
      font-size: 12px;
    }
  }
}

// تعديل أنماط الدردشة لتكون أكثر جاذبية
.chat-middle {
  background-color: rgba(18, 18, 18, 0.8);
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  padding: 6px;
  border-radius: 0;
  box-shadow: none;
  backdrop-filter: blur(5px);
}

// تعديل أنماط الشريط العلوي
.chat-top {
  background-color: rgba(18, 18, 18, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding: 0;
}

// تنسيق اللوحة الداكنة
.dark-panel {
  .rs-panel-header {
    background-color: #000;
    color: white;
    border-bottom: 1px solid #333;
  }

  .rs-panel-body {
    background-color: #000;
    color: white;
  }

  .rs-list-item {
    background-color: #1a1a1a;
    color: white;
    border-bottom: 1px solid #333;
    margin-bottom: 5px;
    border-radius: 5px;
  }
}

// تنسيقات صفحة السجلات
.message-item {
  background-color: rgba(30, 30, 30, 0.7);
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);

  .message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;

    .message-author {
      display: flex;
      align-items: center;

      .author-name {
        margin-right: 8px;
        font-weight: bold;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .message-time {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .message-content {
    padding: 5px;
    color: rgba(255, 255, 255, 0.8);
    white-space: pre-wrap;
  }
}

// تنسيق أزرار معلومات وتعديل الغرفة
.room-btn {
  font-size: 12px !important;
  padding: 8px 12px !important;
  margin: 0 5px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(5px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: white !important;

  &:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  }

  &:active {
    transform: translateY(0) !important;
    box-shadow: none !important;
  }
}

.room-info-btn {
  &:hover {
    border-color: rgba(10, 133, 222, 0.5) !important;
  }
}

.room-edit-btn {
  &:hover {
    border-color: rgba(40, 167, 69, 0.5) !important;
  }
}

.room-delete-btn {
  &:hover {
    border-color: rgba(220, 53, 69, 0.5) !important;
  }
}

// أنماط أزرار إيقاف الدردشة
.chat-close-btn {
  font-size: 12px !important;
  padding: 8px 12px !important;
  margin: 0 5px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  background-color: rgba(255, 0, 0, 0.1) !important;
  backdrop-filter: blur(5px) !important;
  border: 1px solid rgba(255, 0, 0, 0.1) !important;
  color: white !important;

  &:hover {
    background-color: rgba(255, 0, 0, 0.2) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    border-color: rgba(255, 0, 0, 0.3) !important;
  }
}

.schedule-btn {
  font-size: 12px !important;
  padding: 8px 12px !important;
  margin: 0 5px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  background-color: rgba(255, 165, 0, 0.1) !important;
  backdrop-filter: blur(5px) !important;
  border: 1px solid rgba(255, 165, 0, 0.1) !important;
  color: white !important;

  &:hover {
    background-color: rgba(255, 165, 0, 0.2) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    border-color: rgba(255, 165, 0, 0.3) !important;
  }
}

// تنسيق نافذة تعديل المجموعة
.dark-drawer {
  background-color: #1a1a1a !important;
  color: white !important;

  .rs-drawer-header {
    background-color: #1a1a1a !important;
    color: white !important;
    border-bottom: 1px solid #333 !important;
  }

  .rs-drawer-title {
    color: white !important;
  }

  .rs-drawer-body {
    background-color: #1a1a1a !important;
    color: white !important;
  }

  .rs-drawer-actions {
    background-color: #1a1a1a !important;
    border-top: 1px solid #333 !important;
  }

  .rs-form-control-label {
    color: white !important;
  }

  .rs-form-help-text {
    color: #aaa !important;
  }

  .rs-input {
    background-color: #333 !important;
    color: white !important;
    border: 1px solid #444 !important;

    &:focus {
      border-color: #0a85de !important;
    }
  }

  .rs-toggle {
    background-color: #333 !important;

    &-checked {
      background-color: #0a85de !important;
    }
  }
}

// تنسيق صفحة إدارة المشرفين
.admin-section-title {
  color: #fff !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding-bottom: 8px !important;
}

.admin-panel-box {
  background-color: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 10px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.admin-input-group {
  .admin-input {
    background-color: rgba(0, 0, 0, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px 0 0 8px !important;

    &:focus {
      border-color: rgba(10, 133, 222, 0.5) !important;
    }
  }

  .admin-add-btn {
    background-color: rgba(40, 167, 69, 0.2) !important;
    color: #28a745 !important;
    border: 1px solid rgba(40, 167, 69, 0.3) !important;
    border-radius: 0 8px 8px 0 !important;

    &:hover:not(:disabled) {
      background-color: rgba(40, 167, 69, 0.3) !important;
    }

    &:active:not(:disabled) {
      background-color: rgba(40, 167, 69, 0.4) !important;
    }
  }
}

.admin-list {
  .admin-list-item {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px !important;
    margin-bottom: 8px !important;
    transition: all 0.2s ease !important;

    &:hover {
      background-color: rgba(0, 0, 0, 0.3) !important;
    }
  }

  .admin-name {
    color: #1DA1F2 !important;
    display: block !important;
    margin-bottom: 2px !important;
  }

  .admin-owner-name {
    color: #ff4d4f !important;
    display: block !important;
    margin-bottom: 2px !important;
  }

  .admin-uid, .admin-email {
    font-size: 12px !important;
    color: #999 !important;
    direction: ltr !important;
  }

  .admin-remove-btn {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
    border: 1px solid rgba(220, 53, 69, 0.2) !important;
    border-radius: 5px !important;

    &:hover:not(:disabled) {
      background-color: rgba(220, 53, 69, 0.2) !important;
    }

    &:active:not(:disabled) {
      background-color: rgba(220, 53, 69, 0.3) !important;
    }
  }
}

.chat-top-container {
  padding: 0 10px;

  .room-name-display {
    font-size: 16px;
    margin: 0;
    color: $dark-text;
    font-weight: 500;
    padding-top: 2px;
  }

  .link-unstyled {
    display: flex;
    align-items: center;
    margin-left: 5px;

    &.back-button {
      background-color: rgba(0, 132, 255, 0.1);
      padding: 5px;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 132, 255, 0.2);
        transform: scale(1.1);
      }

      .text-blue {
        font-size: 18px;
      }
    }
  }

  .text-blue {
    color: rgba(0, 132, 255, 0.9);
  }
}

// تعديل أنماط الشريط السفلي للدردشة
.chat-bottom {
  background-color: rgba(30, 30, 30, 0.7);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  padding: 8px;

  // مؤشر الكتابة
  .typing-container {
    position: relative;
    min-height: 20px;
  }

  .typing-indicator {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    padding: 4px 10px;
    margin-bottom: 5px;
    text-align: right;
    direction: rtl;

    &.typing-indicator-top {
      position: absolute;
      bottom: 100%;
      right: 0;
      margin-bottom: 2px;
      background-color: rgba(30, 30, 30, 0.9);
      border-radius: 10px;
      padding: 6px 12px;
      max-width: 80%;
      z-index: 10;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    &:after {
      content: "...";
      display: inline-block;
      vertical-align: bottom;
      animation: ellipsis 1.5s infinite;
      width: 12px;
      text-align: left;
    }
  }

  @keyframes ellipsis {
    0% { content: "."; }
    33% { content: ".."; }
    66% { content: "..."; }
    100% { content: "."; }
  }

  // رسالة المجموعة المقيدة
  .owner-only-message {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    padding: 12px 15px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 5px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    direction: rtl;

    i {
      margin-left: 8px;
      color: #ff9800;
    }
  }

  .chat-input-group {
    background-color: transparent;

    .rs-input-group {
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .chat-input {
      background-color: rgba(45, 45, 45, 0.7);
      color: $dark-text;
      border: none;
      padding: 8px 12px;
      font-size: 13px;

      &::placeholder {
        color: rgba($dark-text, 0.5);
      }
    }

    .send-button {
      background-color: rgba(0, 132, 255, 0.8);
      color: white;
      border: none;

      &:hover {
        background-color: rgba(0, 132, 255, 1);
      }
    }

    .attachment-btn {
      background-color: rgba(30, 30, 30, 0.8);
      color: rgba(255, 255, 255, 0.8);
      border: none;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 132, 255, 0.6);
        color: white;
      }

      i {
        font-size: 16px;
      }
    }
  }
}

// تعديل أنماط عناصر الغرف
.room-item {
  padding: 8px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
  margin: 2px 0;
  border-radius: 8px;
  background-color: rgba(30, 30, 30, 0.3);
  backdrop-filter: blur(5px);

  &:hover {
    background-color: rgba(45, 45, 45, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .room-name {
    font-size: 14px;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
  }

  .room-time {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.5);
  }

  .room-last-message {
    margin-top: 3px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
  }

  .last-message-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
    display: inline-block;
  }

  .no-messages {
    font-style: italic;
    color: rgba(255, 255, 255, 0.4);
    font-size: 10px;
  }
}

// تعديل أنماط الشريط الجانبي
.sidebar-container {
  height: 100%;
  background-color: rgba(18, 18, 18, 0.9);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.05);
  padding: 0;
  overflow-y: auto;
}

// تنسيق حاوية الصورة الشخصية
.profile-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  // تكبير حجم الصورة الشخصية في نافذة معلومات المستخدم
  .width-200, .height-200 {
    width: 150px !important;
    height: 150px !important;
  }
}

// تنسيق صور المستخدمين في الدردشة
.msg-avatar-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 3px; // تقليل المسافة بين صورة الملف الشخصي والرسالة

  &.msg-avatar-self {
    order: -1; // وضع الأزرار في يمين الرسالة للمستخدم الحالي
    display: flex;
    flex-direction: row; // ترتيب الأزرار ليكون زر الحذف ثم زر الإعجاب
  }

  &.msg-avatar-other {
    order: -1; // تم تغيير الترتيب ليكون في يمين الرسالة للمستخدمين الآخرين
  }

  .rs-avatar {
    width: 5px; // تصغير حجم صورة الملف الشخصي إلى 5px
    height: 5px; // تصغير حجم صورة الملف الشخصي إلى 5px
    font-size: 2px; // تصغير حجم الخط
    border-radius: 50% !important; // ضمان أن جميع الصور دائرية
    overflow: hidden;
    border: 0.5px solid rgba(255, 255, 255, 0.1); // تقليل سمك الحدود
    box-shadow: none; // إزالة الظل

    // تأكيد أن الصورة دائرية
    &.rs-avatar-circle {
      border-radius: 50% !important;
    }

    // تأكيد أن الصورة داخل الأفاتار دائرية أيضاً
    img {
      border-radius: 50% !important;
      object-fit: cover;
      width: 100%;
      height: 100%;
    }

    // تحسين مظهر الأفاتار المحسن
    &.enhanced-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(30, 30, 30, 0.8);
    }
  }

  // تنسيق زر البروفايل داخل حاوية الصورة
  .avatar-profile-btn {
    padding: 0;
    background: none;
    border: none;

    &:hover, &:focus, &:active {
      background: none;
    }

    .rs-btn-content {
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    // إطار صورة الملف الشخصي الصغيرة
    .small-profile-avatar-wrapper {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .rs-avatar {
      width: 35px;
      height: 35px;
      font-size: 14px;
      border-radius: 50% !important;
      overflow: hidden;
      border: 1.5px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

      // تأكيد أن الصورة داخل الأفاتار دائرية أيضاً
      img {
        border-radius: 50% !important;
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }
  }
}

// تنسيق أزرار التفاعل للمستخدم الحالي
.msg-actions-self {
  order: 1; // وضع الأزرار في يسار الرسالة للمستخدم الحالي
  margin-right: 0;
  margin-left: 5px;

  // تكبير صورة المستخدم الحالي
  .self-avatar {
    width: 30px !important;
    height: 30px !important;
    font-size: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }
}

// تنسيق أزرار التفاعل للمستخدمين الآخرين
.msg-actions-other {
  order: 1; // وضع الأزرار في يسار الرسالة للمستخدمين الآخرين
  margin-left: 5px;
  margin-right: 0;
  align-self: center; // محاذاة الأزرار مع منتصف الرسالة
  margin-top: 15px; // تنزيل الأزرار قليلاً

  // تحسين أداء التحولات لمنع الاهتزاز
  .rs-btn {
    will-change: transform;
    transform: translateZ(0);
  }

  // تحسين أداء شارة عدد الإعجابات
  .rs-badge-content {
    will-change: transform;
    transform: translateZ(0);
  }
}

.sidebar-header {
  padding: 10px;
  background-color: rgba(30, 30, 30, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);

  .rs-divider {
    color: rgba(255, 255, 255, 0.7);
    opacity: 0.7;
    margin: 10px 0;
  }
}

// تنسيقات نافذة البروفايل الداكنة
.dark-modal {
  .rs-modal-content {
    background-color: rgba(18, 18, 18, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }

  .dark-modal-header {
    background-color: rgba(30, 30, 30, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;

    .dark-modal-title {
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }
  }

  .dark-modal-body {
    background-color: rgba(18, 18, 18, 0.95);
    padding: 20px;

    // تنسيق حالة كتم الصوت
    .mute-status-container {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 204, 0, 0.1);
      padding: 8px;
      border-radius: 5px;
      margin-bottom: 10px;
      width: 100%;

      .mute-badge {
        margin-right: 10px;
      }

      .mute-time-remaining {
        color: #ffcc00;
        font-size: 0.9rem;
      }
    }

    // تنسيق أدوات كتم الصوت
    .mute-controls {
      width: 100%;

      .mute-duration-select {
        width: 100%;
        background-color: #333;

        .rs-picker-toggle {
          background-color: #333;
          color: white;
          border-color: #555;
        }
      }
    }

    // تنسيقات إضافية لمكون إدارة المشرفين
    .rs-input {
      background-color: #222 !important;
      color: #fff !important;
      border-color: #444 !important;
    }

    .rs-input-group-addon {
      background-color: #333 !important;
      color: #fff !important;
      border-color: #444 !important;
    }

    .rs-list {
      background-color: #111 !important;
      color: #fff !important;
    }

    .rs-list-item {
      background-color: #222 !important;
      color: #fff !important;
      margin-bottom: 5px;
      border-radius: 5px;

      &:hover {
        background-color: #333 !important;
      }
    }

    .dark-modal-name {
      color: rgba(255, 255, 255, 0.9);
      margin-top: 15px;
      font-size: 18px;
    }

    .dark-modal-email {
      color: rgba(255, 255, 255, 0.6);
      font-size: 16px;
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .dark-modal-text {
      color: rgba(255, 255, 255, 0.7);
      font-size: 16px;
      font-weight: 500;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 8px 15px;
      border-radius: 8px;
      margin-top: 10px;
      display: inline-block;
    }

    // تنسيق محرر الصورة
    canvas {
      border-radius: 100px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .dark-modal-footer {
    background-color: rgba(30, 30, 30, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;

    .dark-close-btn {
      background-color: rgba(50, 50, 50, 0.8);
      color: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        background-color: rgba(60, 60, 60, 0.9);
        color: white;
      }
    }

    .dark-upload-btn {
      background-color: rgba(0, 132, 255, 0.8);
      color: white;
      border: none;

      &:hover, &:focus {
        background-color: rgba(0, 132, 255, 1);
      }

      &:disabled {
        background-color: rgba(0, 132, 255, 0.4);
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  .dark-uid-container {
    margin: 15px 0;
    padding: 10px;
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;

    .uid-label {
      font-weight: bold;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 5px;
      font-size: 13px;
    }

    .uid-value {
      word-break: break-all;
      font-size: 14px;
      direction: ltr;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-family: monospace;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 5px 10px;
      border-radius: 3px;
      width: 100%;
    }
  }
}

// تنسيق زر اختيار الصورة الشخصية
.avatar-upload-btn {
  display: inline-block;
  margin-top: 15px;
  padding: 8px 16px;
  background-color: rgba(30, 30, 30, 0.8);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(40, 40, 40, 0.9);
    color: white;
    cursor: pointer;
  }
}

// تنسيق نافذة الملف الشخصي
.dark-drawer {
  .rs-drawer-content {
    background-color: rgba(18, 18, 18, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5);

    .rs-drawer-header {
      background-color: rgba(30, 30, 30, 0.8);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 15px;

      .rs-drawer-header-close {
        color: rgba(255, 255, 255, 0.7);

        &:hover {
          color: white;
        }
      }

      .rs-drawer-title {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
      }
    }

    .rs-drawer-body {
      background-color: rgba(18, 18, 18, 0.95);
      padding: 15px;

      // تعديل حجم الصورة الشخصية في الملف الشخصي
      .width-150, .height-150 {
        width: 120px !important;
        height: 120px !important;
      }

      // تعديل حجم الصورة الشخصية الكبيرة
      .width-200, .height-200 {
        width: 250px !important;
        height: 250px !important;
      }

      // تعديل حجم الخط في الملف الشخصي
      .font-huge {
        font-size: 60px !important;
      }

      // إطار صورة الملف الشخصي
      .profile-avatar-wrapper {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 130px;
        height: 130px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        margin-bottom: 10px;

        // إطار صورة الملف الشخصي الكبير
        &.large-profile-wrapper {
          width: 280px;
          height: 280px;
          border: 2px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .rs-avatar {
          border: 2px solid rgba(255, 255, 255, 0.2);
          overflow: hidden !important;
          border-radius: 50% !important;

          img {
            object-fit: cover !important;
            width: 100% !important;
            height: 100% !important;
            border-radius: 50% !important;
          }
        }
      }

      // تحسين عرض الصور في الأفاتار
      .rs-avatar-image {
        object-fit: cover !important;
        width: 100% !important;
        height: 100% !important;
        border-radius: 50% !important;
      }

      // إطار صورة الملف الشخصي الصغيرة
      .small-profile-avatar-wrapper {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

        .rs-avatar {
          width: 35px !important;
          height: 35px !important;
          border: 1px solid rgba(255, 255, 255, 0.2);
          overflow: hidden !important;

          img {
            object-fit: cover !important;
            width: 100% !important;
            height: 100% !important;
            border-radius: 50% !important;
          }
        }
      }
    }
  }
}

// تنسيق نافذة الشروط والسياسات
.terms-modal-body {
  max-height: 400px;
  overflow-y: auto;

  .terms-section-title {
    color: rgba(0, 132, 255, 0.9);
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: 500;

    &:first-child {
      margin-top: 0;
    }
  }

  .terms-list {
    padding-right: 20px;
    margin-bottom: 15px;

    li {
      margin-bottom: 8px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .dark-modal-text {
    margin-bottom: 10px;
    line-height: 1.5;
  }
}

// تنسيق النموذج الداكن
.dark-form {
  .rs-form-group {
    margin-bottom: 15px;
  }

  .rs-form-control-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 5px;
  }

  .rs-form-control-wrapper {
    width: 100%;
  }

  .rs-form-help-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
  }

  .rs-form-error-message {
    color: #ff5252;
  }

  .rs-input {
    background-color: rgba(0, 0, 0, 0.8);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:focus {
      border-color: rgba(0, 132, 255, 0.5);
      box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.2);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }

  textarea.rs-input {
    background-color: rgba(0, 0, 0, 0.8);
    color: rgba(255, 255, 255, 0.9);
    min-height: 100px;
  }

  .rs-toggle {
    .rs-toggle-presentation {
      background-color: rgba(30, 30, 30, 0.8);

      &:before {
        background-color: rgba(255, 255, 255, 0.8);
      }
    }

    &.rs-toggle-checked {
      .rs-toggle-presentation {
        background-color: rgba(0, 132, 255, 0.5);
      }
    }
  }
}

.dark-submit-btn {
  background-color: rgba(0, 132, 255, 0.8);
  color: white;
  border: none;

  &:hover, &:focus {
    background-color: rgba(0, 132, 255, 1);
  }

  &:disabled {
    background-color: rgba(30, 30, 30, 0.8);
    color: rgba(255, 255, 255, 0.5);
  }
}

// تنسيق زر الملف الشخصي
.profile-button {
  display: flex;
  align-items: center;
  background-color: rgba(30, 30, 30, 0.8);
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    background-color: rgba(40, 40, 40, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .profile-button-avatar {
    margin-right: 10px;

    .profile-avatar {
      width: 40px !important;
      height: 40px !important;
      border-radius: 50%;
      border: 2px solid rgba(255, 255, 255, 0.2);
    }
  }

  .profile-button-info {
    flex: 1;
    overflow: hidden;

    .profile-button-name {
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .profile-button-email {
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// تنسيق الشريط السفلي المحسن
.enhanced-bottom-navbar {
  position: fixed;
  bottom: 0px; // نقل الشريط إلى أسفل الشاشة تمامًا
  left: 0;
  right: 0;
  z-index: 100; // زيادة z-index لضمان ظهوره فوق جميع العناصر
  display: flex;
  align-items: flex-end; // محاذاة العناصر مع أسفل الحاوية
  justify-content: center;
  pointer-events: none; // منع التفاعل مع الحاوية نفسها
  transition: opacity 0.3s ease;
  padding-bottom: 15px; // زيادة المساحة في الأسفل
  height: 80px; // زيادة ارتفاع الشريط السفلي

  // إخفاء الشريط في صفحة الدردشة
  &.hidden {
    opacity: 0;
    visibility: hidden;
  }
}

// تنسيق حاوية المربعات في الشريط السفلي
.bottom-nav-box-container {
  display: flex;
  justify-content: space-between;
  width: 320px; // زيادة المسافة بين المربعات
  margin-bottom: 10px; // إضافة مساحة في الأسفل
}

// تنسيق المربعات في الشريط السفلي
.bottom-nav-box {
  width: 50px; // تقليل حجم المربعات
  height: 50px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.9);
  pointer-events: auto; // السماح بالتفاعل مع المربعات فقط

  &:hover {
    transform: translateY(-5px);
  }

  // حالة الزر النشط
  &.active {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  // مربع الدردشة
  &.chat-box {
    background-color: rgba(0, 132, 255, 0.8);

    &:hover {
      background-color: rgba(0, 132, 255, 1);
    }

    &.active {
      background-color: rgba(0, 132, 255, 1);
    }
  }

  // مربع الدردشة الخاصة
  &.private-chat-box {
    background-color: rgba(76, 175, 80, 0.8);

    &:hover {
      background-color: rgba(76, 175, 80, 1);
    }

    &.active {
      background-color: rgba(76, 175, 80, 1);
    }
  }

  // مربع الإعدادات
  &.settings-box {
    background-color: rgba(45, 45, 45, 0.8);

    &:hover {
      background-color: rgba(60, 60, 60, 0.9);
    }

    &.active {
      background-color: rgba(60, 60, 60, 0.9);
    }
  }
}

// تنسيق صفحة المستخدمين المحظورين
.banned-users-container {
  padding: 20px;

  .rs-table {
    background-color: rgba(30, 30, 30, 0.8);
    color: white;

    .rs-table-cell {
      background-color: transparent;
      color: white;
    }

    .rs-table-cell-header {
      background-color: rgba(50, 50, 50, 0.8);
      color: white;
    }

    .rs-table-row:hover .rs-table-cell {
      background-color: rgba(60, 60, 60, 0.8);
    }
  }

  .rs-input-group {
    background-color: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(80, 80, 80, 0.8);

    .rs-input {
      background-color: transparent;
      color: white;

      &::placeholder {
        color: rgba(200, 200, 200, 0.6);
      }
    }

    .rs-input-group-addon {
      background-color: rgba(60, 60, 60, 0.8);
      color: white;
    }
  }
}

// تنسيق خيارات الإعدادات
.settings-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

// تنسيق عناوين الشروط والسياسات
.terms-section-title {
  color: #4caf50;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
}

.terms-subsection-title {
  color: #4caf50;
  margin-top: 15px;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: bold;
}

.terms-modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

.settings-option-btn {
  background-color: rgba(30, 30, 30, 0.8);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 10px;
  font-size: 14px;

  &:hover {
    background-color: rgba(40, 40, 40, 0.9);
    color: white;
  }
}

// تنسيق أزرار اللغة
.language-buttons {
  margin-top: 15px;

  .rs-btn-group {
    width: 100%;
  }

  .language-btn {
    padding: 10px;
    font-size: 14px;
    transition: all 0.2s ease;

    &.active {
      background-color: rgba(0, 132, 255, 0.8);
      color: white;
      font-weight: bold;
    }

    &:not(.active) {
      background-color: rgba(30, 30, 30, 0.8);
      color: rgba(255, 255, 255, 0.9);

      &:hover {
        background-color: rgba(40, 40, 40, 0.9);
        color: white;
      }
    }
  }
}

// تنسيق صفحة الإعدادات
.settings-page {
  padding: 20px;
  height: 100%;
  background-color: #000;

  .settings-options {
    margin-top: 20px;
  }
}

// تنسيق صفحة الدردشات الخاصة
.private-chats-container {
  padding: 10px;
  height: 100%;
  background-color: #000;

  .no-chats-message {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 30px;

    .no-chats-hint {
      font-size: 12px;
      margin-top: 10px;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .private-chat-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 5px;
    background-color: rgba(30, 30, 30, 0.8);
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(40, 40, 40, 0.9);
    }

    .private-chat-avatar {
      margin-right: 10px;
    }

    .private-chat-profile-avatar {
      width: 50px;
      height: 50px;
    }

    .private-chat-info {
      flex: 1;

      .private-chat-name {
        font-weight: bold;
        color: white;
        margin-bottom: 5px;
      }

      .private-chat-last-message {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: space-between;

        .private-chat-time {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
}

// تنسيق صفحة الدردشة الخاصة
.private-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .private-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;

    .no-messages-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: rgba(255, 255, 255, 0.5);

      .no-messages-text {
        font-size: 16px;
        margin-bottom: 10px;
      }

      .no-messages-hint {
        font-size: 12px;
      }
    }
  }

  .private-chat-input {
    padding: 10px;
    background-color: rgba(30, 30, 30, 0.7);
    border-top: 1px solid rgba(255, 255, 255, 0.05);

    .rs-input-group {
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 20px;
      overflow: hidden;

      .private-chat-textarea {
        background-color: transparent;
        border: none;
        color: white;
        resize: none;
        padding: 10px 15px;

        &:focus {
          outline: none;
        }
      }

      .private-chat-send-btn {
        background-color: rgba(76, 175, 80, 0.8);
        color: white;
        border: none;

        &:hover {
          background-color: rgba(76, 175, 80, 1);
        }

        &:disabled {
          background-color: rgba(76, 175, 80, 0.3);
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  .private-message {
    display: flex;
    margin-bottom: 10px;

    &.private-message-self {
      flex-direction: row-reverse;

      .private-message-content {
        background-color: rgba(76, 175, 80, 0.2);
        border-radius: 18px 4px 18px 18px;
        margin-left: 20px;
      }
    }

    &.private-message-other {
      .private-message-content {
        background-color: rgba(30, 30, 30, 0.8);
        border-radius: 4px 18px 18px 18px;
        margin-right: 20px;
      }
    }

    .private-message-avatar {
      margin-right: 10px;

      .private-message-profile-avatar {
        width: 35px;
        height: 35px;
      }
    }

    .private-message-content {
      padding: 10px 15px;
      max-width: 80%;

      .private-message-text {
        color: rgba(255, 255, 255, 0.9);
        word-wrap: break-word;
      }

      .private-message-time {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.5);
        text-align: right;
        margin-top: 5px;
      }
    }
  }
}

.private-chat-header {
  display: flex;
  align-items: center;

  .private-chat-header-avatar {
    width: 35px;
    height: 35px;
    margin-right: 10px;
  }

  .private-chat-header-info {
    .private-chat-header-name {
      font-weight: bold;
      color: white;
    }
  }
}

// تنسيق رأس الصفحة
.dark-header {
  background-color: #000;
  color: white;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .header-container {
    display: flex;
    align-items: center;
    padding: 0 15px;

    .back-btn {
      color: white;
      margin-right: 15px;
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: transparent;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }

    .header-title {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }
}

.dark-container {
  height: 100%;
  background-color: #000;
}

.dark-content {
  background-color: #000;
  height: calc(100% - 60px);
}

// تنسيق نافذة سجلات المجموعات المحذوفة
.deleted-messages-modal {
  .rs-modal-dialog {
    width: 100% !important;
    max-width: 100% !important;
    height: 100vh !important;
    margin: 0 !important;
  }

  .rs-modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
    background-color: #000 !important;
  }

  .rs-modal-header {
    background-color: #000 !important;
    color: white !important;
    border-bottom: 1px solid #333 !important;
  }

  .rs-modal-body {
    background-color: #000 !important;
    color: white !important;
    padding: 20px !important;
  }

  .rs-modal-footer {
    background-color: #000 !important;
    border-top: 1px solid #333 !important;
  }

  .messages-container {
    width: 100% !important;
    display: flex;
    flex-direction: column;
  }
}

// تنسيق عام لصور الملف الشخصي
.rs-avatar-image {
  border-radius: 50% !important;
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

// تنسيق معرف المستخدم في نافذة معلومات المستخدم
.user-id-container, .user-uid-container {
  margin-top: 10px;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  text-align: center;

  .uid-label {
    font-size: 12px;
    color: #aaa;
    display: inline-block;
    margin-left: 5px;
  }

  .uid-value {
    font-family: monospace;
    font-size: 14px;
    color: #fff;
    word-break: break-all;
    display: inline-block;
    margin-left: 5px;
  }
}

// تنسيق زر نسخ معرف المستخدم
.copy-uid-btn {
  margin-right: 5px;
  padding: 0 5px;
  background: transparent;
  color: #fff;
  border: none;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    color: #4caf50;
  }
}

// تنسيق علامة معرف المستخدم
.uid-tag {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// تنسيق زر تحميل الصورة الشخصية
.avatar-upload-btn {
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.4);
  }
}

// تنسيق زر تحميل الصورة الشخصية المعطل
.disabled-upload-btn {
  background-color: rgba(0, 0, 0, 0.1);
  color: #888;
  cursor: not-allowed;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

// تنسيق إنذار تغيير الاسم والصورة
.name-change-warning {
  margin-bottom: 15px;
  font-size: 12px;

  .warning-box {
    background-color: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 5px;
    padding: 10px;
    color: #ffc107;
  }
}

// تنسيق زر عجلة الحظ
.lucky-wheel-btn {
  background-color: #ffc107 !important;
  color: #000 !important;
  font-weight: bold;
  transition: all 0.3s ease;

  &:hover {
    background-color: #ffb300 !important;
    transform: scale(1.02);
  }

  i {
    margin-left: 5px;
    animation: spin 4s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// تنسيق حجم صورة الملف الشخصي
.width-5, .height-5 {
  width: 40px !important;
  height: 40px !important;
}

// تنسيق عداد المستخدمين
.room-users-count {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 15px;
  padding: 3px 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 10px;
  cursor: default;

  .user-icon {
    margin-right: 5px;
    font-size: 14px;
    opacity: 0.8;
  }

  .separator {
    margin: 0 5px;
    opacity: 0.7;
  }

  .active-users {
    color: #4caf50;
    font-weight: bold;
    display: flex;
    align-items: center;

    .online-badge {
      width: 8px;
      height: 8px;
      background-color: #4caf50;
      margin-right: 4px;
      display: inline-block;
    }
  }
}
