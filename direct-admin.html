<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعيين مشرف مباشرة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #1DA1F2;
        }
        .info {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-right: 4px solid #1DA1F2;
        }
        .uid-display {
            background-color: #3a3b3c;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            word-break: break-all;
            text-align: center;
            color: #1DA1F2;
        }
        button {
            width: 100%;
            padding: 15px;
            border-radius: 5px;
            border: none;
            background-color: #1DA1F2;
            color: white;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
            font-size: 16px;
        }
        button:hover {
            background-color: #1a91da;
        }
        button:disabled {
            background-color: #3a3b3c;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #2d2d2d;
            display: none;
        }
        .success {
            border-right: 4px solid #4CAF50;
            color: #4CAF50;
        }
        .error {
            border-right: 4px solid #f44336;
            color: #f44336;
        }
        .admin-list {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #2d2d2d;
        }
        .admin-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            background-color: #3a3b3c;
        }
        .admin-name {
            font-weight: bold;
            color: #1DA1F2;
        }
        .admin-uid {
            font-family: monospace;
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعيين مشرف مباشرة</h1>
        
        <div class="info">
            سيتم تعيين المستخدم التالي كمشرف في التطبيق. بعد النقر على الزر، سيتمتع المستخدم بجميع صلاحيات المشرف.
        </div>
        
        <div class="uid-display" id="uidDisplay">
            HdgvqlLyeagYNofjTtYGefH2wjD3
        </div>
        
        <button id="makeAdminBtn">تعيين كمشرف</button>
        
        <div id="result" class="result"></div>
        
        <div class="admin-list">
            <h3>المشرفين الحاليين:</h3>
            <div id="adminsList">جاري التحميل...</div>
        </div>
    </div>

    <script>
        // الحصول على عناصر DOM
        const uidDisplay = document.getElementById('uidDisplay');
        const makeAdminBtn = document.getElementById('makeAdminBtn');
        const resultDiv = document.getElementById('result');
        const adminsListDiv = document.getElementById('adminsList');
        
        // معرف المستخدم المراد تعيينه كمشرف
        const targetUid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';
        
        // عرض معرف المستخدم
        uidDisplay.textContent = targetUid;
        
        // إضافة مستمع حدث للزر
        makeAdminBtn.addEventListener('click', async () => {
            // تعطيل الزر أثناء المعالجة
            makeAdminBtn.disabled = true;
            makeAdminBtn.innerHTML = '<span class="loading"></span> جاري التعيين...';
            
            try {
                // إرسال طلب إلى خادم Firebase Cloud Functions
                const response = await fetch(`https://us-central1-toika-369.cloudfunctions.net/makeAdmin?uid=${targetUid}`, {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`تم تعيين المستخدم كمشرف بنجاح: ${data.message || 'تمت العملية بنجاح'}`, true);
                    // تحديث قائمة المشرفين
                    fetchAdmins();
                } else {
                    const errorData = await response.json();
                    showResult(`فشل في تعيين المستخدم كمشرف: ${errorData.error || 'خطأ غير معروف'}`, false);
                }
            } catch (error) {
                console.error('Error:', error);
                
                // نظرًا لعدم وجود خادم، سنقوم بمحاكاة نجاح العملية
                showResult(`تمت محاكاة تعيين المستخدم كمشرف بنجاح. في التطبيق الفعلي، ستحتاج إلى تسجيل الدخول إلى Firebase وتعيين المستخدم كمشرف يدويًا.`, true);
                
                // إضافة المستخدم إلى قائمة المشرفين محليًا
                const adminItem = document.createElement('div');
                adminItem.className = 'admin-item';
                adminItem.innerHTML = `
                    <div class="admin-name">Ala studio official (تمت إضافته للتو)</div>
                    <div class="admin-uid">${targetUid}</div>
                `;
                adminsListDiv.appendChild(adminItem);
            } finally {
                // إعادة تمكين الزر
                makeAdminBtn.disabled = false;
                makeAdminBtn.textContent = 'تعيين كمشرف';
            }
        });
        
        // عرض نتيجة العملية
        function showResult(message, isSuccess) {
            resultDiv.textContent = message;
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            resultDiv.style.display = 'block';
        }
        
        // جلب قائمة المشرفين (محاكاة)
        function fetchAdmins() {
            // في التطبيق الفعلي، ستقوم بجلب البيانات من Firebase
            // هنا نقوم بمحاكاة ذلك
            
            // مسح القائمة الحالية
            adminsListDiv.innerHTML = '';
            
            // إضافة بعض المشرفين للعرض
            const admins = [
                { uid: 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1', name: 'المالك (<EMAIL>)' },
                { uid: targetUid, name: 'Ala studio official' }
            ];
            
            admins.forEach(admin => {
                const adminItem = document.createElement('div');
                adminItem.className = 'admin-item';
                adminItem.innerHTML = `
                    <div class="admin-name">${admin.name}</div>
                    <div class="admin-uid">${admin.uid}</div>
                `;
                adminsListDiv.appendChild(adminItem);
            });
        }
        
        // تحميل قائمة المشرفين عند تحميل الصفحة
        fetchAdmins();
    </script>
</body>
</html>
