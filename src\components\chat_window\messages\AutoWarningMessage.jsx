import { useEffect, useState } from 'react';
import { ref, push, serverTimestamp, get, query, orderByChild, limitToLast } from 'firebase/database';
import { database } from '../../../misc/firebase.config';
import { useParams } from 'react-router';

// مكون لإرسال رسائل تحذير تلقائية في الدردشات الخاصة بين طرفين
const AutoWarningMessage = ({ isDirectMessage }) => {
  const { chatId } = useParams();
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  // رسالة التحذير
  const warningMessage = {
    text: `⚠️ تحذير هام ⚠️

انتبه! لا ترسل مال أو بيانات الحساب في هذه الدردشة.

هذه الدردشة خاصة بين طرفين وليست تحت سيطرة المشرفين.
أي احتيال أو نصب في هذا المكان نحن لسنا مسؤولين عنه.

هنا فقط البائع والمشتري يتوافقون على الشيء ويخبرون الأدمن أو المشرف ليقوم بإنشاء مجموعة خاصة تحت سيطرة الأدمن.`,
    author: {
      name: 'نظام التحذير التلقائي',
      uid: 'system',
      createdAt: Date.now(),
    },
    createdAt: serverTimestamp(),
    isSystemMessage: true,
    roomId: chatId,
  };

  // إرسال رسالة تحذير
  const sendWarningMessage = async () => {
    try {
      // حفظ الرسالة في قاعدة البيانات
      await push(ref(database, 'messages'), warningMessage);
      console.log('Warning message sent successfully');
    } catch (error) {
      console.error('Error sending warning message:', error);
    }
  };

  // إرسال رسالة تحذير عند فتح الدردشة لأول مرة فقط
  useEffect(() => {
    let isMounted = true;

    const checkAndSendWarning = async () => {
      if (isDirectMessage && isFirstLoad) {
        try {
          // البحث عن آخر رسالة تحذير في هذه الغرفة
          const messagesRef = ref(database, 'messages');
          const messagesQuery = query(
            messagesRef,
            orderByChild('roomId'),
            limitToLast(50) // زيادة عدد الرسائل للتأكد من العثور على رسائل التحذير
          );

          const snapshot = await get(messagesQuery);
          let hasWarning = false;

          if (snapshot.exists()) {
            const messages = snapshot.val();

            // البحث عن أي رسالة تحذير في هذه الغرفة
            Object.values(messages).forEach(msg => {
              if (msg.roomId === chatId && msg.author.uid === 'system' && msg.isSystemMessage) {
                hasWarning = true;
              }
            });
          }

          // إذا كان المكون لا يزال مثبتًا في DOM
          if (isMounted) {
            setIsFirstLoad(false);

            // إرسال رسالة تحذير فقط إذا لم يتم إرسال أي رسالة تحذير من قبل في هذه الغرفة
            if (!hasWarning) {
              console.log('Sending warning message - no previous warnings found');
              await sendWarningMessage();
            } else {
              console.log('Warning message already exists, not sending a new one');
            }
          }
        } catch (error) {
          console.error('Error in checkAndSendWarning:', error);
        }
      }
    };

    checkAndSendWarning();

    // تنظيف عند إلغاء تحميل المكون
    return () => {
      isMounted = false;
    };
  }, [isDirectMessage, chatId, isFirstLoad, sendWarningMessage]);

  // تم إزالة المؤقت لإرسال رسائل تحذير متكررة
  // الآن سيتم إرسال رسالة تحذير واحدة فقط عند فتح الدردشة لأول مرة

  // هذا المكون لا يعرض أي شيء، فقط يرسل رسائل تحذير تلقائية
  return null;
};

export default AutoWarningMessage;
