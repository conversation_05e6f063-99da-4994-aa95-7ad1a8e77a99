import React, { useEffect, useState, useRef } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { Container, Content, Header, Button, Input, InputGroup, Loader, Avatar, Message, toaster } from 'rsuite';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';
import SendIcon from '@rsuite/icons/Send';
import { database, auth, storage } from '../misc/firebase.config';
import { ref, onValue, push, set, get, serverTimestamp, update } from 'firebase/database';
import { ref as storageRef, uploadBytes, getDownloadURL } from 'firebase/storage';
import ProfileAvatar from '../components/ProfileAvatar';
import TimeAgo from 'timeago-react';
import { useProfile } from '../context/profile.context';

const PrivateChat = () => {
  const { userId } = useParams();
  const history = useHistory();
  const { profile } = useProfile();
  const [otherUser, setOtherUser] = useState(null);
  const [messages, setMessages] = useState([]);
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [chatId, setChatId] = useState(null);
  const messagesEndRef = useRef(null);

  // التمرير إلى آخر الرسائل
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    let messagesUnsubscribe = null;

    // جلب بيانات المستخدم الآخر
    const fetchOtherUser = async () => {
      try {
        const userRef = ref(database, `users/${userId}`);
        const snapshot = await get(userRef);

        if (snapshot.exists()) {
          setOtherUser({
            ...snapshot.val(),
            uid: userId
          });
          return true;
        } else {
          console.error('User not found');
          history.push('/private-chats');
          return false;
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        setIsLoading(false);
        return false;
      }
    };

    // البحث عن معرف الدردشة الخاصة أو إنشاء واحدة جديدة
    const findOrCreateChat = async () => {
      try {
        const currentUserId = auth.currentUser.uid;

        // البحث عن دردشة موجودة
        const privateChatsRef = ref(database, 'private-chats');
        const snapshot = await get(privateChatsRef);

        if (snapshot.exists()) {
          const chatsData = snapshot.val();
          let existingChatId = null;

          // البحث عن دردشة تحتوي على كلا المستخدمين
          for (const chatId in chatsData) {
            const chat = chatsData[chatId];
            if (
              chat.members &&
              chat.members[currentUserId] &&
              chat.members[userId]
            ) {
              existingChatId = chatId;
              break;
            }
          }

          if (existingChatId) {
            setChatId(existingChatId);
            return existingChatId;
          }
        }

        // إنشاء دردشة جديدة إذا لم يتم العثور على واحدة
        const chatId = push(ref(database, 'private-chats')).key;

        const members = {};
        members[currentUserId] = true;
        members[userId] = true;

        // إنشاء التحديثات
        const updates = {};

        // إضافة الدردشة الجديدة
        updates[`/private-chats/${chatId}`] = {
          createdAt: Date.now(),
          createdBy: currentUserId,
          members
        };

        // تنفيذ التحديثات
        await update(ref(database), updates);

        setChatId(chatId);
        return chatId;
      } catch (error) {
        console.error('Error finding or creating chat:', error);
        setIsLoading(false);
        return null;
      }
    };

    // جلب الرسائل
    const fetchMessages = async (chatId) => {
      if (!chatId) {
        setIsLoading(false);
        return null;
      }

      try {
        // استخدام استعلام للحصول على الرسائل الخاصة بهذه الدردشة
        const messagesRef = ref(database, 'private-messages');

        const unsubscribe = onValue(messagesRef, snapshot => {
          try {
            if (snapshot.exists()) {
              const messagesData = snapshot.val();
              const messagesArray = [];

              // تصفية الرسائل للحصول على الرسائل الخاصة بهذه الدردشة فقط
              for (const messageId in messagesData) {
                const message = messagesData[messageId];
                if (message.chatId === chatId) {
                  messagesArray.push({
                    id: messageId,
                    ...message
                  });
                }
              }

              // ترتيب الرسائل حسب وقت الإنشاء
              messagesArray.sort((a, b) => {
                // التعامل مع قيم التاريخ التي قد تكون null
                const timeA = a.createdAt || 0;
                const timeB = b.createdAt || 0;
                return timeA - timeB;
              });

              setMessages(messagesArray);
            } else {
              setMessages([]);
            }

            setIsLoading(false);
            scrollToBottom();
          } catch (error) {
            console.error('Error processing messages:', error);
            setIsLoading(false);
          }
        }, error => {
          console.error('Error fetching messages:', error);
          setIsLoading(false);
        });

        return unsubscribe;
      } catch (error) {
        console.error('Error setting up messages listener:', error);
        setIsLoading(false);
        return null;
      }
    };

    const initChat = async () => {
      try {
        const userExists = await fetchOtherUser();
        if (!userExists) return;

        const chatId = await findOrCreateChat();
        if (!chatId) return;

        messagesUnsubscribe = await fetchMessages(chatId);
      } catch (error) {
        console.error('Error initializing chat:', error);
        setIsLoading(false);
      }
    };

    initChat();

    // تنظيف الاشتراكات عند إلغاء تحميل المكون
    return () => {
      if (messagesUnsubscribe) {
        messagesUnsubscribe();
      }
    };
  }, [userId, history]);

  // التمرير إلى أسفل عند إضافة رسائل جديدة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleBack = () => {
    // التحقق من وجود صفحة سابقة في تاريخ التصفح
    if (window.history.length > 1) {
      // إذا كان هناك صفحة سابقة، استخدم history.goBack()
      history.goBack();
    } else {
      // إذا لم يكن هناك صفحة سابقة، انتقل إلى صفحة الدردشات الخاصة
      history.push('/private-chats');
    }
  };

  const handleSend = async () => {
    if (!content.trim() || !chatId) return;

    const currentUserId = auth.currentUser.uid;

    // إنشاء بيانات الرسالة
    const messageData = {
      content,
      createdAt: Date.now(),
      author: {
        uid: currentUserId,
        name: profile.name,
        avatar: profile.avatar || ''
      }
    };

    try {
      // إنشاء مرجع للرسالة الجديدة
      const messageId = push(ref(database, "private-messages")).key;

      // إنشاء التحديثات
      const updates = {};

      // إضافة الرسالة إلى قاعدة البيانات
      updates[`/private-messages/${messageId}`] = {
        ...messageData,
        chatId
      };

      // تحديث آخر رسالة في الدردشة
      updates[`/private-chats/${chatId}/lastMessage`] = {
        ...messageData,
        messageId
      };

      // تنفيذ التحديثات
      await update(ref(database), updates);

      // مسح محتوى الإدخال
      setContent('');

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={2000}>
          تم إرسال الرسالة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error sending message:', error);

      // عرض رسالة خطأ
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إرسال الرسالة: {error.message}
        </Message>
      );
    }
  };

  const handleKeyDown = event => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  return (
    <Container className="dark-container">
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          {otherUser ? (
            <div className="private-chat-header">
              <ProfileAvatar
                src={otherUser.avatar}
                name={otherUser.name}
                className="private-chat-header-avatar"
              />
              <div className="private-chat-header-info">
                <div className="private-chat-header-name">{otherUser.name}</div>
              </div>
            </div>
          ) : (
            <h4 className="header-title">جاري التحميل...</h4>
          )}
        </div>
      </Header>
      <Content className="dark-content private-chat-content">
        {isLoading ? (
          <div className="loader-container">
            <Loader center content="جاري تحميل الرسائل..." />
          </div>
        ) : (
          <>
            <div className="private-chat-messages">
              {messages.length === 0 ? (
                <div className="no-messages-container">
                  <p className="no-messages-text">لا توجد رسائل</p>
                  <p className="no-messages-hint">ابدأ محادثة جديدة الآن</p>
                </div>
              ) : (
                messages.map(message => {
                  const isCurrentUser = message.author.uid === auth.currentUser.uid;

                  return (
                    <div
                      key={message.id}
                      className={`private-message ${isCurrentUser ? 'private-message-self' : 'private-message-other'}`}
                    >
                      {!isCurrentUser && (
                        <div className="private-message-avatar">
                          <ProfileAvatar
                            src={message.author.avatar}
                            name={message.author.name}
                            className="private-message-profile-avatar"
                          />
                        </div>
                      )}
                      <div className="private-message-content">
                        <div className="private-message-text">{message.content}</div>
                        <div className="private-message-time">
                          {message.createdAt && (
                            <TimeAgo datetime={new Date(message.createdAt)} locale="ar" />
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
              <div ref={messagesEndRef} />
            </div>
            <div className="private-chat-input">
              <InputGroup>
                <Input
                  as="textarea"
                  rows={1}
                  placeholder="اكتب رسالة..."
                  value={content}
                  onChange={value => setContent(value)}
                  onKeyDown={handleKeyDown}
                  className="private-chat-textarea"
                />
                <InputGroup.Button
                  onClick={handleSend}
                  disabled={!content.trim()}
                  className="private-chat-send-btn"
                >
                  <SendIcon />
                </InputGroup.Button>
              </InputGroup>
            </div>
          </>
        )}
      </Content>
    </Container>
  );
};

export default PrivateChat;
