<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>تعيين مشرف تلقائي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #1DA1F2;
            margin-bottom: 20px;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        .log {
            background-color: #3a3b3c;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
            text-align: left;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #1DA1F2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعيين مشرف تلقائي</h1>
        <div class="status" id="status">
            جاري تعيين المستخدم كمشرف...
        </div>
        <div class="log" id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "300804286264",
            appId: "1:300804286264:web:29304874a53644e25403cd",
            measurementId: "G-W46F5TY408"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');

        // إضافة رسالة إلى السجل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // تعيين المستخدم كمشرف
        async function makeAdmin() {
            const uid = 'HdgvqlLyeagYNofjTtYGefH2wjD3';
            
            try {
                log(`بدء العملية: تعيين المستخدم ${uid} كمشرف...`);
                
                // تسجيل الدخول كمستخدم مجهول
                log('تسجيل الدخول كمستخدم مجهول...');
                await firebase.auth().signInAnonymously();
                log('تم تسجيل الدخول بنجاح', 'success');
                
                // التحقق من وجود المستخدم
                log(`التحقق من وجود المستخدم ${uid}...`);
                const userRef = firebase.database().ref(`users/${uid}`);
                const snapshot = await userRef.once('value');
                
                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    log(`تم العثور على المستخدم: ${userData.name || uid}`, 'success');
                    
                    // التحقق من حالة المشرف الحالية
                    const isAdmin = userData.isAdmin === true;
                    log(`حالة المشرف الحالية: ${isAdmin ? 'مشرف' : 'مستخدم عادي'}`);
                    
                    if (isAdmin) {
                        log('المستخدم مشرف بالفعل', 'info');
                        statusElement.innerHTML = `<div class="success">المستخدم ${userData.name || uid} مشرف بالفعل</div>`;
                    } else {
                        // تعيين المستخدم كمشرف
                        log('تعيين المستخدم كمشرف...');
                        await userRef.update({ isAdmin: true });
                        
                        // التحقق من نجاح العملية
                        const updatedSnapshot = await userRef.once('value');
                        const updatedUserData = updatedSnapshot.val();
                        const updatedIsAdmin = updatedUserData.isAdmin === true;
                        
                        if (updatedIsAdmin) {
                            log('تم تعيين المستخدم كمشرف بنجاح', 'success');
                            statusElement.innerHTML = `<div class="success">تم تعيين المستخدم ${userData.name || uid} كمشرف بنجاح</div>`;
                        } else {
                            log('فشل في تعيين المستخدم كمشرف', 'error');
                            statusElement.innerHTML = `<div class="error">فشل في تعيين المستخدم ${userData.name || uid} كمشرف</div>`;
                        }
                    }
                } else {
                    log(`لم يتم العثور على المستخدم ${uid}`, 'error');
                    statusElement.innerHTML = `<div class="error">لم يتم العثور على المستخدم ${uid}</div>`;
                }
            } catch (error) {
                log(`خطأ: ${error.message}`, 'error');
                statusElement.innerHTML = `<div class="error">خطأ: ${error.message}</div>`;
            }
        }
        
        // تنفيذ العملية عند تحميل الصفحة
        window.onload = makeAdmin;
    </script>
</body>
</html>
