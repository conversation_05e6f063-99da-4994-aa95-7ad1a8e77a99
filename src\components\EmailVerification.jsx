import React from 'react';
import TranslatedText from './TranslatedText';

const EmailVerification = () => {
  return (
    <div className="email-verification-container">
      <div className="email-verification-box">
        <div className="verify-box">
          <div className="checkmark">✓</div>
          <div className="verify-text">
            <TranslatedText text="verified" />
          </div>
        </div>
        <div className="verification-message">
          <h3><TranslatedText text="accountCreatedSuccessfully" /></h3>
          <p><TranslatedText text="pleaseCheckInbox" /></p>
        </div>
      </div>

      {/* إضافة التنسيق المطلوب */}
      <style jsx>{`
        .email-verification-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          background-color: #000;
          color: white;
          padding: 20px;
        }

        .email-verification-box {
          background-color: #111;
          border-radius: 10px;
          padding: 40px;
          text-align: center;
          max-width: 500px;
          width: 100%;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
        }

        .verify-box {
          margin-bottom: 30px;
        }

        .checkmark {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 100px;
          background-color: rgba(76, 175, 80, 0.2);
          border: 3px solid #4caf50;
          border-radius: 50%;
          margin: 0 auto 20px;
          font-size: 60px;
          color: #4caf50;
          animation: pulse 2s infinite;
        }

        .verify-text {
          font-size: 24px;
          font-weight: bold;
          color: #4caf50;
          margin-bottom: 10px;
        }

        .verification-message {
          color: #ccc;
          line-height: 1.6;
        }

        .verification-message h3 {
          color: white;
          margin-bottom: 15px;
          font-size: 22px;
        }

        .verification-message p {
          color: #ccc;
          font-size: 16px;
        }

        @keyframes pulse {
          0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
          }

          70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
          }

          100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
          }
        }

        @media (max-width: 768px) {
          .email-verification-box {
            padding: 30px 20px;
          }

          .checkmark {
            width: 80px;
            height: 80px;
            font-size: 50px;
          }
        }
      `}</style>
    </div>
  );
};

export default EmailVerification;
