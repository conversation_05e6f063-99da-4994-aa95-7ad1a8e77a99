import React, { useState, useEffect } from 'react';
import { Button, Input, Modal, Message, toaster, Loader, Panel, InputGroup } from 'rsuite';
import { useHistory } from 'react-router';
import { database, auth } from '../../misc/firebase.config';
import { ref, get, query, orderByChild, equalTo, update } from 'firebase/database';
import { useProfile } from '../../context/profile.context';
import EnhancedBottomNavbar from '../../components/EnhancedBottomNavbar';
import { FaCheckCircle, FaTimesCircle, FaCheck } from 'react-icons/fa';
import './reward-code-checker.scss';

const RewardCodeChecker = () => {
  const history = useHistory();
  const { profile } = useProfile();
  const [loading, setLoading] = useState(false);
  const [searchCode, setSearchCode] = useState('');
  const [searchResult, setSearchResult] = useState(null);
  const [allRewards, setAllRewards] = useState([]);
  const [showAllRewards, setShowAllRewards] = useState(false);

  // التحقق من أن المستخدم الحالي هو المالك أو مشرف
  const isOwner = auth.currentUser && (
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.email === '<EMAIL>'
  );
  const isAdmin = profile && profile.isAdmin;

  // التحقق من الصلاحيات
  useEffect(() => {
    if (!isAdmin && !isOwner) {
      history.push('/');
      toaster.push(
        <Message type="error" closable duration={4000}>
          ليس لديك صلاحية للوصول إلى هذه الصفحة
        </Message>
      );
    }
  }, [isAdmin, isOwner, history]);

  // دالة للعودة إلى الصفحة السابقة
  const handleBack = () => {
    history.push('/');
  };

  // دالة للبحث عن كود الجائزة
  const searchRewardCode = async () => {
    if (!searchCode.trim()) {
      toaster.push(
        <Message type="warning" closable duration={4000}>
          الرجاء إدخال كود الجائزة
        </Message>
      );
      return;
    }

    setLoading(true);
    setSearchResult(null);

    try {
      // البحث في جميع المستخدمين
      const usersRef = ref(database, '/lucky-wheel/users');
      const usersSnapshot = await get(usersRef);

      if (usersSnapshot.exists()) {
        const users = usersSnapshot.val();
        let foundReward = null;
        let foundUser = null;
        let foundRewardId = null;

        // البحث عن الكود في جوائز كل مستخدم
        for (const userId in users) {
          if (users[userId].rewards) {
            for (const rewardId in users[userId].rewards) {
              const reward = users[userId].rewards[rewardId];
              if (reward.code === searchCode) {
                foundReward = reward;
                foundUser = userId;
                foundRewardId = rewardId;
                break;
              }
            }
          }
          if (foundReward) break;
        }

        if (foundReward && foundUser) {
          // الحصول على معلومات المستخدم
          const userProfileRef = ref(database, `/profile/${foundUser}`);
          const userProfileSnapshot = await get(userProfileRef);
          const userProfile = userProfileSnapshot.exists() ? userProfileSnapshot.val() : { name: 'مستخدم غير معروف' };

          setSearchResult({
            ...foundReward,
            userId: foundUser,
            rewardId: foundRewardId,
            userName: userProfile.name || 'مستخدم غير معروف',
            userAvatar: userProfile.avatar || null
          });

          toaster.push(
            <Message type="success" closable duration={4000}>
              تم العثور على الكود
            </Message>
          );
        } else {
          toaster.push(
            <Message type="error" closable duration={4000}>
              لم يتم العثور على الكود
            </Message>
          );
        }
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لا توجد بيانات للمستخدمين
          </Message>
        );
      }
    } catch (error) {
      console.error('Error searching for reward code:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء البحث عن الكود
        </Message>
      );
    } finally {
      setLoading(false);
    }
  };

  // دالة لتحميل جميع الجوائز
  const loadAllRewards = async () => {
    setLoading(true);
    setAllRewards([]);

    try {
      const usersRef = ref(database, '/lucky-wheel/users');
      const usersSnapshot = await get(usersRef);

      if (usersSnapshot.exists()) {
        const users = usersSnapshot.val();
        const rewardsList = [];

        // جمع جميع الجوائز من جميع المستخدمين
        for (const userId in users) {
          if (users[userId].rewards) {
            for (const rewardId in users[userId].rewards) {
              const reward = users[userId].rewards[rewardId];

              // الحصول على معلومات المستخدم
              const userProfileRef = ref(database, `/profile/${userId}`);
              const userProfileSnapshot = await get(userProfileRef);
              const userProfile = userProfileSnapshot.exists() ? userProfileSnapshot.val() : { name: 'مستخدم غير معروف' };

              rewardsList.push({
                ...reward,
                rewardId,
                userId,
                userName: userProfile.name || 'مستخدم غير معروف',
                userAvatar: userProfile.avatar || null
              });
            }
          }
        }

        // ترتيب الجوائز حسب التاريخ (الأحدث أولاً)
        rewardsList.sort((a, b) => {
          if (!a.timestamp || !b.timestamp) return 0;
          return b.timestamp - a.timestamp;
        });

        setAllRewards(rewardsList);
        setShowAllRewards(true);
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            لا توجد بيانات للمستخدمين
          </Message>
        );
      }
    } catch (error) {
      console.error('Error loading all rewards:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تحميل الجوائز
        </Message>
      );
    } finally {
      setLoading(false);
    }
  };

  // دالة لتأكيد استلام المكافأة
  const confirmRewardClaimed = async (userId, rewardId) => {
    try {
      setLoading(true);

      // تحديث حالة الجائزة في قاعدة البيانات
      const rewardRef = ref(database, `/lucky-wheel/users/${userId}/rewards/${rewardId}`);
      await update(rewardRef, {
        claimed: true,
        claimedAt: Date.now(),
        claimedBy: auth.currentUser.uid
      });

      // تحديث النتيجة المحلية
      setSearchResult(prev => ({
        ...prev,
        claimed: true,
        claimedAt: Date.now(),
        claimedBy: auth.currentUser.uid
      }));

      // تحديث قائمة جميع الجوائز إذا كانت مفتوحة
      if (showAllRewards) {
        setAllRewards(prev =>
          prev.map(reward =>
            reward.userId === userId && reward.rewardId === rewardId
              ? { ...reward, claimed: true, claimedAt: Date.now(), claimedBy: auth.currentUser.uid }
              : reward
          )
        );
      }

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تأكيد استلام المكافأة بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Error confirming reward claimed:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          حدث خطأ أثناء تأكيد استلام المكافأة
        </Message>
      );
    } finally {
      setLoading(false);
    }
  };

  // تنسيق التاريخ
  const formatDate = (timestamp) => {
    if (!timestamp) return 'غير معروف';
    const date = new Date(timestamp);
    return date.toLocaleString('ar-SA');
  };

  if (loading) {
    return (
      <div className="reward-code-checker-page">
        <Loader center vertical size="md" content="جاري التحميل..." speed="slow" />
      </div>
    );
  }

  return (
    <div className="reward-code-checker-page">
      <div className="checker-header">
        <Button appearance="subtle" onClick={handleBack} className="back-button">
          <i className="fas fa-arrow-right"></i> العودة
        </Button>
        <h2>فحص أكواد الجوائز</h2>
      </div>

      <div className="checker-content">
        <div className="search-section">
          <InputGroup>
            <Input
              placeholder="أدخل كود الجائزة"
              value={searchCode}
              onChange={value => setSearchCode(value)}
              onPressEnter={searchRewardCode}
            />
            <InputGroup.Button onClick={searchRewardCode}>
              <i className="fas fa-search"></i> بحث
            </InputGroup.Button>
          </InputGroup>

          <Button
            appearance="primary"
            color="blue"
            block
            className="mt-3"
            onClick={loadAllRewards}
          >
            عرض جميع الجوائز
          </Button>
        </div>

        {searchResult && (
          <div className="result-section">
            <Panel header="نتيجة البحث" bordered>
              <div className="reward-info">
                <div className="reward-code">
                  <strong>الكود:</strong> {searchResult.code}
                </div>
                <div className="reward-type">
                  <strong>نوع الجائزة:</strong> {searchResult.rewardText}
                </div>
                <div className="reward-user">
                  <strong>المستخدم:</strong> {searchResult.userName}
                </div>
                <div className="reward-date">
                  <strong>تاريخ الربح:</strong> {formatDate(searchResult.timestamp)}
                </div>
                <div className="reward-status">
                  <strong>الحالة:</strong> {searchResult.claimed ? (
                    <span className="claimed-status">
                      <FaCheckCircle style={{ color: 'green', marginRight: '5px' }} /> تم استلامها
                    </span>
                  ) : (
                    <span className="unclaimed-status">
                      <FaTimesCircle style={{ color: 'red', marginRight: '5px' }} /> لم يتم استلامها
                    </span>
                  )}
                </div>

                {!searchResult.claimed && (
                  <div className="reward-actions">
                    <Button
                      appearance="primary"
                      color="green"
                      block
                      onClick={() => confirmRewardClaimed(searchResult.userId, searchResult.rewardId)}
                    >
                      <FaCheck style={{ marginRight: '5px' }} /> تأكيد استلام المكافأة
                    </Button>
                  </div>
                )}
              </div>
            </Panel>
          </div>
        )}

        <Modal full open={showAllRewards} onClose={() => setShowAllRewards(false)}>
          <Modal.Header>
            <Modal.Title>جميع الجوائز</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {allRewards.length > 0 ? (
              <div className="all-rewards-list">
                {allRewards.map((reward, index) => (
                  <Panel key={index} header={`${reward.rewardText} - ${reward.userName}`} bordered collapsible>
                    <div className="reward-info">
                      <div className="reward-code">
                        <strong>الكود:</strong> {reward.code}
                      </div>
                      <div className="reward-type">
                        <strong>نوع الجائزة:</strong> {reward.rewardText}
                      </div>
                      <div className="reward-user">
                        <strong>المستخدم:</strong> {reward.userName}
                      </div>
                      <div className="reward-date">
                        <strong>تاريخ الربح:</strong> {formatDate(reward.timestamp)}
                      </div>
                      <div className="reward-status">
                        <strong>الحالة:</strong> {reward.claimed ? (
                          <span className="claimed-status">
                            <FaCheckCircle style={{ color: 'green', marginRight: '5px' }} /> تم استلامها
                          </span>
                        ) : (
                          <span className="unclaimed-status">
                            <FaTimesCircle style={{ color: 'red', marginRight: '5px' }} /> لم يتم استلامها
                          </span>
                        )}
                      </div>

                      {!reward.claimed && (
                        <div className="reward-actions">
                          <Button
                            appearance="primary"
                            color="green"
                            block
                            onClick={() => confirmRewardClaimed(reward.userId, reward.rewardId)}
                          >
                            <FaCheck style={{ marginRight: '5px' }} /> تأكيد استلام المكافأة
                          </Button>
                        </div>
                      )}
                    </div>
                  </Panel>
                ))}
              </div>
            ) : (
              <div className="no-rewards">
                لا توجد جوائز
              </div>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button onClick={() => setShowAllRewards(false)} appearance="primary">
              إغلاق
            </Button>
          </Modal.Footer>
        </Modal>
      </div>

      <EnhancedBottomNavbar />
    </div>
  );
};

export default RewardCodeChecker;
