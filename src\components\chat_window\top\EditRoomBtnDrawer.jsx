import { ref, set, get, remove } from "firebase/database";
import React, { memo, useState } from "react";
import { useParams, useHistory } from "react-router";
import { Button, Drawer, Message, toaster, Toggle, Form, Input, Modal } from "rsuite";
import { useCurrentRoom } from "../../../context/current-room.context";
import { useMediaQuery, useModalState } from "../../../misc/custom-hooks";
import { database, auth } from "../../../misc/firebase.config";
import EditableInput from "../../EditableInput";

// دالة للتحقق من صحة البريد الإلكتروني
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// دالة للبحث عن مستخدم بواسطة البريد الإلكتروني
const findUserByEmail = async (email) => {
  try {
    console.log('Searching for user with email:', email);
    const usersRef = ref(database, 'users');
    const snapshot = await get(usersRef);

    if (snapshot.exists()) {
      const usersData = snapshot.val();
      const users = Object.entries(usersData);
      console.log('Total users found:', users.length);

      for (const [uid, userData] of users) {
        console.log('Checking user:', uid, userData);
        // تحقق من وجود البريد الإلكتروني في بيانات المستخدم
        if (userData && userData.email) {
          console.log('Comparing emails:', userData.email.toLowerCase(), 'vs', email.toLowerCase());
          if (userData.email.toLowerCase() === email.toLowerCase()) {
            console.log('User found with UID:', uid);
            return uid;
          }
        }
      }
    } else {
      console.log('No users found in database');
    }

    console.log('User not found with email:', email);
    return null;
  } catch (error) {
    console.error('Error finding user by email:', error);
    return null;
  }
};

const EditRoomBtnDrawer = () => {
  const { chatId } = useParams();
  const history = useHistory();
  const { isOpen, open, close } = useModalState();
  const { isOpen: isDeleteConfirmOpen, open: openDeleteConfirm, close: closeDeleteConfirm } = useModalState();
  const isMobile = useMediaQuery("(max-width: 992px)");

  const name = useCurrentRoom((v) => v.name);
  const description = useCurrentRoom((v) => v.description);
  const isAdmin = useCurrentRoom((v) => v.admins && v.admins[auth.currentUser.uid]);

  const [isPrivate, setIsPrivate] = useState(false);
  const [allowedUser1, setAllowedUser1] = useState("");
  const [allowedUser2, setAllowedUser2] = useState("");
  const [allowedUser3, setAllowedUser3] = useState("");
  const [allowedUser4, setAllowedUser4] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // جلب معلومات الغرفة عند فتح النموذج
  const handleOpen = async () => {
    open();
    setIsLoading(true);

    try {
      // جلب معلومات الغرفة
      const roomRef = ref(database, `/rooms/${chatId}`);
      const roomSnapshot = await get(roomRef);

      if (roomSnapshot.exists()) {
        const roomData = roomSnapshot.val();
        setIsPrivate(roomData.isPrivate || false);

        // تحويل قائمة المستخدمين المسموح لهم إلى مصفوفة
        if (roomData.allowedUsers) {
          const allowedUsersArray = Object.keys(roomData.allowedUsers)
            .filter(uid => uid !== auth.currentUser.uid); // استبعاد المستخدم الحالي

          // تعيين المستخدمين المسموح لهم في الحقول المنفصلة
          if (allowedUsersArray.length > 0) setAllowedUser1(allowedUsersArray[0]);
          if (allowedUsersArray.length > 1) setAllowedUser2(allowedUsersArray[1]);
          if (allowedUsersArray.length > 2) setAllowedUser3(allowedUsersArray[2]);
          if (allowedUsersArray.length > 3) setAllowedUser4(allowedUsersArray[3]);
        } else {
          setAllowedUser1("");
          setAllowedUser2("");
          setAllowedUser3("");
          setAllowedUser4("");
        }
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading room data:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في تحميل بيانات الغرفة: {error.message}
        </Message>
      );
      setIsLoading(false);
    }
  };

  const updateData = (key, value) => {
    set(ref(database, `/rooms/${chatId}/${key}`), value)
      .then(() => {
        toaster.push(
          <Message type="success" closable duration={4000}>
            Successfully updated
          </Message>
        );
      })
      .catch((err) => {
        toaster.push(
          <Message type="error" closable duration={4000}>
            {err.message}
          </Message>
        );
      });
  };

  const onNameSave = (newName) => {
    updateData("name", newName);
  };

  const onDescriptionSave = (newDesc) => {
    updateData("description", newDesc);
  };

  // تحديث خصوصية الغرفة
  const onPrivacyToggle = async (checked) => {
    setIsPrivate(checked);

    try {
      await updateData("isPrivate", checked);

      // إذا كانت الغرفة خاصة، أضف المستخدم الحالي إلى قائمة المستخدمين المسموح لهم
      if (checked) {
        const allowedUsersObj = {};
        allowedUsersObj[auth.currentUser.uid] = true;

        // إضافة المستخدمين المحددين من الحقول المنفصلة
        const userInputs = [allowedUser1, allowedUser2, allowedUser3, allowedUser4];
        for (const userInput of userInputs) {
          if (userInput && userInput.trim() !== "") {
            allowedUsersObj[userInput] = true;
          }
        }

        await updateData("allowedUsers", allowedUsersObj);
      } else {
        // إذا كانت الغرفة عامة، احذف قائمة المستخدمين المسموح لهم
        await set(ref(database, `/rooms/${chatId}/allowedUsers`), null);
      }
    } catch (error) {
      console.error('Error updating privacy:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في تحديث خصوصية الغرفة: {error.message}
        </Message>
      );
    }
  };

  // التحقق من صحة معرف المستخدم أو البريد الإلكتروني
  const validateUser = async (value) => {
    if (!value || value.trim() === "") {
      return null;
    }

    setIsLoading(true);
    console.log('Validating user input:', value);

    try {
      let uid = value.trim();

      // التحقق من وجود المستخدم في قاعدة البيانات باستخدام UID أولاً
      console.log('Checking if user exists with UID:', uid);
      const userRef = ref(database, `users/${uid}`);
      const userSnapshot = await get(userRef);

      if (userSnapshot.exists()) {
        console.log('User exists with UID:', uid);
        // عرض معلومات المستخدم
        const userData = userSnapshot.val();
        console.log('User data:', userData);

        // عرض رسالة نجاح
        toaster.push(
          <Message type="success" closable duration={4000}>
            تم العثور على المستخدم وإضافته بنجاح: {userData.name || 'مستخدم'}
          </Message>
        );

        setIsLoading(false);
        return uid;
      }
      // إذا كان البريد الإلكتروني، ابحث عن معرف المستخدم المرتبط به
      else if (isValidEmail(uid)) {
        console.log('Input is an email, searching for user...');
        const foundUid = await findUserByEmail(uid);

        if (foundUid) {
          console.log('User found with email, using UID:', foundUid);

          // عرض رسالة نجاح
          toaster.push(
            <Message type="success" closable duration={4000}>
              تم العثور على المستخدم وإضافته بنجاح
            </Message>
          );

          setIsLoading(false);
          return foundUid;
        } else {
          console.log('No user found with email:', uid);
          toaster.push(
            <Message type="error" closable duration={4000}>
              لم يتم العثور على مستخدم بهذا البريد الإلكتروني: {uid}
            </Message>
          );
          setIsLoading(false);
          return null;
        }
      }
      // إذا لم يكن UID صالح ولا بريد إلكتروني
      else {
        console.log('Invalid input, not a valid UID or email:', uid);
        toaster.push(
          <Message type="error" closable duration={4000}>
            لم يتم العثور على مستخدم بهذا المعرف: {uid}
          </Message>
        );
        setIsLoading(false);
        return null;
      }
    } catch (error) {
      console.error('Error validating user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في التحقق من المستخدم: {error.message}
        </Message>
      );
      setIsLoading(false);
      return null;
    }
  };

  // تحديث معرف المستخدم المسموح له 1
  const onAllowedUser1Change = (value) => {
    setAllowedUser1(value);
    updateAllowedUsers();
  };

  // تحديث معرف المستخدم المسموح له 2
  const onAllowedUser2Change = (value) => {
    setAllowedUser2(value);
    updateAllowedUsers();
  };

  // تحديث معرف المستخدم المسموح له 3
  const onAllowedUser3Change = (value) => {
    setAllowedUser3(value);
    updateAllowedUsers();
  };

  // تحديث معرف المستخدم المسموح له 4
  const onAllowedUser4Change = (value) => {
    setAllowedUser4(value);
    updateAllowedUsers();
  };

  // معالجة حذف المجموعة
  const handleDeleteRoom = async (isSuccessful) => {
    try {
      setIsLoading(true);

      // الحصول على بيانات الغرفة قبل حذفها
      const roomRef = ref(database, `/rooms/${chatId}`);
      const roomSnapshot = await get(roomRef);

      if (roomSnapshot.exists()) {
        const roomData = roomSnapshot.val();

        // إضافة الغرفة إلى سجل الغرف المحذوفة
        const deletedRoomData = {
          ...roomData,
          deletedAt: new Date().toISOString(),
          deletedBy: auth.currentUser.uid,
          saleSuccessful: isSuccessful
        };

        // حفظ بيانات الغرفة في سجل الغرف المحذوفة
        await set(ref(database, `/deleted-rooms/${chatId}`), deletedRoomData);

        // حذف الغرفة من قائمة الغرف النشطة
        await remove(roomRef);

        // إغلاق نافذة التأكيد ونافذة التحرير
        closeDeleteConfirm();
        close();

        // عرض رسالة نجاح
        toaster.push(
          <Message type="success" closable duration={4000}>
            تم حذف المجموعة بنجاح {isSuccessful ? '✅' : '❌'}
          </Message>
        );

        // الانتقال إلى الصفحة الرئيسية
        history.push('/');
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error deleting room:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في حذف المجموعة: {error.message}
        </Message>
      );
      setIsLoading(false);
    }
  };

  // تحديث قائمة المستخدمين المسموح لهم في قاعدة البيانات
  const updateAllowedUsers = async () => {
    if (isPrivate) {
      try {
        const allowedUsersObj = {};

        // إضافة المستخدم الحالي (مشرف الغرفة) إلى قائمة المستخدمين المسموح لهم
        allowedUsersObj[auth.currentUser.uid] = true;

        // إضافة المستخدمين المحددين من الحقول المنفصلة
        const userInputs = [allowedUser1, allowedUser2, allowedUser3, allowedUser4];
        for (const userInput of userInputs) {
          if (userInput && userInput.trim() !== "") {
            const uid = await validateUser(userInput);
            if (uid) {
              allowedUsersObj[uid] = true;
            }
          }
        }

        await updateData("allowedUsers", allowedUsersObj);
      } catch (error) {
        console.error('Error updating allowed users:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            خطأ في تحديث قائمة المستخدمين: {error.message}
          </Message>
        );
      }
    }
  };

  return (
    <>
      <Button className="br-circle" size="sm" color="red" onClick={handleOpen} appearance="primary">
        A
      </Button>

      <Drawer
        size={isMobile ? "full" : "md"}
        open={isOpen}
        onClose={close}
        placement="right"
      >
        <Drawer.Header>
          <Drawer.Title>تعديل الغرفة</Drawer.Title>
        </Drawer.Header>
        <Drawer.Body>
          <div style={{ height: "90%" }}>
            <EditableInput
              initialValue={name}
              onSave={onNameSave}
              label={<h6 className="mb-2">اسم الغرفة</h6>}
              emptyMsg="اسم الغرفة لا يمكن أن يكون فارغًا"
            />
            <EditableInput
              as="textarea"
              rows={5}
              initialValue={description}
              onSave={onDescriptionSave}
              emptyMsg="وصف الغرفة لا يمكن أن يكون فارغًا"
              wrapperClassName="mt-3"
            />

            <div className="mt-3">
              <Form.Group>
                <Form.ControlLabel>نوع الغرفة</Form.ControlLabel>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                  <Toggle
                    checked={isPrivate}
                    onChange={onPrivacyToggle}
                    disabled={isLoading}
                  />
                  <span style={{ marginRight: '10px' }}>
                    {isPrivate ? 'غرفة خاصة' : 'غرفة عامة'}
                  </span>
                </div>
                <Form.HelpText>
                  {isPrivate
                    ? 'الغرفة الخاصة تظهر فقط للمستخدمين المحددين'
                    : 'الغرفة العامة تظهر لجميع المستخدمين'}
                </Form.HelpText>
              </Form.Group>
            </div>

            {isPrivate && (
              <div className="mt-3">
                <Form.Group>
                  <Form.ControlLabel>المستخدمين المسموح لهم</Form.ControlLabel>

                  <Form.Group controlId="allowedUser1">
                    <Form.ControlLabel>المستخدم المسموح له 1</Form.ControlLabel>
                    <Form.Control
                      value={allowedUser1}
                      onChange={value => setAllowedUser1(value)}
                      placeholder="أدخل معرف المستخدم (UID)"
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group controlId="allowedUser2">
                    <Form.ControlLabel>المستخدم المسموح له 2</Form.ControlLabel>
                    <Form.Control
                      value={allowedUser2}
                      onChange={value => setAllowedUser2(value)}
                      placeholder="أدخل معرف المستخدم (UID)"
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group controlId="allowedUser3">
                    <Form.ControlLabel>المستخدم المسموح له 3</Form.ControlLabel>
                    <Form.Control
                      value={allowedUser3}
                      onChange={value => setAllowedUser3(value)}
                      placeholder="أدخل معرف المستخدم (UID)"
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group controlId="allowedUser4">
                    <Form.ControlLabel>المستخدم المسموح له 4</Form.ControlLabel>
                    <Form.Control
                      value={allowedUser4}
                      onChange={value => setAllowedUser4(value)}
                      placeholder="أدخل معرف المستخدم (UID)"
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Button
                    appearance="primary"
                    onClick={updateAllowedUsers}
                    disabled={isLoading}
                    style={{ marginTop: '10px' }}
                  >
                    تحديث قائمة المستخدمين
                  </Button>

                  <Form.HelpText>
                    <div>يفضل إدخال معرف المستخدم (UID) للإضافة إلى الغرفة</div>
                    <div>يمكنك الحصول على معرف المستخدم (UID) من صفحة الملف الشخصي للمستخدم</div>
                  </Form.HelpText>
                </Form.Group>
              </div>
            )}
          </div>
          <div style={{ height: "10%" }}>
            <Drawer.Actions>
              <Button block onClick={close}>
                إغلاق
              </Button>
            </Drawer.Actions>
          </div>
        </Drawer.Body>
      </Drawer>
    </>
  );
};

export default memo(EditRoomBtnDrawer);
