import React from "react";
import { <PERSON><PERSON>, But<PERSON> } from "rsuite";
import { useModalState } from "../../../misc/custom-hooks";

const ImgBtnModal = ({ src, fileName }) => {
  const { isOpen, open, close } = useModalState();

  return (
    <>
      <div className="image-container">
        <input
          type="image"
          src={src}
          alt="file"
          onClick={open}
          className="message-image vertical-image"
          style={{
            maxWidth: '100%',
            maxHeight: '600px',
            width: 'auto',
            height: 'auto',
            objectFit: 'contain',
            borderRadius: '8px',
            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)'
          }}
        />
      </div>
      <Modal open={isOpen} onClose={close} className="dark-modal image-preview-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">{fileName}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <div className="image-preview-container">
            <img
              src={src}
              className="full-size-image"
              alt="file"
              style={{
                maxWidth: '100%',
                maxHeight: '90vh',
                width: 'auto',
                height: 'auto',
                objectFit: 'contain'
              }}
            />
          </div>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button appearance="subtle" onClick={close}>
            إغلاق
          </Button>
          <a href={src} target="_blank" rel="noopener noreferrer" className="view-original-link">
            عرض الصورة الأصلية
          </a>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ImgBtnModal;
