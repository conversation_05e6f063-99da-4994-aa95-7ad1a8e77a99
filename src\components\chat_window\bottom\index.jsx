import React, { useCallback, useState, useEffect } from "react";
import { Input, InputGroup, Message, toaster, Button } from "rsuite";
import SendIcon from "@rsuite/icons/Send";
import CloseIcon from "@rsuite/icons/Close";
import { serverTimestamp, ref, push, update, onValue, onDisconnect, set, remove, get } from "firebase/database";
import { useParams } from "react-router";
import { useProfile } from "../../../context/profile.context";
import { useReply } from "../../../context/reply.context";
import { useCurrentRoom } from "../../../context/current-room.context";
import { database, auth } from "../../../misc/firebase.config";
import { processMentions, isOwner } from "../../../misc/mention-handler";
import AttchmentBtnModal from "./AttchmentBtnModal";
import SpecialMessageModal from "./SpecialMessageModal";
import TypingIndicator from "./TypingIndicator";
import TranslatedText from "../../TranslatedText";

function assembleMessage(profile, chatId, replyTo = null) {
  return {
    roomId: chatId,
    author: {
      name: profile.name,
      uid: profile.uid,
      email: profile.email,
      createdAt: profile.createdAt,
      ...(profile.avatar ? { avatar: profile.avatar } : {}),
    },
    createdAt: serverTimestamp(),
    likeCount: 0,
    ...(replyTo ? { replyTo } : {}),
  };
}

const ChatBottom = () => {
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState(null);
  const [isOwnerOnly, setIsOwnerOnly] = useState(false);
  const [isSpecialMode, setIsSpecialMode] = useState(false);
  const [isSpecialModalOpen, setIsSpecialModalOpen] = useState(false);
  const [canSendMessages, setCanSendMessages] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [, setMuteEndTime] = useState(null);
  const [muteTimeRemaining, setMuteTimeRemaining] = useState('');
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockReason, setBlockReason] = useState('');
  const [isChatClosed, setIsChatClosed] = useState(false);

  const { chatId } = useParams();
  const { profile } = useProfile();
  const { replyMessage, clearReply } = useReply();
  const isDirectMessage = useCurrentRoom(v => v.isDirectMessage);

  // التحقق من صلاحيات الكتابة في الغرفة وحالة كتم الصوت وحالة الحظر
  useEffect(() => {
    if (!chatId || !profile.uid) return;

    const roomRef = ref(database, `/rooms/${chatId}`);
    const unsubscribe = onValue(roomRef, (snapshot) => {
      if (snapshot.exists()) {
        const roomData = snapshot.val();

        // التحقق مما إذا كانت الغرفة مقيدة (فقط المالك يمكنه الكتابة)
        const isRoomOwnerOnly = roomData.isOwnerOnly || false;
        setIsOwnerOnly(isRoomOwnerOnly);

        // التحقق مما إذا كانت الغرفة في الوضع الخاص
        const isRoomSpecialMode = roomData.isSpecialMode || false;
        setIsSpecialMode(isRoomSpecialMode);

        // التحقق مما إذا كانت الدردشة مغلقة
        const chatClosed = roomData.isChatClosed || false;
        setIsChatClosed(chatClosed);

        // التحقق مما إذا كان المستخدم الحالي هو المالك
        const isUserOwner = profile.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || profile.email === '<EMAIL>';

        // التحقق مما إذا كان المستخدم مشرفاً
        // تحقق من خاصية isAdmin في ملف التعريف وأيضاً من وجود المستخدم في قائمة المشرفين للغرفة
        // const isUserAdmin = profile.isAdmin || false || (roomData.admins && roomData.admins[profile.uid]);

        // التحقق من حالة كتم الصوت
        const checkMuteStatus = async () => {
          try {
            const muteRef = ref(database, `/rooms/${chatId}/muted-users/${profile.uid}`);
            const muteSnapshot = await get(muteRef);

            if (muteSnapshot.exists()) {
              const muteData = muteSnapshot.val();
              const endTime = new Date(muteData.endTime);
              const now = new Date();

              if (endTime > now) {
                // المستخدم ما زال مكتوم الصوت
                setIsMuted(true);
                setMuteEndTime(endTime);

                // حساب الوقت المتبقي
                const remainingMs = endTime - now;
                const remainingMins = Math.floor(remainingMs / 60000);
                const remainingHours = Math.floor(remainingMins / 60);

                if (remainingHours > 0) {
                  setMuteTimeRemaining(`${remainingHours} ساعة و ${remainingMins % 60} دقيقة`);
                } else {
                  setMuteTimeRemaining(`${remainingMins} دقيقة`);
                }
              } else {
                // انتهت مدة كتم الصوت، إزالة السجل
                await set(muteRef, null);
                setIsMuted(false);
                setMuteEndTime(null);
                setMuteTimeRemaining('');
              }
            } else {
              setIsMuted(false);
              setMuteEndTime(null);
              setMuteTimeRemaining('');
            }
          } catch (error) {
            console.error('Error checking mute status:', error);
          }
        };

        // التحقق من حالة الحظر في الدردشة الخاصة
        const checkBlockStatus = async () => {
          try {
            // التحقق مما إذا كانت الدردشة خاصة
            if (!isDirectMessage) {
              setIsBlocked(false);
              setBlockReason('');
              return;
            }

            const currentUserId = auth.currentUser.uid;

            // الحصول على معرف المستخدم الآخر
            const members = roomData.members || {};
            const otherUserId = Object.keys(members).find(id => id !== currentUserId);

            if (!otherUserId) {
              setIsBlocked(false);
              setBlockReason('');
              return;
            }

            // التحقق مما إذا كان المستخدم الحالي قد حظر المستخدم الآخر
            const blockedByMeRef = ref(database, `/private-blocks/${currentUserId}/${otherUserId}`);
            const blockedByMeSnapshot = await get(blockedByMeRef);

            // التحقق مما إذا كان المستخدم الآخر قد حظر المستخدم الحالي
            const blockedByThemRef = ref(database, `/private-blocks/${otherUserId}/${currentUserId}`);
            const blockedByThemSnapshot = await get(blockedByThemRef);

            const isBlockedByMe = blockedByMeSnapshot.exists();
            const isBlockedByThem = blockedByThemSnapshot.exists();

            if (isBlockedByMe) {
              setIsBlocked(true);
              setBlockReason('لقد قمت بحظر هذا المستخدم');
            } else if (isBlockedByThem) {
              setIsBlocked(true);
              setBlockReason('قام هذا المستخدم بحظرك');
            } else {
              setIsBlocked(false);
              setBlockReason('');
            }

          } catch (error) {
            console.error('Error checking block status:', error);
            setIsBlocked(false);
            setBlockReason('');
          }
        };

        checkMuteStatus();
        checkBlockStatus();

        // يمكن للمستخدم إرسال الرسائل في الحالات التالية:
        // 1. إذا كانت الغرفة غير مقيدة (isOwnerOnly = false)
        // 2. إذا كانت الغرفة مقيدة ولكن المستخدم هو المالك أو مشرف
        // 3. إذا لم يكن المستخدم مكتوم الصوت
        // 4. إذا لم يكن المستخدم محظوراً في الدردشة الخاصة
        // 5. إذا كانت دردشة خاصة بين شخصين، يمكن لأي عضو فيها إرسال الرسائل
        // 6. إذا لم تكن الدردشة مغلقة، أو إذا كان المستخدم مالك أو مشرف

        // تحقق مما إذا كانت دردشة خاصة بين شخصين
        const isPrivateChat = isDirectMessage === true;

        // التحقق مما إذا كان المستخدم مشرفًا في هذه الغرفة
        const admins = roomData.admins || {};
        const isUserAdmin = admins[profile.uid] === true;

        // تحقق من صلاحيات إرسال الرسائل
        let canSend = false;

        if (isPrivateChat) {
          // في الدردشات الخاصة، يمكن لأي عضو إرسال الرسائل ما لم يكن محظوراً
          canSend = !isBlocked && !isMuted;
        } else {
          // في المجموعات العامة، تطبق قواعد المشرفين والمالك
          // تعديل: فقط المالك يمكنه إرسال الرسائل في المجموعات المقيدة (isOwnerOnly)
          // المشرفين لا يمكنهم إرسال رسائل في المجموعات المقيدة
          // إذا كانت الدردشة مغلقة، فقط المالك والمشرفين يمكنهم إرسال الرسائل
          canSend = (!isRoomOwnerOnly || isUserOwner) && !isMuted &&
                   (!chatClosed || isUserOwner || isUserAdmin);
        }

        setCanSendMessages(canSend);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [chatId, profile.uid, profile.isAdmin, profile.email, isMuted, isDirectMessage, isBlocked]);

  // إعداد حالة الكتابة في قاعدة البيانات
  useEffect(() => {
    if (!chatId || !profile.uid) return;

    // مرجع لحالة الكتابة للمستخدم الحالي في الغرفة الحالية
    const typingRef = ref(database, `/rooms/${chatId}/typing/${profile.uid}`);

    // إزالة حالة الكتابة عند مغادرة الصفحة أو قطع الاتصال
    const typingOffRef = onDisconnect(typingRef);
    typingOffRef.remove();

    // إضافة مستمع لحدث beforeunload لإزالة حالة الكتابة عند مغادرة الصفحة
    const handleBeforeUnload = () => {
      remove(typingRef);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // تنظيف عند إلغاء تحميل المكون
    return () => {
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
      window.removeEventListener('beforeunload', handleBeforeUnload);
      remove(typingRef);
    };
  }, [chatId, profile.uid, typingTimeout]);

  const onInputChange = useCallback((value) => {
    setInput(value);

    if (!chatId || !profile.uid) return;

    // مرجع لحالة الكتابة للمستخدم الحالي في الغرفة الحالية
    const typingRef = ref(database, `/rooms/${chatId}/typing/${profile.uid}`);

    // إذا كان هناك نص، قم بتعيين حالة الكتابة
    if (value.trim()) {
      // تعيين حالة الكتابة بدون مؤقت للإزالة - ستبقى حتى يتم إرسال الرسالة أو حذف النص
      set(typingRef, {
        name: profile.name,
        timestamp: serverTimestamp(),
        isTyping: true,
        text: value.trim().substring(0, 20) // تخزين جزء من النص للتأكد من أن المستخدم ما زال يكتب نفس الرسالة
      });

      // لا نحتاج إلى مؤقت لأننا نريد أن تبقى حالة الكتابة حتى يتم إرسال الرسالة أو حذف النص
      if (typingTimeout) {
        clearTimeout(typingTimeout);
        setTypingTimeout(null);
      }
    } else {
      // إذا كان النص فارغاً، قم بإزالة حالة الكتابة
      remove(typingRef);
      if (typingTimeout) {
        clearTimeout(typingTimeout);
        setTypingTimeout(null);
      }
    }
  }, [chatId, profile.uid, profile.name, typingTimeout]);

  const onSendClick = async () => {
    if (input.trim() === "") return;

    // إذا كان هناك رد، قم بإضافة معلومات الرد إلى الرسالة
    const msgData = assembleMessage(
      profile,
      chatId,
      replyMessage ? {
        messageId: replyMessage.messageId,
        text: replyMessage.text,
        author: {
          name: replyMessage.author.name,
          uid: replyMessage.author.uid,
          email: replyMessage.author.email || replyMessage.author.uid
        }
      } : null
    );

    msgData.text = input;

    const updates = {};

    const messageId = push(ref(database, "messages")).key;

    updates[`/messages/${messageId}`] = msgData;
    updates[`/rooms/${chatId}/lastMessage`] = {
      ...msgData,
      msgId: messageId,
    };

    // تحديث علامة hasMessages لتشير إلى أن المجموعة تحتوي على رسائل
    updates[`/rooms/${chatId}/hasMessages`] = true;

    // إذا كانت دردشة خاصة، أضف جميع الأعضاء إلى visibleTo
    if (isDirectMessage) {
      // الحصول على معرف المستخدم الآخر
      const roomRef = ref(database, `/rooms/${chatId}`);
      const roomSnapshot = await get(roomRef);

      if (roomSnapshot.exists()) {
        const roomData = roomSnapshot.val();
        const members = roomData.members || {};

        // إضافة جميع الأعضاء إلى visibleTo
        Object.keys(members).forEach(memberId => {
          updates[`/rooms/${chatId}/visibleTo/${memberId}`] = true;
        });
      }
    }

    // إزالة حالة الكتابة عند إرسال الرسالة
    updates[`/rooms/${chatId}/typing/${profile.uid}`] = null;

    setIsLoading(true);

    try {
      await update(ref(database), updates);

      // معالجة الإشارات في الرسالة إذا كان المستخدم هو المالك
      if (isOwner(profile)) {
        // استخدام messageId لأنه تم إنشاؤه بالفعل
        const message = {
          id: messageId,
          text: input,
          ...msgData
        };

        // معالجة الإشارات وإرسال الإشعارات
        await processMentions(input, profile, message, chatId);
      }

      setInput("");
      setIsLoading(false);

      // إلغاء المؤقت
      if (typingTimeout) {
        clearTimeout(typingTimeout);
        setTypingTimeout(null);
      }

      // مسح الرد بعد الإرسال
      if (replyMessage) {
        clearReply();
      }
    } catch (error) {
      setIsLoading(false);
      toaster.push(
        <Message type="error" closable duration={4000}>
          {error.message}
        </Message>
      );
    }
  };

  const onKeyDown = (e) => {
    if (e.keyCode === 13) {
      e.preventDefault();
      onSendClick();
    }
  };

  const afterUpload = useCallback(
    async (files) => {
      setIsLoading(true);

      const updates = {};

      files.forEach((file) => {
        const msgData = assembleMessage(
          profile,
          chatId,
          replyMessage ? {
            messageId: replyMessage.messageId,
            text: replyMessage.text,
            author: {
              name: replyMessage.author.name,
              uid: replyMessage.author.uid,
              email: replyMessage.author.email || replyMessage.author.uid
            }
          } : null
        );
        msgData.file = file;

        const messageId = push(ref(database, "messages")).key;

        updates[`/messages/${messageId}`] = msgData;
      });

      const lastMsgId = Object.keys(updates).pop();

      updates[`/rooms/${chatId}/lastMessage`] = {
        ...updates[lastMsgId],
        msgId: lastMsgId,
      };

      // تحديث علامة hasMessages لتشير إلى أن المجموعة تحتوي على رسائل
      updates[`/rooms/${chatId}/hasMessages`] = true;

      // إذا كانت دردشة خاصة، أضف جميع الأعضاء إلى visibleTo
      if (isDirectMessage) {
        // الحصول على معرف المستخدم الآخر
        const roomRef = ref(database, `/rooms/${chatId}`);
        const roomSnapshot = await get(roomRef);

        if (roomSnapshot.exists()) {
          const roomData = roomSnapshot.val();
          const members = roomData.members || {};

          // إضافة جميع الأعضاء إلى visibleTo
          Object.keys(members).forEach(memberId => {
            updates[`/rooms/${chatId}/visibleTo/${memberId}`] = true;
          });
        }
      }

      try {
        await update(ref(database), updates);
        setIsLoading(false);

        // مسح الرد بعد تحميل الملفات
        if (replyMessage) {
          clearReply();
        }
      } catch (err) {
        setIsLoading(false);
        toaster.push(
          <Message type="error" closable duration={4000}>
            {err.message}
          </Message>
        );
      }
    },
    [profile, chatId, replyMessage, clearReply, isDirectMessage]
  );

  // إضافة مكون الرد
  const renderReplyPreview = () => {
    if (!replyMessage) return null;

    return (
      <div className="reply-preview">
        <div className="reply-preview-content">
          <div className="reply-author">
            <span>رد على {replyMessage.author.name}</span>
          </div>
          <div className="reply-text">
            {replyMessage.text || (replyMessage.file ? 'صورة' : 'ملف مرفق')}
          </div>
        </div>
        <Button
          appearance="subtle"
          size="xs"
          onClick={clearReply}
          className="reply-close-btn"
        >
          <CloseIcon />
        </Button>
      </div>
    );
  };

  // معالجة فتح نافذة الرسالة الخاصة
  const handleOpenSpecialModal = () => {
    setIsSpecialModalOpen(true);
  };

  // معالجة إغلاق نافذة الرسالة الخاصة
  const handleCloseSpecialModal = () => {
    setIsSpecialModalOpen(false);
  };

  return (
    <div>
      {renderReplyPreview()}
      <div className="typing-container">
        <TypingIndicator />
      </div>

      {isMuted ? (
        <div className="owner-only-message">
          <i className="fa-solid fa-volume-xmark"></i>
          <span>تم كتم صوتك في هذه المجموعة. متبقي: {muteTimeRemaining}</span>
        </div>
      ) : isBlocked ? (
        <div className="owner-only-message">
          <i className="fa-solid fa-ban"></i>
          <span>{blockReason}. لا يمكنك إرسال رسائل في هذه الدردشة.</span>
        </div>
      ) : isChatClosed && !canSendMessages ? (
        <div className="owner-only-message">
          <i className="fa-solid fa-clock"></i>
          <span><TranslatedText text="chatClosedMessage" /></span>
        </div>
      ) : isOwnerOnly && !canSendMessages ? (
        <div className="owner-only-message">
          <i className="fa-solid fa-lock"></i>
          <span>هذه المجموعة مقيدة. فقط المالك يمكنه إرسال الرسائل.</span>
        </div>
      ) : isSpecialMode ? (
        // واجهة الإرسال في الوضع الخاص
        <div className="special-mode-input">
          <Button
            block
            appearance="primary"
            color="green"
            onClick={handleOpenSpecialModal}
            className="special-mode-button"
          >
            <i className="fa fa-plus-circle"></i> إنشاء رسالة خاصة
          </Button>
        </div>
      ) : (
        // واجهة الإرسال العادية
        <InputGroup className="chat-input-group">
          <AttchmentBtnModal afterUpload={afterUpload} />
          <Input
            placeholder="اكتب رسالة جديدة هنا..."
            value={input}
            onChange={onInputChange}
            onKeyDown={onKeyDown}
            style={{ textAlign: 'right', direction: 'rtl' }}
            className="chat-input"
            disabled={!canSendMessages}
          />
          <InputGroup.Button
            color="blue"
            appearance="primary"
            onClick={onSendClick}
            disabled={isLoading || !canSendMessages}
            className="send-button"
          >
            <SendIcon />
          </InputGroup.Button>
        </InputGroup>
      )}

      {/* نافذة إنشاء رسالة خاصة */}
      <SpecialMessageModal
        open={isSpecialModalOpen}
        onClose={handleCloseSpecialModal}
      />
    </div>
  );
};

export default ChatBottom;
