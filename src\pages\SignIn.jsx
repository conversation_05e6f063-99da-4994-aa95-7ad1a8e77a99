import React, { useState } from "react";
import {
  signInWithPopup,
  signInWithRedirect,
  getAdditionalUserInfo,
  getRedirectResult,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
} from "firebase/auth";
import GoogleOfficialIcon from "@rsuite/icons/legacy/Google";
import {
  Button,
  Col,
  Container,
  Grid,
  Message,
  Panel,
  Row,
  toaster,
  Loader,
  Checkbox,
  Modal,
  Form,
  Input,
  ButtonToolbar,
  Schema,
  Toggle,
} from "rsuite";
import { useLanguage } from "../context/language.context";
import { ref, serverTimestamp, set, get } from "firebase/database";
import { auth, database, googleProvider } from "../misc/firebase.config.js";
import LanguageSelector from "../components/LanguageSelector";
import EmailVerification from "../components/EmailVerification";
import TranslatedText from "../components/TranslatedText";
import DashboardIcon from '@rsuite/icons/Dashboard';

// Esquema de validación para el formulario
const { StringType, BooleanType } = Schema.Types;

const model = Schema.Model({
  email: StringType()
    .isEmail('الرجاء إدخال بريد إلكتروني صحيح')
    .isRequired('البريد الإلكتروني مطلوب'),
  password: StringType()
    .isRequired('كلمة المرور مطلوبة')
    .minLength(6, 'يجب أن تكون كلمة المرور 6 أحرف على الأقل'),
  name: StringType()
    .isRequired('الاسم مطلوب')
    .minLength(3, 'يجب أن يكون الاسم 3 أحرف على الأقل')
  // تم إزالة التحقق من الموافقة على الشروط والأحكام
});

const SignIn = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [checkboxError, setCheckboxError] = useState(false);
  const [formValue, setFormValue] = useState({
    email: '',
    password: '',
    name: '',
    agreeTerms: false
  });
  const [formError, setFormError] = useState({});
  const [isSignUp, setIsSignUp] = useState(false);
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetEmailSent, setResetEmailSent] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);

  // التحقق من حالة المصادقة عند تحميل الصفحة
  React.useEffect(() => {
    const checkAuthState = async () => {
      try {
        // التحقق من وجود مستخدم مسجل الدخول بالفعل
        if (auth.currentUser) {
          console.log('User already signed in:', auth.currentUser.email);

          // تخزين حالة المصادقة في التخزين المحلي
          localStorage.setItem('authState', 'authenticated');

          // إعادة توجيه المستخدم إلى الصفحة الرئيسية
          window.location.href = '/';
          return;
        }

        // التحقق من وجود بيانات مصادقة في التخزين المحلي
        const savedAuthState = localStorage.getItem('authState');
        if (savedAuthState === 'authenticated') {
          console.log('Found saved auth state, redirecting to home');
          window.location.href = '/';
          return;
        }

        console.log('No authenticated user found');
      } catch (error) {
        console.error('Auth state check error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);

  // معالجة أخطاء تسجيل الدخول
  const handleSignInError = (error) => {
    console.error('Sign in error details:', error);

    let errorMessage = error.message;

    // رسائل خطأ أكثر وضوحًا
    if (error.code === 'auth/popup-closed-by-user') {
      errorMessage = 'تم إغلاق نافذة تسجيل الدخول. يرجى المحاولة مرة أخرى.';
    } else if (error.code === 'auth/popup-blocked') {
      errorMessage = 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.';
    } else if (error.code === 'auth/cancelled-popup-request') {
      errorMessage = 'تم إلغاء طلب النافذة المنبثقة.';
    } else if (error.code === 'auth/network-request-failed') {
      errorMessage = 'فشل طلب الشبكة. يرجى التحقق من اتصالك بالإنترنت.';
    } else if (error.code === 'auth/unauthorized-domain') {
      errorMessage = 'هذا المجال غير مصرح له بتسجيل الدخول. جاري تحويلك إلى طريقة تسجيل دخول بديلة...';
      // محاولة تسجيل الدخول بطريقة إعادة التوجيه بدلاً من النافذة المنبثقة
      setTimeout(() => {
        signInWithRedirect(auth, googleProvider);
      }, 2000);
      return;
    }

    toaster.push(
      <Message type="error" closable duration={6000}>
        خطأ في تسجيل الدخول: {errorMessage}
      </Message>
    );
  };

  const signInWithProvider = async (provider) => {
    setIsLoading(true);
    try {
      console.log('Attempting to sign in with Google...');
      // محاولة تسجيل الدخول باستخدام النافذة المنبثقة أولاً
      const credential = await signInWithPopup(auth, provider);
      console.log('Sign in successful:', credential);

      // التحقق من حالة الحظر
      const bannedRef = ref(database, `/banned-users/${credential.user.uid}`);
      const bannedSnapshot = await get(bannedRef);

      if (bannedSnapshot.exists()) {
        console.log('User is banned, signing out');
        // المستخدم محظور، قم بتسجيل الخروج
        await auth.signOut();

        // عرض رسالة خطأ
        toaster.push(
          <Message type="error" closable duration={8000}>
            <div>
              <h6>تم حظر حسابك</h6>
              <p>لقد تم حظر حسابك من قبل إدارة الموقع.</p>
              <p>للاستفسار، يرجى التواصل مع الدعم الفني.</p>
              <Button appearance="primary" color="blue" size="sm" onClick={() => window.location.href = 'mailto:<EMAIL>'}>
                اتصل بالدعم
              </Button>
            </div>
          </Message>
        );

        setIsLoading(false);
        return;
      }

      const userMeta = getAdditionalUserInfo(credential);

      if (userMeta.isNewUser) {
        console.log('New user, creating profile...');
        await set(ref(database, "users/" + credential.user.uid), {
          name: credential.user.displayName,
          email: credential.user.email,
          createdAt: serverTimestamp(),
        });
      }

      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تسجيل الدخول بنجاح
        </Message>
      );
    } catch (error) {
      console.error('Sign in error:', error);
      handleSignInError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // عرض نافذة الشروط والأحكام
  const showTerms = () => {
    setShowTermsModal(true);
  };

  const onGoogleSignIn = async () => {
    // تم إزالة التحقق من قبول الشروط والأحكام

    // استخدم نفس طريقة تسجيل الدخول لجميع الأجهزة
    try {
      setIsLoading(true);
      console.log('Starting Google sign-in');

      // استخدم signInWithPopup لجميع الأجهزة
      const result = await signInWithPopup(auth, googleProvider);
      console.log('Sign in successful:', result.user.email);

      // التحقق من حالة الحظر
      const bannedRef = ref(database, `/banned-users/${result.user.uid}`);
      const bannedSnapshot = await get(bannedRef);

      if (bannedSnapshot.exists()) {
        console.log('User is banned, signing out');
        // المستخدم محظور، قم بتسجيل الخروج
        await auth.signOut();

        // عرض رسالة خطأ
        toaster.push(
          <Message type="error" closable duration={8000}>
            <div>
              <h6>تم حظر حسابك</h6>
              <p>لقد تم حظر حسابك من قبل إدارة الموقع.</p>
              <p>للاستفسار، يرجى التواصل مع الدعم الفني.</p>
              <Button appearance="primary" color="blue" size="sm" onClick={() => window.location.href = 'mailto:<EMAIL>'}>
                اتصل بالدعم
              </Button>
            </div>
          </Message>
        );

        setIsLoading(false);
        return;
      }

      // التحقق مما إذا كان المستخدم جديدًا
      const userMeta = getAdditionalUserInfo(result);

      if (userMeta.isNewUser) {
        console.log('New user, creating profile...');
        await set(ref(database, "users/" + result.user.uid), {
          name: result.user.displayName,
          email: result.user.email,
          createdAt: serverTimestamp(),
        });
      }

      // تخزين حالة المصادقة في التخزين المحلي
      localStorage.setItem('authState', 'authenticated');

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تسجيل الدخول بنجاح
        </Message>
      );

      // التوجيه مباشرة إلى الصفحة الرئيسية بدون إعادة تحميل
      console.log('Redirecting to home page directly from SignIn');
      window.location.href = '/';

    } catch (error) {
      console.error('Sign in error:', error);
      handleSignInError(error);
      setIsLoading(false);
    }
  };

  // التحقق من البريد الإلكتروني وإضافة @gmail.com إذا لم يكن موجودًا
  const validateAndFormatEmail = (email) => {
    if (!email) return '';

    // إذا كان البريد الإلكتروني لا يحتوي على @ فأضف @gmail.com
    if (!email.includes('@')) {
      return `${email}@gmail.com`;
    }

    return email;
  };

  // التسجيل باستخدام البريد الإلكتروني وكلمة المرور
  const handleEmailSignUp = async () => {
    // تم إزالة التحقق من قبول الشروط والأحكام

    try {
      setIsLoading(true);

      // تنسيق البريد الإلكتروني وإضافة @gmail.com إذا لزم الأمر
      const formattedEmail = validateAndFormatEmail(formValue.email);

      // إنشاء حساب جديد
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        formattedEmail,
        formValue.password
      );

      // إرسال رسالة تحقق من البريد الإلكتروني
      await sendEmailVerification(userCredential.user);

      // إنشاء ملف تعريف المستخدم في قاعدة البيانات
      await set(ref(database, "users/" + userCredential.user.uid), {
        name: formValue.name,
        email: formattedEmail,
        createdAt: serverTimestamp(),
      });

      // تسجيل الخروج حتى يتم التحقق من البريد الإلكتروني
      await auth.signOut();

      // إظهار صفحة التحقق من البريد الإلكتروني بدلاً من النافذة المنبثقة
      setVerificationSent(true);

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={6000}>
          تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب.
        </Message>
      );
    } catch (error) {
      console.error('Sign up error:', error);
      let errorMessage = 'حدث خطأ أثناء إنشاء الحساب';

      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'البريد الإلكتروني غير صالح';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'كلمة المرور ضعيفة جدًا';
      }

      toaster.push(
        <Message type="error" closable duration={6000}>
          {errorMessage}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  const handleEmailSignIn = async () => {
    try {
      setIsLoading(true);

      // تنسيق البريد الإلكتروني وإضافة @gmail.com إذا لزم الأمر
      const formattedEmail = validateAndFormatEmail(formValue.email);

      // تسجيل الدخول
      const userCredential = await signInWithEmailAndPassword(
        auth,
        formattedEmail,
        formValue.password
      );

      // التحقق من تأكيد البريد الإلكتروني
      if (!userCredential.user.emailVerified) {
        // إرسال رسالة تحقق جديدة
        await sendEmailVerification(userCredential.user);

        // تسجيل الخروج حتى يتم التحقق من البريد الإلكتروني
        await auth.signOut();

        // إظهار صفحة التحقق من البريد الإلكتروني بدلاً من النافذة المنبثقة
        setVerificationSent(true);

        // عرض رسالة خطأ
        toaster.push(
          <Message type="error" closable duration={8000}>
            <div>
              <h6>البريد الإلكتروني غير مؤكد</h6>
              <p>يرجى التحقق من بريدك الإلكتروني لتأكيد حسابك.</p>
              <p>تم إرسال رسالة تأكيد جديدة إلى بريدك الإلكتروني.</p>
            </div>
          </Message>
        );

        setIsLoading(false);
        return;
      }

      // التحقق من حالة الحظر
      const bannedRef = ref(database, `/banned-users/${userCredential.user.uid}`);
      const bannedSnapshot = await get(bannedRef);

      if (bannedSnapshot.exists()) {
        console.log('User is banned, signing out');
        // المستخدم محظور، قم بتسجيل الخروج
        await auth.signOut();

        // عرض رسالة خطأ
        toaster.push(
          <Message type="error" closable duration={8000}>
            <div>
              <h6>تم حظر حسابك</h6>
              <p>لقد تم حظر حسابك من قبل إدارة الموقع.</p>
              <p>للاستفسار، يرجى التواصل مع الدعم الفني.</p>
              <Button appearance="primary" color="blue" size="sm" onClick={() => window.location.href = 'mailto:<EMAIL>'}>
                اتصل بالدعم
              </Button>
            </div>
          </Message>
        );

        setIsLoading(false);
        return;
      }

      // تخزين حالة المصادقة في التخزين المحلي
      localStorage.setItem('authState', 'authenticated');

      // عرض رسالة نجاح
      toaster.push(
        <Message type="success" closable duration={4000}>
          تم تسجيل الدخول بنجاح
        </Message>
      );

      // التوجيه إلى الصفحة الرئيسية
      window.location.href = '/';
    } catch (error) {
      console.error('Sign in error:', error);
      let errorMessage = 'حدث خطأ أثناء تسجيل الدخول';

      if (error.code === 'auth/user-not-found') {
        errorMessage = 'البريد الإلكتروني غير مسجل';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'كلمة المرور غير صحيحة';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'البريد الإلكتروني غير صالح';
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'تم تعطيل هذا الحساب';
      }

      toaster.push(
        <Message type="error" closable duration={6000}>
          {errorMessage}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  // إعادة تعيين كلمة المرور
  const handleResetPassword = async () => {
    if (!resetEmail) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          الرجاء إدخال البريد الإلكتروني
        </Message>
      );
      return;
    }

    try {
      setIsLoading(true);

      // تنسيق البريد الإلكتروني وإضافة @gmail.com إذا لزم الأمر
      const formattedEmail = validateAndFormatEmail(resetEmail);

      await sendPasswordResetEmail(auth, formattedEmail);
      setResetEmailSent(true);
      toaster.push(
        <Message type="success" closable duration={6000}>
          تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني
        </Message>
      );
    } catch (error) {
      console.error('Reset password error:', error);
      let errorMessage = 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور';

      if (error.code === 'auth/user-not-found') {
        errorMessage = 'البريد الإلكتروني غير مسجل';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'البريد الإلكتروني غير صالح';
      }

      toaster.push(
        <Message type="error" closable duration={6000}>
          {errorMessage}
        </Message>
      );
    } finally {
      setIsLoading(false);
    }
  };

  // استخدام سياق اللغة
  const { t } = useLanguage();

  // إذا تم إرسال رسالة التحقق، عرض صفحة التحقق
  if (verificationSent) {
    return <EmailVerification />;
  }

  return (
    <Container>
      <Grid className="mt-page">
        <Row>
          <Col x={24} md={12} mdOffset={6}>
            <Panel style={{ backgroundColor: '#000', color: '#fff', borderRadius: '10px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                <div className="text-center" style={{ flex: 1 }}>
                  <h2 style={{ color: '#fff' }}>{t('welcome')}</h2>
                  <p style={{ color: '#ccc' }}>{t('appDescription')}</p>
                </div>
                <div style={{ marginRight: '10px' }}>
                  <LanguageSelector size="sm" appearance="default" />
                </div>
              </div>

              {/* عنوان الصفحة */}
              <div style={{ textAlign: 'center', marginBottom: '30px', marginTop: '10px' }}>
                <h3 style={{ color: '#fff', fontWeight: 'bold' }}>
                  {isSignUp ? t('signUp') : t('signIn')}
                </h3>
              </div>

              {/* نموذج تسجيل الدخول / إنشاء حساب */}
              {!showResetPassword ? (
                <Form
                  fluid
                  model={model}
                  formValue={formValue}
                  onChange={value => setFormValue(value)}
                  onCheck={errors => setFormError(errors)}
                  style={{ marginBottom: '20px' }}
                >
                  {isSignUp && (
                    <Form.Group>
                      <Form.ControlLabel style={{ color: '#fff' }}>{t('name')}</Form.ControlLabel>
                      <Form.Control
                        name="name"
                        placeholder={t('enterName')}
                        style={{ backgroundColor: '#333', color: '#fff', borderColor: '#555' }}
                      />
                    </Form.Group>
                  )}

                  <Form.Group>
                    <Form.ControlLabel style={{ color: '#fff' }}>{t('email')}</Form.ControlLabel>
                    <Form.Control
                      name="email"
                      type="email"
                      placeholder={t('enterEmail')}
                      style={{ backgroundColor: '#333', color: '#fff', borderColor: '#555' }}
                    />
                  </Form.Group>

                  <Form.Group>
                    <Form.ControlLabel style={{ color: '#fff' }}>{t('password')}</Form.ControlLabel>
                    <Form.Control
                      name="password"
                      type="password"
                      placeholder={t('enterPassword')}
                      style={{ backgroundColor: '#333', color: '#fff', borderColor: '#555' }}
                    />
                  </Form.Group>

                  {/* تم إزالة مربع الاختيار من هنا وترك فقط المربع الموجود في الأسفل */}

                  <Form.Group>
                    <ButtonToolbar style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Button
                        appearance="primary"
                        color={isSignUp ? "green" : "blue"}
                        onClick={isSignUp ? handleEmailSignUp : handleEmailSignIn}
                        disabled={isLoading}
                        style={{ width: '100%', marginBottom: '10px' }}
                      >
                        {isLoading ? (
                          <Loader content={isSignUp ? "جاري إنشاء الحساب..." : "جاري تسجيل الدخول..."} />
                        ) : (
                          isSignUp ? t('signUp') : t('signIn')
                        )}
                      </Button>
                    </ButtonToolbar>
                  </Form.Group>

                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '15px' }}>
                    {!isSignUp && (
                      <div style={{ textAlign: 'right' }}>
                        <a
                          href="#"
                          onClick={(e) => { e.preventDefault(); setShowResetPassword(true); }}
                          style={{ color: '#4caf50', textDecoration: 'underline' }}
                        >
                          {t('forgotPassword')}
                        </a>
                      </div>
                    )}

                    <div style={{ textAlign: isSignUp ? 'center' : 'left' }}>
                      <a
                        href="#"
                        onClick={(e) => { e.preventDefault(); setIsSignUp(!isSignUp); }}
                        style={{ color: isSignUp ? '#4caf50' : '#ffeb3b', textDecoration: 'underline', fontWeight: 'bold' }}
                      >
                        {isSignUp ? t('alreadyHaveAccount') + " " + t('signIn') : t('noAccount') + " " + t('signUp')}
                      </a>
                    </div>
                  </div>
                </Form>
              ) : (
                // نموذج استعادة كلمة المرور
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#fff', textAlign: 'center', marginBottom: '15px' }}>{t('resetPassword')}</h4>

                  {!resetEmailSent ? (
                    <>
                      <Form.Group>
                        <Form.ControlLabel style={{ color: '#fff' }}>{t('email')}</Form.ControlLabel>
                        <Input
                          value={resetEmail}
                          onChange={value => setResetEmail(value)}
                          placeholder={t('enterEmail')}
                          style={{ backgroundColor: '#333', color: '#fff', borderColor: '#555' }}
                        />
                      </Form.Group>

                      <Button
                        appearance="primary"
                        color="green"
                        onClick={handleResetPassword}
                        disabled={isLoading}
                        style={{ width: '100%', marginTop: '10px', marginBottom: '10px' }}
                      >
                        {isLoading ? (
                          <Loader content={t('loggingIn')} />
                        ) : (
                          t('sendResetLink')
                        )}
                      </Button>
                    </>
                  ) : (
                    <div style={{ textAlign: 'center', color: '#4caf50', marginBottom: '15px' }}>
                      {t('resetLinkSent')}
                    </div>
                  )}

                  <div style={{ textAlign: 'center' }}>
                    <a
                      href="#"
                      onClick={(e) => { e.preventDefault(); setShowResetPassword(false); setResetEmailSent(false); }}
                      style={{ color: '#4caf50', textDecoration: 'underline' }}
                    >
                      {t('backToSignIn')}
                    </a>
                  </div>
                </div>
              )}

              <div className="mt-4" style={{ marginBottom: '20px' }}>
                <Button
                  appearance="subtle"
                  block
                  className="settings-option-btn"
                  onClick={showTerms}
                  style={{
                    marginBottom: '15px',
                    backgroundColor: '#222',
                    color: '#fff',
                    display: 'flex',
                    alignItems: 'center',
                    padding: '10px 15px',
                    borderRadius: '4px'
                  }}
                >
                  <DashboardIcon style={{ margin: '0 5px' }} /> <TranslatedText text="termsAndPolicies" />
                </Button>
              </div>

              {/* إضافة تعريف الرسوم المتحركة للاهتزاز */}
              <style jsx>{`
                @keyframes shake {
                  0%, 100% { transform: translateX(0); }
                  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                  20%, 40%, 60%, 80% { transform: translateX(5px); }
                }
              `}</style>

              <div className="mt-3" style={{ marginTop: '20px', marginBottom: '10px', position: 'relative' }}>
                <div style={{ textAlign: 'center', marginBottom: '15px', color: '#aaa' }}>
                  <span style={{ display: 'inline-block', position: 'relative' }}>
                    <span style={{ position: 'relative', zIndex: 2, backgroundColor: '#000', padding: '0 10px' }}>أو</span>
                    <hr style={{ position: 'absolute', top: '50%', left: 0, right: 0, margin: 0, zIndex: 1 }} />
                  </span>
                </div>

                <Button
                  block
                  appearance="default"
                  onClick={onGoogleSignIn}
                  disabled={isLoading}
                  style={{
                    marginTop: '10px',
                    marginBottom: '10px',
                    backgroundColor: '#fff',
                    color: '#757575',
                    border: '1px solid #ddd',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '8px 16px'
                  }}
                >
                  {isLoading ? (
                    <Loader content={t('loggingIn')} />
                  ) : (
                    <>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '100%'
                      }}>
                        <GoogleOfficialIcon style={{ color: '#4285F4', marginRight: '10px' }} />
                        <span>{t('signInWithGoogle')}</span>
                      </div>
                    </>
                  )}
                </Button>
              </div>
            </Panel>
          </Col>
        </Row>
      </Grid>

      {/* نافذة التحقق من البريد الإلكتروني */}
      <Modal
        open={showVerificationModal}
        onClose={() => setShowVerificationModal(false)}
        size="md"
        style={{
          background: 'black',
          color: 'white'
        }}
      >
        <Modal.Header style={{ background: '#000', color: '#fff', borderBottom: '1px solid #333' }}>
          <Modal.Title style={{ color: '#fff' }}><TranslatedText text="verified" /></Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ background: '#000', color: '#fff', padding: '20px' }}>
          <div style={{ textAlign: 'center', marginBottom: '20px' }}>
            <div style={{ position: 'relative', width: '100px', height: '100px', margin: '0 auto', marginBottom: '20px' }}>
              {/* دائرة خضراء متحركة */}
              <div style={{
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100px',
                height: '100px',
                borderRadius: '50%',
                border: '3px solid #4caf50',
                animation: 'pulse 2s infinite'
              }}></div>

              {/* أيقونة التحقق */}
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: '50px',
                color: '#4caf50'
              }}>
                <i className="fas fa-check"></i>
              </div>
            </div>

            <h4 style={{ color: '#fff', marginBottom: '15px', fontSize: '22px' }}><TranslatedText text="accountCreatedSuccessfully" /></h4>

            <div style={{
              padding: '15px',
              border: '1px solid #4caf50',
              borderRadius: '8px',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              marginBottom: '15px'
            }}>
              <p style={{ color: '#ccc', marginBottom: '10px', fontSize: '16px' }}>
                <i className="fas fa-info-circle" style={{ marginLeft: '8px', color: '#4caf50' }}></i>
                <TranslatedText text="pleaseCheckInbox" />
              </p>
            </div>
          </div>

          {/* تعريف الرسوم المتحركة */}
          <style jsx>{`
            @keyframes pulse {
              0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
              }

              70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
              }

              100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
              }
            }
          `}</style>
        </Modal.Body>
        <Modal.Footer style={{ background: '#000', borderTop: '1px solid #333' }}>
          <Button
            onClick={() => setShowVerificationModal(false)}
            appearance="primary"
            color="green"
          >
            <TranslatedText text="close" />
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة الشروط والأحكام */}
      <Modal
        open={showTermsModal}
        onClose={() => setShowTermsModal(false)}
        size="md"
        style={{
          background: 'black',
          color: 'white'
        }}
      >
        <Modal.Header style={{ background: '#000', color: '#fff', borderBottom: '1px solid #333' }}>
          <Modal.Title style={{ color: '#fff' }}><TranslatedText text="termsAndConditions" /></Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ background: '#000', color: '#fff', padding: '20px' }}>
          <div style={{ backgroundColor: '#000', borderRadius: '8px', padding: '15px' }}>
            <h4 style={{ color: '#4caf50', marginBottom: '10px' }}><TranslatedText text="termsOfUseAndPolicies" /></h4>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>1. <TranslatedText text="allowedAge" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="ageRequirement" />
            </p>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>2. <TranslatedText text="appPurpose" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="appPurposeDescription1" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="appPurposeDescription2" />
            </p>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>3. <TranslatedText text="buyingAndSellingInApp" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="buyingAndSellingDescription1" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="buyingAndSellingDescription2" />
            </p>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>4. <TranslatedText text="safeDealingMechanism" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="safeDealingDescription1" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="safeDealingDescription2" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="safeDealingDescription3" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="safeDealingDescription4" />
            </p>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>5. <TranslatedText text="paymentMechanism" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="paymentDescription1" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="paymentDescription2" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="paymentDescription3" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="paymentDescription4" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="paymentDescription5" />
            </p>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>6. <TranslatedText text="problemOccurrence" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="problemDescription1" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="problemDescription2" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="problemDescription3" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="problemDescription4" />
            </p>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>7. <TranslatedText text="disputes" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="disputesDescription" />
            </p>
            <ul style={{ color: '#ddd', paddingRight: '20px', backgroundColor: '#000' }}>
              <li><TranslatedText text="disputePoint1" /></li>
              <li><TranslatedText text="disputePoint2" /></li>
              <li><TranslatedText text="disputePoint3" /></li>
            </ul>

            <h5 style={{ color: '#4caf50', marginTop: '15px', marginBottom: '8px' }}>8. <TranslatedText text="chatBehavior" /></h5>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="chatBehaviorDescription1" />
            </p>
            <p style={{ color: '#ccc' }}>
              <TranslatedText text="chatBehaviorDescription2" />
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer style={{ background: '#000', borderTop: '1px solid #333' }}>
          <Button
            onClick={() => { setTermsAccepted(true); setShowTermsModal(false); }}
            appearance="primary"
            color="green"
          >
            <TranslatedText text="close" />
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default SignIn;
