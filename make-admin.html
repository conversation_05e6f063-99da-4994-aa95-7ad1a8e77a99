<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعيين مشرف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e4e6eb;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #3a3b3c;
            background-color: #2d2d2d;
            color: #e4e6eb;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: none;
            background-color: #1DA1F2;
            color: white;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #1a91da;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background-color: #2d2d2d;
            display: none;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعيين مشرف</h1>
        <div class="form-group">
            <label for="uid">معرف المستخدم (UID):</label>
            <input type="text" id="uid" value="HdgvqlLyeagYNofjTtYGefH2wjD3" readonly>
        </div>
        <div class="form-group">
            <label for="email">البريد الإلكتروني للمالك:</label>
            <input type="email" id="email" placeholder="أدخل بريدك الإلكتروني">
        </div>
        <div class="form-group">
            <label for="password">كلمة المرور للمالك:</label>
            <input type="password" id="password" placeholder="أدخل كلمة المرور">
        </div>
        <button id="makeAdmin">تعيين كمشرف</button>
        <div id="result" class="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-database-compat.js"></script>

    <script>
        // تكوين Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyDXYQP0Iu0v5ZeKlGvBKEIY9O5eLyXOHgk",
            authDomain: "toika-369.firebaseapp.com",
            databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
            projectId: "toika-369",
            storageBucket: "toika-369.appspot.com",
            messagingSenderId: "************",
            appId: "1:************:web:c9a5c9b8e9e0c1a0e9e0c1"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);

        // الحصول على عناصر DOM
        const uidInput = document.getElementById('uid');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const makeAdminButton = document.getElementById('makeAdmin');
        const resultDiv = document.getElementById('result');

        // إضافة مستمع حدث للزر
        makeAdminButton.addEventListener('click', async () => {
            const uid = uidInput.value.trim();
            const email = emailInput.value.trim();
            const password = passwordInput.value;

            if (!uid || !email || !password) {
                showResult('يرجى ملء جميع الحقول', false);
                return;
            }

            try {
                // تسجيل الدخول باستخدام بريد المالك وكلمة المرور
                await firebase.auth().signInWithEmailAndPassword(email, password);
                
                // التحقق من أن المستخدم هو المالك
                const currentUser = firebase.auth().currentUser;
                if (currentUser.email !== '<EMAIL>' && currentUser.uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1') {
                    showResult('فقط المالك يمكنه تعيين مشرفين', false);
                    return;
                }

                // تعيين المستخدم كمشرف
                await firebase.database().ref(`users/${uid}/isAdmin`).set(true);
                
                showResult(`تم تعيين المستخدم ${uid} كمشرف بنجاح`, true);
            } catch (error) {
                showResult(`خطأ: ${error.message}`, false);
            }
        });

        // عرض نتيجة العملية
        function showResult(message, isSuccess) {
            resultDiv.textContent = message;
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
