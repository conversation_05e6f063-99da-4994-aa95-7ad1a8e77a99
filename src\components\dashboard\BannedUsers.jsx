import React, { useEffect, useState } from 'react';
import { Button, Table, Message, toaster, InputGroup, Input, Loader } from 'rsuite';
import { ref, onValue, remove, get } from 'firebase/database';
import { database, auth } from '../../misc/firebase.config';
import SearchIcon from '@rsuite/icons/Search';

const { Column, HeaderCell, Cell } = Table;

const BannedUsers = () => {
  const [bannedUsers, setBannedUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [isUnbanning, setIsUnbanning] = useState(false);
  const [unbanningUid, setUnbanningUid] = useState(null);

  // التحقق من أن المستخدم الحالي هو مشرف
  const isAdmin = auth.currentUser && (
    auth.currentUser.email === '<EMAIL>' ||
    auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
    auth.currentUser.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2' ||
    localStorage.getItem('isAdmin') === 'true'
  );

  useEffect(() => {
    const bannedUsersRef = ref(database, '/banned-users');

    const handleBannedUsers = (snapshot) => {
      if (snapshot.exists()) {
        const bannedUsersData = snapshot.val();
        const bannedUsersArray = Object.keys(bannedUsersData).map(uid => ({
          uid,
          ...bannedUsersData[uid]
        }));

        setBannedUsers(bannedUsersArray);
        setFilteredUsers(bannedUsersArray);
      } else {
        setBannedUsers([]);
        setFilteredUsers([]);
      }
      setIsLoading(false);
    };

    onValue(bannedUsersRef, handleBannedUsers);

    return () => {
      // إلغاء الاشتراك عند تفكيك المكون
      const bannedUsersRef = ref(database, '/banned-users');
      onValue(bannedUsersRef, () => {});
    };
  }, []);

  // تصفية المستخدمين المحظورين بناءً على البحث
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredUsers(bannedUsers);
    } else {
      const filtered = bannedUsers.filter(user =>
        user.uid.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (user.name && user.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (user.email && user.email.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery, bannedUsers]);

  // إلغاء حظر المستخدم
  const handleUnban = async (uid) => {
    if (!isAdmin) {
      toaster.push(
        <Message type="error" closable duration={4000}>
          فقط المشرفين يمكنهم إلغاء حظر المستخدمين
        </Message>
      );
      return;
    }

    try {
      setIsUnbanning(true);
      setUnbanningUid(uid);

      // التحقق من وجود المستخدم في قائمة المحظورين
      const bannedUserRef = ref(database, `/banned-users/${uid}`);
      const bannedUserSnapshot = await get(bannedUserRef);

      if (bannedUserSnapshot.exists()) {
        try {
          // إزالة المستخدم من قائمة المحظورين
          await remove(bannedUserRef);

          // تحديث قائمة المستخدمين المحظورين محلياً
          setBannedUsers(prevUsers => prevUsers.filter(user => user.uid !== uid));

          toaster.push(
            <Message type="success" closable duration={4000}>
              تم إلغاء حظر المستخدم بنجاح
            </Message>
          );
        } catch (removeError) {
          console.error('Error removing user from banned list:', removeError);
          toaster.push(
            <Message type="error" closable duration={4000}>
              خطأ في إزالة المستخدم من قائمة الحظر: {removeError.message || 'خطأ غير معروف'}
            </Message>
          );
        }
      } else {
        toaster.push(
          <Message type="error" closable duration={4000}>
            المستخدم غير موجود في قائمة المحظورين
          </Message>
        );
      }
    } catch (error) {
      console.error('Error unbanning user:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          خطأ في إلغاء حظر المستخدم: {error.message || 'خطأ غير معروف'}
        </Message>
      );
    } finally {
      setIsUnbanning(false);
      setUnbanningUid(null);
    }
  };

  // تنسيق تاريخ الحظر
  const formatBanDate = (dateString) => {
    if (!dateString) return 'غير معروف';

    const date = new Date(dateString);
    return date.toLocaleString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="banned-users-container">
      <h3 className="mb-3">المستخدمين المحظورين</h3>

      <InputGroup className="mb-3">
        <Input
          placeholder="البحث بواسطة UID أو الاسم أو البريد الإلكتروني"
          value={searchQuery}
          onChange={setSearchQuery}
        />
        <InputGroup.Addon>
          <SearchIcon />
        </InputGroup.Addon>
      </InputGroup>

      {isLoading ? (
        <div className="text-center mt-4">
          <Loader content="جاري تحميل المستخدمين المحظورين..." />
        </div>
      ) : (
        <Table
          autoHeight
          data={filteredUsers}
          className="dark-table"
          wordWrap
          hover
        >
          <Column width={150} align="center">
            <HeaderCell>معرف المستخدم (UID)</HeaderCell>
            <Cell dataKey="uid" />
          </Column>

          <Column width={150} align="center">
            <HeaderCell>الاسم</HeaderCell>
            <Cell dataKey="name" />
          </Column>

          <Column width={200} align="center">
            <HeaderCell>البريد الإلكتروني</HeaderCell>
            <Cell>
              {rowData => rowData.email || 'غير متوفر'}
            </Cell>
          </Column>

          <Column width={200} align="center">
            <HeaderCell>تاريخ الحظر</HeaderCell>
            <Cell>
              {rowData => formatBanDate(rowData.bannedAt)}
            </Cell>
          </Column>

          <Column width={150} align="center">
            <HeaderCell>تم الحظر بواسطة</HeaderCell>
            <Cell>
              {rowData => rowData.bannedBy ? rowData.bannedBy.name : 'غير معروف'}
            </Cell>
          </Column>

          <Column width={150} align="center">
            <HeaderCell>الإجراءات</HeaderCell>
            <Cell>
              {rowData => (
                <Button
                  appearance="primary"
                  color="green"
                  size="xs"
                  onClick={() => handleUnban(rowData.uid)}
                  disabled={isUnbanning || !isAdmin}
                >
                  {isUnbanning && unbanningUid === rowData.uid ? (
                    <span>جاري إلغاء الحظر... <i className="fas fa-spinner fa-spin"></i></span>
                  ) : (
                    'إلغاء الحظر'
                  )}
                </Button>
              )}
            </Cell>
          </Column>
        </Table>
      )}

      {!isLoading && filteredUsers.length === 0 && (
        <div className="text-center mt-4">
          <p>لا يوجد مستخدمين محظورين</p>
        </div>
      )}
    </div>
  );
};

export default BannedUsers;
