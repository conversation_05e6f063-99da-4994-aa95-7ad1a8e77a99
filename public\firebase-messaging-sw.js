// ملف خدمة العامل لإشعارات Firebase
// هذا الملف ضروري لتجنب أخطاء CSP

// تعريف متغيرات Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDQPFgyKIPEYHZL4WZiZwDlHKlRPEQVJ8g",
  authDomain: "toika-369.firebaseapp.com",
  databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
  projectId: "toika-369",
  storageBucket: "toika-369.appspot.com",
  messagingSenderId: "300804286264",
  appId: "1:300804286264:web:29304874a53644e25403cd"
};

// تهيئة Firebase
self.addEventListener('install', function(event) {
  console.log('Service Worker installed');
});

self.addEventListener('activate', function(event) {
  console.log('Service Worker activated');
});

self.addEventListener('fetch', function(event) {
  // يمكن إضافة منطق التخزين المؤقت هنا إذا لزم الأمر
});

// التعامل مع الإشعارات
self.addEventListener('push', function(event) {
  console.log('Push notification received');
});

self.addEventListener('notificationclick', function(event) {
  console.log('Notification clicked');
});
