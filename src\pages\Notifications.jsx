import React, { useState, useEffect } from 'react';
import { Container, Content, Header, But<PERSON>, List, Badge } from 'rsuite';
import { useHistory } from 'react-router';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';
import { ref, onValue, off, query, orderByChild, limitToLast, update, get, set } from 'firebase/database';
import { database } from '../misc/firebase.config';
import { useProfile } from '../context/profile.context';
import TranslatedText from '../components/TranslatedText';
import TimeAgo from 'timeago-react';
import '../styles/_notifications.scss';

/**
 * صفحة الإشعارات
 * تعرض قائمة بالإشعارات الخاصة بالمستخدم
 */
const Notifications = () => {
  const history = useHistory();
  const { profile } = useProfile();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  // دالة لاسترجاع الإشعارات
  const fetchNotifications = async () => {
    if (!profile || !profile.uid) return;

    console.log('جاري تحميل الإشعارات للمستخدم:', profile.uid, 'في:', new Date().toLocaleTimeString());
    setLoading(true);

    try {
        // قائمة المسارات المحتملة للإشعارات
        const notificationPaths = [
          `notifications/${profile.uid}`,
          `user_notifications/${profile.uid}`,
          `user_data/${profile.uid}/notifications`
        ];

        console.log('جاري البحث عن الإشعارات في المسارات التالية:', notificationPaths);

        let allNotifications = [];
        let foundNotifications = false;

        // البحث في جميع المسارات المحتملة
        for (const path of notificationPaths) {
          console.log('جاري البحث في المسار:', path);
          const pathRef = ref(database, path);

          try {
            const snapshot = await get(pathRef);

            if (snapshot.exists()) {
              console.log('تم العثور على إشعارات في المسار:', path);
              foundNotifications = true;

              const notificationsData = snapshot.val();
              console.log('عدد الإشعارات المستلمة:', Object.keys(notificationsData).length);

              // تحويل البيانات إلى مصفوفة
              const notificationsArray = Object.entries(notificationsData)
                .map(([key, notification]) => {
                  // التأكد من أن الإشعار يحتوي على معرف وأنه كائن صالح
                  if (!notification || typeof notification !== 'object') {
                    console.warn('تم العثور على إشعار غير صالح:', key);
                    return null;
                  }

                  return {
                    id: notification.id || key,
                    key: key, // إضافة المفتاح الأصلي
                    path: path, // إضافة المسار للاستخدام لاحقًا
                    type: notification.type || 'info',
                    title: notification.title || 'إشعار',
                    body: notification.body || '',
                    link: notification.link || '/',
                    timestamp: notification.timestamp || notification.createdAt || Date.now(),
                    createdAt: notification.createdAt || Date.now(),
                    read: notification.read || false
                  };
                })
                .filter(notification => notification !== null); // إزالة الإشعارات غير الصالحة

              // إضافة الإشعارات إلى القائمة الكاملة
              allNotifications = [...allNotifications, ...notificationsArray];
            }
          } catch (pathError) {
            console.warn('خطأ في البحث في المسار:', path, pathError);
          }
        }

        // ترتيب جميع الإشعارات حسب الوقت (الأحدث أولاً)
        allNotifications.sort((a, b) => (b.timestamp || b.createdAt || 0) - (a.timestamp || a.createdAt || 0));

        console.log('إجمالي عدد الإشعارات التي تم العثور عليها:', allNotifications.length);

        if (allNotifications.length > 0) {
          console.log('أول إشعار:', JSON.stringify(allNotifications[0]).substring(0, 200) + '...');
          setNotifications(allNotifications);
          setLoading(false);
        } else {
          console.warn('لم يتم العثور على أي إشعارات في جميع المسارات!');
          setNotifications([]);
          setLoading(false);

          if (!foundNotifications) {
            // إنشاء إشعار اختباري إذا لم يتم العثور على أي إشعارات
            console.log('إنشاء إشعار اختباري...');

            // إنشاء معرف فريد للإشعار
            const notificationId = `test_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

            // بيانات الإشعار
            const testNotification = {
              id: notificationId,
              type: 'info',
              title: 'إشعار اختباري',
              body: 'هذا إشعار اختباري للتأكد من عمل نظام الإشعارات',
              link: '/',
              timestamp: Date.now(),
              createdAt: Date.now(),
              read: false
            };

            // محاولة حفظ الإشعار في جميع المسارات المحتملة
            for (const path of notificationPaths) {
              try {
                const testRef = ref(database, `${path}/${notificationId}`);
                await set(testRef, testNotification);
                console.log('تم إنشاء إشعار اختباري في المسار:', path);

                // التحقق من وجود الإشعار
                const checkSnapshot = await get(testRef);
                if (checkSnapshot.exists()) {
                  console.log('تم التأكد من وجود الإشعار الاختباري في المسار:', path);
                  break; // الخروج من الحلقة بعد النجاح
                }
              } catch (testError) {
                console.warn('فشل في إنشاء إشعار اختباري في المسار:', path, testError);
              }
            }
          }
        }
      } catch (error) {
        console.error('خطأ في استرجاع الإشعارات:', error);
        setLoading(false);

        // محاولة إنشاء إشعار اختباري في حالة حدوث خطأ
        try {
          console.log('إنشاء إشعار اختباري بعد حدوث خطأ...');

          // إنشاء معرف فريد للإشعار
          const notificationId = `error_test_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

          // بيانات الإشعار
          const testNotification = {
            id: notificationId,
            type: 'error',
            title: 'إشعار اختباري بعد خطأ',
            body: 'تم إنشاء هذا الإشعار بعد حدوث خطأ في استرجاع الإشعارات',
            link: '/',
            timestamp: Date.now(),
            createdAt: Date.now(),
            read: false
          };

          // محاولة الحفظ في المسار الرئيسي
          const testRef = ref(database, `notifications/${profile.uid}/${notificationId}`);
          await set(testRef, testNotification);
          console.log('تم إنشاء إشعار اختباري بعد حدوث خطأ');
        } catch (testError) {
          console.error('فشل في إنشاء إشعار اختباري بعد حدوث خطأ:', testError);
        }
      }
    };

  // استدعاء دالة استرجاع الإشعارات عند تحميل المكون
  useEffect(() => {
    if (!profile || !profile.uid) return;

    console.log('تهيئة صفحة الإشعارات...');
    fetchNotifications(); // eslint-disable-line react-hooks/exhaustive-deps

    // قائمة المسارات المحتملة للإشعارات
    const notificationPaths = [
      `notifications/${profile.uid}`,
      `user_notifications/${profile.uid}`,
      `user_data/${profile.uid}/notifications`
    ];

    // قائمة لتخزين وظائف إلغاء الاشتراك
    const unsubscribeFunctions = [];

    // الاستماع للتغييرات في جميع المسارات المحتملة
    for (const path of notificationPaths) {
      console.log('إعداد مستمع للتغييرات في المسار:', path);
      const pathRef = ref(database, path);

      const unsubscribe = onValue(pathRef, (snapshot) => {
        console.log('تم استلام تحديث للإشعارات في المسار:', path, 'في:', new Date().toLocaleTimeString());

        // إعادة تحميل جميع الإشعارات من جميع المسارات
        console.log('إعادة تحميل جميع الإشعارات بعد استلام تحديث...');
        fetchNotifications();
      }, (error) => {
        console.warn('خطأ في الاستماع للتغييرات في المسار:', path, error);
      });

      // إضافة وظيفة إلغاء الاشتراك إلى القائمة
      unsubscribeFunctions.push(unsubscribe);
    }

    // إضافة مؤقت لإعادة محاولة استرجاع الإشعارات بعد 3 ثوانٍ
    const retryTimer = setTimeout(() => {
      console.log('إعادة محاولة استرجاع الإشعارات بعد 3 ثوانٍ');
      fetchNotifications();
    }, 3000);

    // إضافة مؤقت آخر لإعادة محاولة استرجاع الإشعارات بعد 10 ثوانٍ
    const secondRetryTimer = setTimeout(() => {
      console.log('إعادة محاولة استرجاع الإشعارات بعد 10 ثوانٍ');
      fetchNotifications();
    }, 10000);

    return () => {
      console.log('إلغاء الاشتراك في الإشعارات');
      clearTimeout(retryTimer);
      clearTimeout(secondRetryTimer);

      // إلغاء الاشتراك في جميع المستمعين
      console.log('إلغاء الاشتراك في جميع المستمعين:', unsubscribeFunctions.length);
      unsubscribeFunctions.forEach(unsubscribe => {
        try {
          unsubscribe();
        } catch (error) {
          console.warn('خطأ في إلغاء الاشتراك:', error);
        }
      });
    };
  }, [profile]);

  // التعامل مع النقر على الإشعار
  const handleNotificationClick = (notification) => {
    // تحديث حالة الإشعار إلى "مقروء"
    if (profile && profile.uid && notification.id) {
      // استخدام المسار المخزن في الإشعار إذا كان متاحًا
      const notificationPath = notification.path || `notifications/${profile.uid}`;
      const notificationKey = notification.key || notification.id;

      console.log('تحديث حالة الإشعار إلى مقروء:', notificationPath, notificationKey);

      const notificationRef = ref(database, `${notificationPath}/${notificationKey}`);
      update(notificationRef, { read: true })
        .then(() => {
          console.log('تم تحديث حالة الإشعار إلى مقروء');

          // تحديث حالة الإشعار في القائمة المحلية
          setNotifications(prevNotifications =>
            prevNotifications.map(item =>
              item.id === notification.id ? { ...item, read: true } : item
            )
          );
        })
        .catch(error => {
          console.error('خطأ في تحديث حالة الإشعار:', error);

          // محاولة تحديث الإشعار في المسارات البديلة
          const alternativePaths = [
            `notifications/${profile.uid}/${notificationKey}`,
            `user_notifications/${profile.uid}/${notificationKey}`,
            `user_data/${profile.uid}/notifications/${notificationKey}`
          ];

          // استبعاد المسار الذي تم استخدامه بالفعل
          const filteredPaths = alternativePaths.filter(path =>
            path !== `${notificationPath}/${notificationKey}`
          );

          console.log('محاولة تحديث الإشعار في المسارات البديلة:', filteredPaths);

          // محاولة تحديث الإشعار في المسارات البديلة
          filteredPaths.forEach(path => {
            try {
              update(ref(database, path), { read: true })
                .then(() => console.log('تم تحديث حالة الإشعار في المسار البديل:', path))
                .catch(err => console.warn('فشل في تحديث الإشعار في المسار البديل:', path, err));
            } catch (pathError) {
              console.warn('خطأ في محاولة تحديث الإشعار في المسار البديل:', path, pathError);
            }
          });
        });
    }

    // الانتقال إلى الرابط المرتبط بالإشعار
    if (notification.link) {
      history.push(notification.link);
    }
  };

  // دالة لإنشاء إشعار اختباري
  const createTestNotification = async () => {
    if (!profile || !profile.uid) return;

    try {
      console.log('إنشاء إشعار اختباري يدوي...');

      // إنشاء معرف فريد للإشعار
      const notificationId = `manual_test_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // بيانات الإشعار
      const testNotification = {
        id: notificationId,
        type: 'info',
        title: 'إشعار اختباري يدوي',
        body: 'تم إنشاء هذا الإشعار يدويًا للتأكد من عمل نظام الإشعارات',
        link: '/',
        timestamp: Date.now(),
        createdAt: Date.now(),
        read: false
      };

      // قائمة المسارات المحتملة للإشعارات
      const notificationPaths = [
        `notifications/${profile.uid}`,
        `user_notifications/${profile.uid}`,
        `user_data/${profile.uid}/notifications`
      ];

      // محاولة حفظ الإشعار في جميع المسارات المحتملة
      let success = false;

      for (const path of notificationPaths) {
        try {
          const testRef = ref(database, `${path}/${notificationId}`);
          await set(testRef, testNotification);
          console.log('تم إنشاء إشعار اختباري في المسار:', path);

          // التحقق من وجود الإشعار
          const checkSnapshot = await get(testRef);
          if (checkSnapshot.exists()) {
            console.log('تم التأكد من وجود الإشعار الاختباري في المسار:', path);
            success = true;
            break; // الخروج من الحلقة بعد النجاح
          }
        } catch (pathError) {
          console.warn('فشل في إنشاء إشعار اختباري في المسار:', path, pathError);
        }
      }

      if (success) {
        console.log('تم إنشاء إشعار اختباري يدوي بنجاح');

        // إعادة تحميل الإشعارات
        setTimeout(() => {
          console.log('إعادة تحميل الإشعارات بعد إنشاء إشعار اختباري...');
          setLoading(true);
          fetchNotifications();
        }, 1000);
      } else {
        console.error('فشل في إنشاء إشعار اختباري في جميع المسارات!');

        // محاولة أخيرة باستخدام update
        try {
          console.log('محاولة أخيرة باستخدام update...');
          await update(ref(database), {
            [`notifications/${profile.uid}/${notificationId}`]: testNotification
          });
          console.log('تمت المحاولة الأخيرة باستخدام update');

          // إعادة تحميل الإشعارات
          setTimeout(() => {
            console.log('إعادة تحميل الإشعارات بعد المحاولة الأخيرة...');
            setLoading(true);
            fetchNotifications();
          }, 1000);
        } catch (finalError) {
          console.error('فشل في جميع محاولات إنشاء إشعار اختباري:', finalError);
        }
      }
    } catch (error) {
      console.error('فشل في إنشاء إشعار اختباري يدوي:', error);
    }
  };

  // الرجوع إلى الصفحة السابقة
  const handleBack = () => {
    if (window.history.length > 1) {
      history.goBack();
    } else {
      history.push('/');
    }
  };

  // الحصول على أيقونة الإشعار حسب النوع
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'reply':
        return <i className="fa-solid fa-reply notification-list-icon"></i>;
      case 'privateMessage':
        return <i className="fa-solid fa-envelope notification-list-icon"></i>;
      case 'ownerMessage':
        return <i className="fa-solid fa-bullhorn notification-list-icon"></i>;
      case 'mention':
        return <i className="fa-solid fa-at notification-list-icon"></i>;
      default:
        return <i className="fa-solid fa-bell notification-list-icon"></i>;
    }
  };

  return (
    <Container className="dark-container">
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          <h4 className="header-title"><TranslatedText text="notifications" /></h4>
          <Button
            appearance="primary"
            size="sm"
            onClick={createTestNotification}
            style={{ marginLeft: '10px' }}
          >
            إنشاء إشعار اختباري
          </Button>
        </div>
      </Header>
      <Content className="dark-content">
        <div className="notifications-page">
          {loading ? (
            <div className="loading-container">
              <i className="fa-solid fa-spinner fa-spin"></i>
              <p><TranslatedText text="loading" /></p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="empty-notifications">
              <i className="fa-solid fa-bell-slash"></i>
              <p><TranslatedText text="noNotifications" /></p>
              <Button
                appearance="primary"
                onClick={createTestNotification}
                style={{ marginTop: '10px' }}
              >
                إنشاء إشعار اختباري
              </Button>
            </div>
          ) : (
            <List className="notifications-list">
              {notifications.map((notification) => (
                <List.Item
                  key={notification.id}
                  className={`notification-item ${notification.read ? 'read' : 'unread'}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="notification-item-content">
                    <div className={`notification-icon-container ${notification.type}`}>
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="notification-details">
                      <div className="notification-title">
                        {notification.title}
                      </div>
                      <div className="notification-body">
                        {notification.body}
                      </div>
                      <div className="notification-time">
                        <TimeAgo datetime={new Date(notification.timestamp)} />
                      </div>
                    </div>
                    {!notification.read && (
                      <Badge className="unread-badge" />
                    )}
                  </div>
                </List.Item>
              ))}
            </List>
          )}
        </div>
      </Content>
    </Container>
  );
};

export default Notifications;
