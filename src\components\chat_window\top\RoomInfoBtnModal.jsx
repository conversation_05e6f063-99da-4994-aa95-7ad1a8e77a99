import React, { memo, useState, useEffect } from "react";
import { <PERSON><PERSON>, Modal, Message, toaster, IconButton, Form, Toggle, Popover, Whisper } from "rsuite";
import Drawer from 'rsuite/Drawer';
import { useCurrentRoom } from "../../../context/current-room.context";
import { useMediaQuery, useModalState } from "../../../misc/custom-hooks";
import { ref, get, set, remove, update } from "firebase/database";
import { database, auth } from "../../../misc/firebase.config";
import { useParams, useHistory } from "react-router";
import EditableInput from "../../EditableInput";
import MoreIcon from '@rsuite/icons/More';
import TranslatedText from "../../TranslatedText";

const RoomInfoBtnModal = () => {
  const { chatId } = useParams();
  const history = useHistory();
  const { isOpen, open, close } = useModalState();
  const { isOpen: isEditOpen, open: openEdit, close: closeEdit } = useModalState();
  const description = useCurrentRoom((v) => v.description);
  const name = useCurrentRoom((v) => v.name);
  const isAdmin = useCurrentRoom(v => v.isAdmin);
  const createdBy = useCurrentRoom(v => v.createdBy);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const isMobile = useMediaQuery("(max-width: 992px)");
  const [isRoomCreator, setIsRoomCreator] = useState(false);

  // حالات للغرفة الخاصة
  const [isPrivate, setIsPrivate] = useState(false);
  const [allowedUser1, setAllowedUser1] = useState("");
  const [allowedUser2, setAllowedUser2] = useState("");
  const [allowedUser3, setAllowedUser3] = useState("");
  const [allowedUser4, setAllowedUser4] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // التحقق مما إذا كان المستخدم الحالي هو منشئ المجموعة
  useEffect(() => {
    if (chatId && auth.currentUser) {
      const roomRef = ref(database, `/rooms/${chatId}`);
      get(roomRef).then((snapshot) => {
        if (snapshot.exists()) {
          const roomData = snapshot.val();
          // التحقق مما إذا كان المستخدم الحالي هو منشئ المجموعة
          if (roomData.createdBy && roomData.createdBy === auth.currentUser.uid) {
            setIsRoomCreator(true);
          } else {
            setIsRoomCreator(false);
          }
        }
      }).catch(err => {
        console.error("Error checking room creator:", err);
      });
    }
  }, [chatId]);

  // تحميل بيانات الغرفة عند فتح نافذة التعديل
  useEffect(() => {
    if (isEditOpen && chatId) {
      console.log("Loading room data for editing...");
      // تحميل حالة الخصوصية
      const roomRef = ref(database, `/rooms/${chatId}`);
      get(roomRef).then((snapshot) => {
        if (snapshot.exists()) {
          console.log("Room data found:", snapshot.val());
          const roomData = snapshot.val();
          if (roomData.isPrivate) {
            setIsPrivate(true);

            // تحميل المستخدمين المسموح لهم
            if (roomData.allowedUsers) {
              const allowedUsers = Object.keys(roomData.allowedUsers).filter(
                uid => uid !== auth.currentUser.uid && uid !== 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1'
              );

              console.log("Allowed users:", allowedUsers);

              if (allowedUsers.length > 0) setAllowedUser1(allowedUsers[0] || "");
              if (allowedUsers.length > 1) setAllowedUser2(allowedUsers[1] || "");
              if (allowedUsers.length > 2) setAllowedUser3(allowedUsers[2] || "");
              if (allowedUsers.length > 3) setAllowedUser4(allowedUsers[3] || "");
            } else {
              // إعادة تعيين حقول المستخدمين المسموح لهم
              setAllowedUser1("");
              setAllowedUser2("");
              setAllowedUser3("");
              setAllowedUser4("");
            }
          } else {
            setIsPrivate(false);
            setAllowedUser1("");
            setAllowedUser2("");
            setAllowedUser3("");
            setAllowedUser4("");
          }
        } else {
          console.log("Room data not found");
        }
      }).catch(err => {
        console.error("Error loading room data:", err);
      });
    }
  }, [isEditOpen, chatId]);

  // دالة تحديث البيانات
  const updateData = (key, value) => {
    set(ref(database, `/rooms/${chatId}/${key}`), value)
      .then(() => {
        toaster.push(
          <Message type="success" closable duration={4000}>
            <TranslatedText text="updatedSuccessfully" />
          </Message>
        );
      })
      .catch((err) => {
        toaster.push(
          <Message type="error" closable duration={4000}>
            {err.message}
          </Message>
        );
      });
  };

  const onNameSave = (newName) => {
    updateData("name", newName);
  };

  const onDescriptionSave = (newDesc) => {
    updateData("description", newDesc);
  };

  // تحديث خصوصية الغرفة
  const onPrivacyToggle = async (checked) => {
    setIsPrivate(checked);

    try {
      await updateData("isPrivate", checked);

      // إذا كانت الغرفة خاصة، أضف المستخدم الحالي إلى قائمة المستخدمين المسموح لهم
      if (checked) {
        const allowedUsersObj = {};
        allowedUsersObj[auth.currentUser.uid] = true;

        // إضافة المستخدمين المحددين من الحقول المنفصلة
        const userInputs = [allowedUser1, allowedUser2, allowedUser3, allowedUser4];
        for (const userInput of userInputs) {
          if (userInput && userInput.trim() !== "") {
            allowedUsersObj[userInput] = true;
          }
        }

        await updateData("allowedUsers", allowedUsersObj);
      } else {
        // إذا كانت الغرفة عامة، احذف قائمة المستخدمين المسموح لهم
        await set(ref(database, `/rooms/${chatId}/allowedUsers`), null);
      }
    } catch (error) {
      console.error('Error updating privacy:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          <TranslatedText text="errorUpdatingRoomPrivacy" />: {error.message}
        </Message>
      );
    }
  };

  // تحديث قائمة المستخدمين المسموح لهم
  const updateAllowedUsers = async () => {
    if (isPrivate) {
      try {
        const allowedUsersObj = {};

        // إضافة المستخدم الحالي (مشرف الغرفة) إلى قائمة المستخدمين المسموح لهم
        allowedUsersObj[auth.currentUser.uid] = true;

        // إضافة المستخدمين المحددين من الحقول المنفصلة
        const userInputs = [allowedUser1, allowedUser2, allowedUser3, allowedUser4];
        for (const userInput of userInputs) {
          if (userInput && userInput.trim() !== "") {
            allowedUsersObj[userInput] = true;
          }
        }

        await updateData("allowedUsers", allowedUsersObj);

        toaster.push(
          <Message type="success" closable duration={4000}>
            <TranslatedText text="allowedUsersListUpdatedSuccessfully" />
          </Message>
        );
      } catch (error) {
        console.error('Error updating allowed users:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorUpdatingUsersList" />: {error.message}
          </Message>
        );
      }
    }
  };

  // معالجة حذف المجموعة
  const handleDeleteRoom = async (isSuccessful) => {
    try {
      console.log('Deleting room from RoomInfoBtnModal:', chatId, 'Sale successful:', isSuccessful);

      if (!chatId) {
        console.error('Chat ID is undefined');
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorRoomIdNotFound" />
          </Message>
        );
        return;
      }

      // الحصول على بيانات الغرفة
      const roomRef = ref(database, `/rooms/${chatId}`);
      const roomSnapshot = await get(roomRef);

      if (!roomSnapshot.exists()) {
        console.error('Room not found');
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorRoomNotFound" />
          </Message>
        );
        return;
      }

      const roomData = roomSnapshot.val();
      const isDirectMessage = roomData.isDirectMessage === true;

      // التحقق من الصلاحيات
      const currentUserId = auth.currentUser.uid;
      const isOwner = currentUserId === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.currentUser.email === '<EMAIL>';
      const isRoomCreator = roomData.createdBy === currentUserId;
      const isUserAdmin = isAdmin || isOwner;

      // إذا كانت دردشة خاصة بين شخصين، تحقق مما إذا كان المستخدم عضو فيها
      const isMember = roomData.members && roomData.members[currentUserId];

      // التحقق من صلاحيات الحذف
      let canDelete = false;

      if (isDirectMessage) {
        // إذا كانت دردشة خاصة، يمكن لأي عضو فيها حذفها
        // دائماً السماح بحذف الدردشات الخاصة للمستخدمين الأعضاء فيها
        canDelete = true;
      } else {
        // إذا كانت مجموعة عادية، يمكن فقط للمشرف المنشئ أو المالك حذفها
        canDelete = isUserAdmin && (isRoomCreator || isOwner);
      }

      if (!canDelete) {
        console.error('User does not have permission to delete this room');
        toaster.push(
          <Message type="error" closable duration={4000}>
            <TranslatedText text="errorNoPermissionToDeleteGroup" />
          </Message>
        );
        return;
      }

      try {
        // إذا كانت دردشة خاصة بين شخصين، نحذفها فعلياً
        if (isDirectMessage) {
          // حذف الرسائل المرتبطة بالدردشة
          try {
            // الحصول على الرسائل المرتبطة بالدردشة
            const messagesRef = ref(database, `/messages`);
            const messagesSnapshot = await get(messagesRef);

            if (messagesSnapshot.exists()) {
              const messagesData = messagesSnapshot.val();
              const updates = {};

              // البحث عن الرسائل المرتبطة بالدردشة وحذفها
              for (const messageId in messagesData) {
                const message = messagesData[messageId];
                if (message.roomId === chatId) {
                  updates[`/messages/${messageId}`] = null;
                }
              }

              // تنفيذ الحذف
              if (Object.keys(updates).length > 0) {
                await update(ref(database), updates);
              }
            }
          } catch (error) {
            console.error('Error deleting messages:', error);
          }

          // حذف الدردشة من قائمة الغرف النشطة
          await remove(roomRef);
        } else {
          // للمجموعات العادية، نقوم بإخفائها بدلاً من حذفها
          // تحديث الغرفة لتكون مخفية
          const updatedRoomData = {
            ...roomData,
            isHidden: true,
            hiddenAt: new Date().toISOString(),
            hiddenBy: currentUserId,
            saleSuccessful: isSuccessful
          };

          // حفظ بيانات الغرفة المحدثة في نفس المكان
          await set(roomRef, updatedRoomData);
          console.log('Room marked as hidden');

          // إضافة الغرفة إلى سجل الغرف المخفية للمالك
          // استخدام معرف المالك بدلاً من معرف المستخدم الحالي لتجنب مشاكل الصلاحيات
          const ownerUid = 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1';
          await set(ref(database, `/hidden-rooms/${chatId}`), {
            id: chatId,
            name: roomData.name,
            description: roomData.description,
            hiddenAt: new Date().toISOString(),
            hiddenBy: isOwner ? currentUserId : ownerUid,
            saleSuccessful: isSuccessful
          });
          console.log('Added to hidden-rooms for owner');
        }

        // إغلاق نافذة التأكيد ونافذة المعلومات
        setIsDeleteConfirmOpen(false);
        close();

        // عرض رسالة نجاح
        toaster.push(
          <Message type="success" closable duration={4000}>
            {isDirectMessage
              ? <TranslatedText text="chatDeletedSuccessfully" />
              : <><TranslatedText text="groupDeletedSuccessfully" />{isSuccessful ? ' ✅' : ' ❌'}</>
            }
          </Message>
        );

        // الانتقال إلى الصفحة الرئيسية
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } catch (error) {
        console.error('Error processing room:', error);
        toaster.push(
          <Message type="error" closable duration={4000}>
            {isDirectMessage
              ? <><TranslatedText text="errorDeletingChat" />: {error.message}</>
              : <><TranslatedText text="errorDeletingGroup" />: {error.message}</>
            }
          </Message>
        );
      }
    } catch (error) {
      console.error('Error processing room:', error);
      toaster.push(
        <Message type="error" closable duration={4000}>
          {isDirectMessage
            ? <><TranslatedText text="errorDeletingChat" />: {error.message}</>
            : <><TranslatedText text="errorDeletingGroup" />: {error.message}</>
          }
        </Message>
      );
    }
  };

  // مكون زر تعديل الغرفة
  const EditRoomButton = ({ onEdit }) => {
    return (
      <Button
        appearance="primary"
        color="blue"
        block
        onClick={onEdit}
        className="mt-2"
      >
        <TranslatedText text="editRoom" />
      </Button>
    );
  };

  // تم نقل هذا الكود إلى أعلى المكون

  // الحصول على معلومات المجموعة
  const isDirectMessage = useCurrentRoom(v => v.isDirectMessage);
  const members = useCurrentRoom(v => v.members);

  // مكون أزرار معلومات وتعديل الغرفة
  const renderButtons = () => {
    // التحقق مما إذا كان المستخدم هو المالك
    const isOwner = auth.currentUser && (
      auth.currentUser.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' ||
      auth.currentUser.email === '<EMAIL>'
    );

    // عرض أزرار التعديل والحذف فقط إذا كان المستخدم هو منشئ المجموعة أو المالك
    const canEditOrDeleteRoom = isRoomCreator || isOwner;

    // التحقق مما إذا كان المستخدم عضو في المجموعة
    const isMember = members && auth.currentUser && members[auth.currentUser.uid];

    // إذا كانت المجموعة هي دردشة خاصة بين شخصين، عرض زر الحذف فقط
    if (isDirectMessage) {
      return (
        <Button
          id="delete-chat-btn"
          appearance="subtle"
          onClick={() => setIsDeleteConfirmOpen(true)}
          className="room-btn room-delete-btn"
        >
          <TranslatedText text="deleteChat" />
        </Button>
      );
    }

    return (
      <>
        {/* زر معلومات المجموعة يظهر للجميع */}
        <Button
          id="room-info-btn"
          appearance="subtle"
          onClick={open}
          className="room-btn room-info-btn"
        >
          <TranslatedText text="groupInfo" />
        </Button>

        {/* زر تعديل المجموعة يظهر فقط للمالك */}
        {isOwner && (
          <Button
            id="room-edit-btn"
            appearance="subtle"
            onClick={openEdit}
            className="room-btn room-edit-btn"
          >
            <TranslatedText text="editGroup" />
          </Button>
        )}

        {/* زر حذف المجموعة يظهر للمشرف منشئ المجموعة أو المالك */}
        {((isAdmin && isRoomCreator) || isOwner) && (
          <Button
            id="room-delete-btn"
            appearance="subtle"
            onClick={() => setIsDeleteConfirmOpen(true)}
            className="room-btn room-delete-btn"
          >
            <TranslatedText text="deleteGroup" />
          </Button>
        )}
      </>
    );
  };

  return (
    <>
      {renderButtons()}

      <Drawer
        size={isMobile ? "full" : "md"}
        open={isEditOpen}
        onClose={closeEdit}
        placement="right"
        className="dark-drawer"
      >
        <Drawer.Header>
          <Drawer.Title><TranslatedText text="editRoom" /></Drawer.Title>
        </Drawer.Header>
        <Drawer.Body>
          <div style={{ height: "90%" }}>
            <EditableInput
              initialValue={name}
              onSave={onNameSave}
              label={<h6 className="mb-2"><TranslatedText text="roomName" /></h6>}
              emptyMsg={<TranslatedText text="roomNameRequired" />}
            />
            <EditableInput
              as="textarea"
              rows={5}
              initialValue={description}
              onSave={onDescriptionSave}
              emptyMsg={<TranslatedText text="roomDescriptionRequired" />}
              wrapperClassName="mt-3"
            />

            <div className="mt-3">
              <Form.Group>
                <Form.ControlLabel><TranslatedText text="roomType" /></Form.ControlLabel>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                  <Toggle
                    checked={isPrivate}
                    onChange={onPrivacyToggle}
                    disabled={isLoading}
                  />
                  <span style={{ marginRight: '10px' }}>
                    {isPrivate ? <TranslatedText text="privateRoom" /> : <TranslatedText text="publicRoom" />}
                  </span>
                </div>
                <Form.HelpText>
                  {isPrivate
                    ? <TranslatedText text="privateRoomDescription" />
                    : <TranslatedText text="publicRoomDescription" />}
                </Form.HelpText>
              </Form.Group>
            </div>

            {isPrivate && (
              <div className="mt-3">
                <Form.Group>
                  <Form.ControlLabel><TranslatedText text="allowedUsers" /></Form.ControlLabel>

                  <Form.Group controlId="allowedUser1">
                    <Form.ControlLabel><TranslatedText text="allowedUser1" /></Form.ControlLabel>
                    <Form.Control
                      value={allowedUser1}
                      onChange={value => setAllowedUser1(value)}
                      placeholder={<TranslatedText text="enterUserID" />}
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group controlId="allowedUser2">
                    <Form.ControlLabel><TranslatedText text="allowedUser2" /></Form.ControlLabel>
                    <Form.Control
                      value={allowedUser2}
                      onChange={value => setAllowedUser2(value)}
                      placeholder={<TranslatedText text="enterUserID" />}
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group controlId="allowedUser3">
                    <Form.ControlLabel><TranslatedText text="allowedUser3" /></Form.ControlLabel>
                    <Form.Control
                      value={allowedUser3}
                      onChange={value => setAllowedUser3(value)}
                      placeholder={<TranslatedText text="enterUserID" />}
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group controlId="allowedUser4">
                    <Form.ControlLabel><TranslatedText text="allowedUser4" /></Form.ControlLabel>
                    <Form.Control
                      value={allowedUser4}
                      onChange={value => setAllowedUser4(value)}
                      placeholder={<TranslatedText text="enterUserID" />}
                      dir="ltr"
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Button
                    appearance="primary"
                    onClick={updateAllowedUsers}
                    disabled={isLoading}
                    style={{ marginTop: '10px' }}
                  >
                    <TranslatedText text="updateUsersList" />
                  </Button>

                  <Form.HelpText>
                    <div><TranslatedText text="preferEnterUserID" /></div>
                    <div><TranslatedText text="getUIDFromProfile" /></div>
                  </Form.HelpText>
                </Form.Group>
              </div>
            )}
          </div>
          <div style={{ height: "10%" }}>
            <Drawer.Actions>
              <Button block onClick={closeEdit}>
                <TranslatedText text="close" />
              </Button>
            </Drawer.Actions>
          </div>
        </Drawer.Body>
      </Drawer>

      <Modal open={isOpen} onClose={close} className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">{name}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          <h6 className="mb-1"><TranslatedText text="description" /></h6>
          <p>{description || <TranslatedText text="noDescription" />}</p>
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          <Button block onClick={close}>
            <TranslatedText text="close" />
          </Button>
        </Modal.Footer>
      </Modal>

      {/* نافذة تأكيد حذف المجموعة */}
      <Modal open={isDeleteConfirmOpen} onClose={() => setIsDeleteConfirmOpen(false)} size="xs" className="dark-modal">
        <Modal.Header className="dark-modal-header">
          <Modal.Title className="dark-modal-title">
            {isDirectMessage
              ? <TranslatedText text="confirmDeleteChat" />
              : <TranslatedText text="confirmGroupDeletion" />
            }
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="dark-modal-body">
          {isDirectMessage ? (
            <p><TranslatedText text="areYouSureDeleteChat" /></p>
          ) : (
            <p><TranslatedText text="wasTheSaleSuccessful" /></p>
          )}
        </Modal.Body>
        <Modal.Footer className="dark-modal-footer">
          {isDirectMessage ? (
            <>
              <Button onClick={() => handleDeleteRoom(true)} color="red" appearance="primary">
                <TranslatedText text="yes" />
              </Button>
              <Button onClick={() => setIsDeleteConfirmOpen(false)} color="green" appearance="primary">
                <TranslatedText text="no" />
              </Button>
            </>
          ) : (
            <>
              <Button onClick={() => handleDeleteRoom(true)} color="green" appearance="primary">
                <TranslatedText text="yes" />
              </Button>
              <Button onClick={() => handleDeleteRoom(false)} color="red" appearance="primary">
                <TranslatedText text="no" />
              </Button>
              <Button onClick={() => setIsDeleteConfirmOpen(false)} appearance="subtle">
                <TranslatedText text="cancel" />
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default memo(RoomInfoBtnModal);
