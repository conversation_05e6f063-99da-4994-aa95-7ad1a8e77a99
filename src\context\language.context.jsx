import React, { createContext, useContext, useState, useEffect } from 'react';

// تعريف اللغات المدعومة
export const LANGUAGES = {
  AR: 'ar', // العربية
  EN: 'en', // الإنجليزية
  KU: 'ku', // الكردية السورانية
};

// استخدام ترجمة Google التلقائية
// الترجمات لكل لغة
export const translations = {
  [LANGUAGES.AR]: {
    // الواجهة العامة
    appName: 'Trust Market',
    login: 'تسجيل الدخول',
    logout: 'تسجيل الخروج',
    settings: 'الإعدادات',
    chat: 'الدردشة',
    privateChats: 'الدردشات الخاصة',
    luckyWheel: 'عجلة الحظ',

    // اللغات
    language: 'اللغة',
    selectLanguage: 'اختر اللغة',
    arabic: 'العربية',
    english: 'الإنجليزية',
    kurdish: 'الكردية',

    // صفحة تسجيل الدخول
    welcome: 'مرحبًا بك في Trust Market',
    appDescription: 'منصة بيع وشراء بشكل آمن',
    signIn: 'تسجيل دخول',
    email: 'بريد إلكتروني',
    enterEmail: 'أدخل بريد إلكتروني',
    password: 'كلمة مرور',
    enterPassword: 'أدخل كلمة مرور',
    noAccount: 'ليس لديك حساب؟',
    signUp: 'سجل الآن',
    forgotPassword: 'هل نسيت كلمة المرور؟',
    agreeToTerms: 'أنت تتوافق مع سياسات التطبيق',
    termsAndConditions: 'الشروط والأحكام',
    name: 'الاسم',
    enterName: 'أدخل اسمك',
    signInWithGoogle: 'تسجيل دخول باستخدام جوجل',
    resetPassword: 'استعادة كلمة المرور',
    sendResetLink: 'أرسل رابط استعادة كلمة المرور',
    backToSignIn: 'رجوع إلى تسجيل الدخول',
    nameRequired: 'الاسم مطلوب',
    emailRequired: 'البريد الإلكتروني مطلوب',
    passwordMinLength: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    passwordRequired: 'كلمة المرور مطلوبة',
    resetLinkSent: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
    alreadyHaveAccount: 'لديك حساب بالفعل؟',
    successfullySignedIn: 'تم تسجيل دخول بنجاح',
    mustAgreeToTerms: 'يجب الموافقة على الشروط والأحكام للمتابعة',
    loginWithGoogle: 'تسجيل الدخول باستخدام جوجل',
    loggingIn: 'جاري تسجيل الدخول...',

    // الإعدادات
    support: 'الدعم الفني',
    contactSupport: 'اتصل بالدعم',
    termsAndPolicies: 'الشروط والسياسات',
    rewardCodes: 'فحص أكواد الجوائز',
    close: 'إغلاق',
    language: 'اللغة',
    selectLanguage: 'اختر اللغة',

    // عجلة الحظ
    back: 'العودة',
    availableTickets: 'التذاكر المتاحة',
    spin: 'تدوير العجلة',
    spinning: 'جاري التدوير...',
    completeTasksForTickets: 'أكمل المهام للحصول على بطاقات',
    dailyTasks: 'المهام اليومية',
    completeTask: 'إكمال المهمة',
    taskCompleted: 'تم الإكمال',
    tickets: 'تذكرة',
    dailyLogin: 'تسجيل الدخول اليومي',
    dailyLoginDesc: 'قم بتسجيل الدخول إلى التطبيق كل يوم',
    ticket: 'تذكرة',
    // جوائز عجلة الحظ
    nothingReward: 'لقد ربحت: لا شيء',
    spinAgainReward: 'لقد ربحت: إعادة السحب',
    freeAdReward: 'لقد ربحت: إعلان مجاني',
    adminRoomReward: 'لقد ربحت: غرفة أدمن',
    spinAgainTicketReturned: 'تم إعادة بطاقة السحب',
    rewardCode: 'رمز الجائزة',
    congratulations: 'مبروك!',
    keepCodeForSupport: 'احتفظ بهذا الرمز وقدمه للدعم للحصول على جائزتك',
    close: 'إغلاق',
    loading: 'جاري التحميل...',
    manageWheel: 'إدارة العجلة',
    shareApp: 'مشاركة التطبيق',
    shareAppDesc: 'قم بمشاركة رابط التطبيق مع أصدقائك',
    taskCompletedSuccess: 'تم إكمال المهمة! حصلت على',
    errorCompletingTask: 'حدث خطأ أثناء إكمال المهمة',

    // الدردشة
    send: 'إرسال',
    reply: 'رد',
    like: 'إعجاب',
    delete: 'حذف',
    mute: 'كتم الصوت',
    ban: 'حظر',
    likeMessage: 'الإعجاب بالرسالة',
    typeMessage: 'اكتب رسالة جديدة هنا...',
    blockUser: 'حظر المستخدم',
    blockUserInChat: 'حظر المستخدم في هذه الدردشة',
    deleteChat: 'حذف الدردشة',

    // المجموعات
    roomName: 'اسم الغرفة',
    description: 'الوصف',
    roomDescriptionOptional: 'وصف الغرفة اختياري',
    roomNameRequired: 'اسم الغرفة مطلوب',
    roomType: 'نوع الغرفة',
    privateRoomDescription: 'الغرفة الخاصة تظهر فقط للمستخدمين المحددين',
    ownerOnlyPublicRooms: 'ملاحظة: فقط المالك يمكنه إنشاء غرف دردشة عامة',
    allowedUsers: 'المستخدم المسموح له',
    createNewChatRoom: 'إنشاء غرفة دردشة جديدة',
    availableChatRooms: 'غرف الدردشة المتاحة',
    groupInfo: 'معلومات المجموعة',
    restrictedGroup: 'هذه المجموعة مقيدة. فقط المالك يمكنه إرسال الرسائل.',
    memberSince: 'عضو منذ',
    createPrivateRoom: 'إنشاء غرفة خاصة',
    totalUsers: 'إجمالي المستخدمين',
    activeUsers: 'المستخدمين النشطين',
    deleteGroup: 'حذف المجموعة',
    confirmGroupDeletion: 'تأكيد حذف المجموعة',
    wasSuccessful: 'هل نجحت عملية البيع؟',
    cancel: 'الغاء',
    no: 'لا',
    yes: 'نعم',

    // إيقاف الدردشة
    closeChatButton: 'إيقاف الدردشة',
    openChatButton: 'فتح الدردشة',
    chatClosedMessage: 'تم اغلاق الدردشة لليوم سوف يفتح في 9:00 صباحا الی 12:00 ليل بتوقيت العراق',
    autoChatSchedule: 'جدولة تلقائية',
    chatOpenTime: 'وقت فتح الدردشة',
    chatCloseTime: 'وقت إغلاق الدردشة',
    saveSchedule: 'حفظ الجدولة',
    chatScheduleSaved: 'تم حفظ جدولة الدردشة بنجاح',

    // الملف الشخصي
    nickname: 'الاسم المستعار',
    changeNamePhotoWeekly: 'يمكنك تغيير الاسم والصورة مرة واحدة في الأسبوع',
    chooseNewProfilePicture: 'اختيار صورة شخصية جديدة',
    profile: 'الملف الشخصي',

    // الصور
    uploadPhotos: 'رفع صور',
    fileSizeLimit: '* يسمح فقط بالملفات التي حجمها أقل من 5 ميجابايت',
    sendToChat: 'إرسال إلى الدردشة',

    // الإشعارات
    notifications: 'الإشعارات',
    noNotifications: 'لا توجد إشعارات',
    loading: 'جاري التحميل...',
    replyNotification: 'رد على رسالتك',
    privateMessageNotification: 'رسالة خاصة جديدة',
    ownerMessageNotification: 'رسالة من المالك',
    mentionNotification: 'تمت الإشارة إليك',
    chatClosedMessage: 'تم اغلاق الدردشة لليوم سوف يفتح في 9:00 صباحا الی 12:00 ليل بتوقيت العراق',

    // أزرار إيقاف الدردشة
    openChatButton: 'فتح الدردشة',
    closeChatButton: 'إغلاق الدردشة',
    autoChatSchedule: 'جدولة الدردشة',
    chatOpenTime: 'وقت فتح الدردشة',
    chatCloseTime: 'وقت إغلاق الدردشة',
    saveSchedule: 'حفظ الجدولة',
    chatScheduleSaved: 'تم حفظ إعدادات الجدولة بنجاح',

    // اللغات
    arabic: 'العربية',
    english: 'الإنجليزية',
    kurdish: 'الكردية السورانية',
  },

  [LANGUAGES.EN]: {
    // General UI
    appName: 'Trust Market',
    login: 'Login',
    logout: 'Log Out',
    settings: 'Settings',
    chat: 'Chat',
    privateChats: 'Private Chats',
    luckyWheel: 'Wheel Spin',

    // Privacy Policy Translations
    'termsOfUseAndPolicies': 'Terms of Use and App Policies',
    'allowedAge': 'Allowed Age',
    'ageRequirement': 'Users must be 14 years or older to use the application.',
    'appPurpose': 'App Purpose',
    'appPurposeDescription1': 'This app is designed to facilitate good and safe transactions between sellers and buyers.',
    'appPurposeDescription2': 'This app takes no responsibility for fraud, scams, or theft of accounts/money outside the official system of the app.',
    'buyingAndSellingInApp': 'Buying and Selling in the App',
    'buyingAndSellingDescription1': 'Users can list their accounts for sale or search for accounts to buy through the available sections and groups.',
    'buyingAndSellingDescription2': 'It is prohibited to send money or account information to anyone in private chat without administration supervision.',
    'safeDealingMechanism': 'Safe Transaction Mechanism',
    'safeDealingDescription1': 'When an agreement is reached between the seller and buyer, the moderator is notified.',
    'safeDealingDescription2': 'The moderator (who has a blue badge and name appears in blue) creates a private group that includes the seller and buyer.',
    'safeDealingDescription3': 'Both parties are added to this group using their ID numbers.',
    'safeDealingDescription4': 'Within this group, the seller sends account information to the buyer after the moderator confirms receipt of the payment.',
    'paymentMechanism': 'Payment and Transfer Mechanism',
    'paymentDescription1': 'Money is sent from the buyer to the administration/moderator.',
    'paymentDescription2': 'After receiving the money, the seller sends account information within the group.',
    'paymentDescription3': 'The buyer changes the account information within one hour.',
    'paymentDescription4': 'After the change, it is verified by the moderator that the account has become fully owned by the buyer.',
    'paymentDescription5': 'Then, the administration transfers the money to the seller.',
    'problemOccurrence': 'In Case of a Problem',
    'problemDescription1': 'If the buyer forgets to change the account information, and the account is recovered by the seller after the transaction, the administration bears no responsibility.',
    'problemDescription2': 'In case one of the parties lies (such as claiming false information), the case is reviewed directly by the owner.',
    'problemDescription3': 'If the intention to scam is proven from one of the parties, their account will be permanently banned.',
    'problemDescription4': 'If the administration cannot determine who is at fault, the case is transferred to legal authorities.',
    'disputes': 'Disputes',
    'disputesDescription': 'If the app administration cannot determine who the scammer is:',
    'disputePoint1': 'The amount is frozen until investigations are completed.',
    'disputePoint2': 'The administration is allowed to temporarily hold the amount to ensure the rights of both parties.',
    'disputePoint3': 'If the matter is not resolved, the case is transferred to the law, and both parties must take legal procedures.',
    'chatBehavior': 'Chat Behavior',
    'chatBehaviorDescription1': 'It is strictly prohibited to use offensive words, insults, or manipulation within chats.',
    'chatBehaviorDescription2': 'Any violation leads to permanent or temporary ban depending on the severity of the case.',

    // Email Verification Page Translations
    'verified': 'Verified',
    'accountCreatedSuccessfully': 'Account created successfully!',
    'pleaseCheckInbox': 'Please check your <strong>inbox</strong> and click on the activation link to complete your account registration.',

    // Languages
    language: 'Language',
    selectLanguage: 'Select Language',
    arabic: 'Arabic',
    english: 'English',
    kurdish: 'Kurdish',

    // Login Page
    welcome: 'Welcome to Trust Market',
    appDescription: 'Safe platform for buying and selling',
    signIn: 'Sign In',
    email: 'Email',
    enterEmail: 'Enter your email',
    password: 'Password',
    enterPassword: 'Enter your password',
    noAccount: "Don't have an account?",
    signUp: 'Sign up',
    forgotPassword: 'Forgot your password?',
    agreeToTerms: "You agree to the app's policies",
    termsAndConditions: 'Terms and Conditions',
    name: 'Name',
    enterName: 'Enter your name',
    signInWithGoogle: 'Sign in with Google',
    resetPassword: 'Reset Password',
    sendResetLink: 'Send password reset link',
    backToSignIn: 'Back to Sign In',
    nameRequired: 'Name is required',
    emailRequired: 'Email is required',
    passwordMinLength: 'Password must be at least 6 characters',
    passwordRequired: 'Password is required',
    resetLinkSent: 'A reset link has been sent to your email',
    alreadyHaveAccount: 'Already have an account?',
    successfullySignedIn: 'Successfully signed in',
    mustAgreeToTerms: 'You must agree to the terms and conditions to continue',
    loginWithGoogle: 'Sign in with Google',
    loggingIn: 'Logging in...',

    // Settings
    support: 'Support',
    contactSupport: 'Contact Support',
    termsAndPolicies: 'Terms and Policies',
    rewardCodes: 'Check Reward Codes',
    close: 'Close',
    language: 'Language',
    selectLanguage: 'Select Language',

    // Lucky Wheel
    back: 'Back',
    availableTickets: 'Available Tickets',
    spin: 'Spin the Wheel',
    spinning: 'Spinning...',
    completeTasksForTickets: 'Complete tasks to get tickets',
    dailyTasks: 'Daily Tasks',
    completeTask: 'Complete Task',
    taskCompleted: 'Completed',
    tickets: 'tickets',
    dailyLogin: 'Daily Login',
    dailyLoginDesc: 'Log in to the app every day',
    ticket: 'Ticket',
    // Lucky Wheel Rewards
    nothingReward: 'You won: Nothing',
    spinAgainReward: 'You won: Spin Again',
    freeAdReward: 'You won: Free Ad',
    adminRoomReward: 'You won: Admin Room',
    spinAgainTicketReturned: 'Your spin ticket has been returned',
    rewardCode: 'Reward Code',
    congratulations: 'Congratulations!',
    keepCodeForSupport: 'Keep this code and present it to support to claim your prize',
    close: 'Close',
    loading: 'Loading...',
    manageWheel: 'Manage Wheel',
    shareApp: 'Share App',
    shareAppDesc: 'Share the app link with your friends',
    taskCompletedSuccess: 'Task completed! You got',
    errorCompletingTask: 'Error completing task',

    // Chat
    send: 'Send',
    reply: 'Reply',
    like: 'Like',
    delete: 'Delete',
    mute: 'Mute',
    ban: 'Ban',
    likeMessage: 'Like Message',
    typeMessage: 'Type a new message here...',
    blockUser: 'Block User',
    blockUserInChat: 'Block user in this chat',
    deleteChat: 'Delete Chat',

    // Groups
    roomName: 'Group Name',
    description: 'Description',
    roomDescriptionOptional: 'Room description is optional',
    roomNameRequired: 'Group name is required',
    roomType: 'Group Type',
    privateRoomDescription: 'Private group: only visible to selected users by the admin',
    ownerOnlyPublicRooms: 'Note: Only the owner can create public rooms',
    allowedUsers: 'Allowed User ID(s)',
    createNewChatRoom: 'Create New Chat Room',
    availableChatRooms: 'Available Chat Rooms',
    groupInfo: 'Group Info',
    restrictedGroup: 'This group is restricted. Only the owner can send messages.',
    memberSince: 'Member since',
    createPrivateRoom: 'Create Private Room',
    totalUsers: 'Total Users',
    activeUsers: 'Active Users',
    deleteGroup: 'Delete Group',
    confirmGroupDeletion: 'Confirm group deletion?',
    wasSuccessful: 'Was the sale successful?',
    cancel: 'Cancel',
    no: 'No',
    yes: 'Yes',

    // Chat Closing
    closeChatButton: 'Close Chat',
    openChatButton: 'Open Chat',
    chatClosedMessage: 'Chat is closed for today. It will open from 9:00 AM to 12:00 AM Iraq time',
    autoChatSchedule: 'Auto Schedule',
    chatOpenTime: 'Chat Open Time',
    chatCloseTime: 'Chat Close Time',
    saveSchedule: 'Save Schedule',
    chatScheduleSaved: 'Chat schedule saved successfully',

    // Profile
    nickname: 'Nickname',
    changeNamePhotoWeekly: 'You can change the name and photo once per week',
    chooseNewProfilePicture: 'Choose a new profile picture',
    profile: 'Profile',

    // Photos
    uploadPhotos: 'Upload Photos',
    fileSizeLimit: '* Only files smaller than 5MB are allowed',
    sendToChat: 'Send to Chat',

    // Notifications
    notifications: 'Notifications',
    noNotifications: 'No notifications',
    loading: 'Loading...',
    replyNotification: 'Reply to your message',
    privateMessageNotification: 'New private message',
    ownerMessageNotification: 'Message from owner',
    mentionNotification: 'You were mentioned',
    chatClosedMessage: 'Chat is closed for today. It will open from 9:00 AM to 12:00 AM Iraq time',

    // Chat Close Buttons
    openChatButton: 'Open Chat',
    closeChatButton: 'Close Chat',
    autoChatSchedule: 'Chat Schedule',
    chatOpenTime: 'Chat Open Time',
    chatCloseTime: 'Chat Close Time',
    saveSchedule: 'Save Schedule',
    chatScheduleSaved: 'Schedule settings saved successfully',

    // Languages
    arabic: 'Arabic',
    english: 'English',
    kurdish: 'Kurdish Sorani',
  },

  [LANGUAGES.KU]: {
    // واجهەی گشتی
    appName: 'Trust Market',
    login: 'چوونە ژوورەوە',
    logout: 'چونەدەرەوە لە ئەکاونت',
    settings: 'ڕێکخستنەکان',
    chat: 'چات',
    privateChats: 'چاتی تایبەت',
    luckyWheel: 'چەڕخ بەخت',

    // زمانەکان
    language: 'زمان',
    selectLanguage: 'زمان هەڵبژێرە',
    arabic: 'عەرەبی',
    english: 'ئینگلیزی',
    kurdish: 'کوردی',

    // پەڕەی چوونە ژوورەوە
    welcome: 'بەخێربێیت بۆ Trust Market',
    appDescription: 'پلاتفۆرمی کڕین و فرۆشتن بە شێوەیەکی پارێزراو',
    signIn: 'چونەژورەوە',
    email: 'ئیمێڵ',
    enterEmail: 'ئیمەیڵ بنوسە',
    password: 'پاسبۆرد',
    enterPassword: 'پاسبۆرد بنوسە',
    noAccount: 'ئەکاونتت نیە؟',
    signUp: 'ئێستا دروستی بکە',
    forgotPassword: 'پاسبۆردت بیرچووە؟',
    agreeToTerms: 'تۆ ڕازی دەبی بە یاسا و مەرجەکانی بەرنامە',
    termsAndConditions: 'مەرج و ڕێساکان',
    name: 'ناو',
    enterName: 'ناوت بنوسە',
    signInWithGoogle: 'چونەژورەوە لە ڕێگای گۆگل',
    resetPassword: 'گەڕاندنەوەی پاسبۆرد',
    sendResetLink: 'ناردنی پاسبۆرد بۆ ئیمەیڵ',
    backToSignIn: 'گەڕانەوە بۆ چونەژورەوە',
    nameRequired: 'ناو گرنگە',
    emailRequired: 'ئیمەیڵ پێویستە',
    passwordMinLength: 'پاسبۆرد دەبێ لە 6 ژمارە کەمتر نەبێت',
    passwordRequired: 'پاسبۆرد پێویستە',
    resetLinkSent: 'لینکی پاسبۆرد نوێکردنەوە نێردرا بۆ ئیمەیڵەکەت',
    alreadyHaveAccount: 'ئەکاونتت هەیە؟',
    successfullySignedIn: 'بەسەرکەوتووی هاتیتە ژورەوە',
    mustAgreeToTerms: 'پێویستە ڕازی بیت بە مەرج و ڕێساکان بۆ بەردەوامبوون',
    loginWithGoogle: 'چوونە ژوورەوە بە گووگڵ',
    loggingIn: 'چوونە ژوورەوە...',

    // ڕێکخستنەکان
    support: 'پشتگیری',
    contactSupport: 'پەیوەندی بە پشتگیری',
    termsAndPolicies: 'مەرج و سیاسەتەکان',
    rewardCodes: 'پشکنینی کۆدەکانی خەڵات',
    close: 'داخستن',
    language: 'زمان',
    selectLanguage: 'زمان هەڵبژێرە',

    // چەرخی بەخت
    back: 'گەڕانەوە',
    availableTickets: 'کارتەکان',
    spin: 'سوڕاندن',
    spinning: 'چەرخاندن...',
    completeTasksForTickets: 'ئەرکەکان تەواو بکە بۆ وەرگرتنی بلیت',
    dailyTasks: 'چاڵنجی ڕۆژانە',
    completeTask: 'تەواوکردنی ئەرک',
    taskCompleted: 'تەواوکرا',
    tickets: 'بلیت',
    dailyLogin: 'هەموو ڕۆژێک وەرە ناو بەرنامە',
    dailyLoginDesc: 'هەموو ڕۆژ نوێ دەبێتەوە',
    ticket: 'کارت',
    // خەڵاتەکانی چەرخی بەخت
    nothingReward: 'بردتەوە: هیچ',
    spinAgainReward: 'بردتەوە: دووبارە سوڕاندنەوە',
    freeAdReward: 'بردتەوە: ڕیکلامی بەخۆڕایی',
    adminRoomReward: 'بردتەوە: ژووری ئەدمین',
    spinAgainTicketReturned: 'کارتی سوڕاندنەوەکەت گەڕێندرایەوە',
    rewardCode: 'کۆدی خەڵات',
    congratulations: 'پیرۆزە!',
    keepCodeForSupport: 'ئەم کۆدە هەڵبگرە و پێشکەشی پشتگیری بکە بۆ وەرگرتنی خەڵاتەکەت',
    close: 'داخستن',
    loading: 'بارکردن...',
    manageWheel: 'بەڕێوەبردنی چەرخ',
    shareApp: 'هاوبەشکردنی ئەپ',
    shareAppDesc: 'لینکی ئەپەکە لەگەڵ هاوڕێکانت هاوبەش بکە',
    taskCompletedSuccess: 'ئەرکەکە تەواو بوو! تۆ بەدەستت هێنا',
    errorCompletingTask: 'هەڵە لە تەواوکردنی ئەرکەکەدا',

    // چات
    send: 'ناردن',
    reply: 'وەڵامدانەوە',
    like: 'بەدڵبوون',
    delete: 'سڕینەوە',
    mute: 'میووت کردن',
    ban: 'قەدەغەکردن',
    likeMessage: 'بەدڵ بوون',
    typeMessage: 'بنوسە',
    blockUser: 'بلۆک',
    blockUserInChat: 'بلۆک کردن لەم نامەیە',
    deleteChat: 'سڕینەوەی ئەم چاتە',

    // ئاگادارکردنەوەکان
    notifications: 'ئاگادارکردنەوەکان',
    noNotifications: 'هیچ ئاگادارکردنەوەیەک نییە',
    loading: 'چاوەڕێ بکە...',
    replyNotification: 'وەڵامی نامەکەت',
    privateMessageNotification: 'نامەی تایبەتی نوێ',
    ownerMessageNotification: 'نامە لە خاوەنەوە',
    mentionNotification: 'ئاماژەت پێکرا',

    // گروپەکان
    roomName: 'ناوی گروپ',
    description: 'پێناسە',
    roomDescriptionOptional: 'دەتوانی نەینوسیت گرنگ نیە',
    roomNameRequired: 'ناوی گروپ گرنگە',
    roomType: 'جۆری گروپ',
    privateRoomDescription: 'ئەم گروپە تەنیا لای ئەوانە دیارە کە لەلاین ئادمین زیاد دەکرێنە گروپ',
    ownerOnlyPublicRooms: 'تەنیا بەڕێوبەر دەتوانێ گروپێ گشتی دابنێت',
    allowedUsers: 'ئایدی کەسەکان لێرە دانێ',
    createNewChatRoom: 'دروستکردنی گروپی نوێ',
    availableChatRooms: 'ئەو گروپانەی بەردەستن',
    groupInfo: 'زانیاری گروپ',
    restrictedGroup: 'ئەم پەیجە تایبەتە بە ڕێکلام و هەواڵەکانی بەرنامە',
    memberSince: 'بەکارهێنەرە لە',
    createPrivateRoom: 'ناردنی نامە',
    totalUsers: 'ڕێژەی بەکارهێنەر',
    activeUsers: 'ئۆنڵاین',
    deleteGroup: 'سڕینەوەی گروپ',
    confirmGroupDeletion: 'دڵنیای لە سڕینەوەی گروپ؟',
    wasSuccessful: 'ئایا مامەڵەکە سەرکەوتوو بوو؟',
    cancel: 'لابردن',
    no: 'نەخێر',
    yes: 'بەڵێ',

    // داخستنی چات
    closeChatButton: 'داخستنی چات',
    openChatButton: 'کردنەوەی چات',
    chatClosedMessage: 'چات داخراوە بۆ ئەمڕۆ. لە کاتژمێر 9:00 بەیانی بۆ 12:00 شەو بە کاتی عێراق دەکرێتەوە',
    autoChatSchedule: 'خشتەی خۆکار',
    chatOpenTime: 'کاتی کردنەوەی چات',
    chatCloseTime: 'کاتی داخستنی چات',
    saveSchedule: 'پاشەکەوتکردنی خشتە',
    chatScheduleSaved: 'خشتەی چات بە سەرکەوتوویی پاشەکەوت کرا',

    // پڕۆفایل
    nickname: 'ناوی تۆ',
    changeNamePhotoWeekly: 'لە هەفتەیەکدا دەتوانی یەکجار ناو لەگەڵ وێنە بگۆڕیت',
    chooseNewProfilePicture: 'وێنەیەک هەڵبژێرە بۆ پڕۆفایل',
    profile: 'پڕۆفال',

    // وێنەکان
    uploadPhotos: 'دەست بنێ بە ئەپڵۆد',
    fileSizeLimit: 'نابێت کێشی وێنەکە لە 5MB زیاتر بێت',
    sendToChat: 'ناردن',

    // ئاگادارکردنەوەکان
    replyNotification: 'وەڵامی نامەکەت',
    privateMessageNotification: 'نامەی تایبەتی نوێ',
    ownerMessageNotification: 'نامە لە خاوەنەوە',
    chatClosedMessage: 'چات داخراوە بۆ ئەمڕۆ. لە کاتژمێر 9:00 بەیانی بۆ 12:00 شەو بە کاتی عێراق دەکرێتەوە',

    // دوگمەکانی داخستنی چات
    openChatButton: 'کردنەوەی چات',
    closeChatButton: 'داخستنی چات',
    autoChatSchedule: 'خشتەی چات',
    chatOpenTime: 'کاتی کردنەوەی چات',
    chatCloseTime: 'کاتی داخستنی چات',
    saveSchedule: 'پاشەکەوتکردنی خشتە',
    chatScheduleSaved: 'ڕێکخستنەکانی خشتە بە سەرکەوتوویی پاشەکەوت کران',

    // زمانەکان
    arabic: 'عەرەبی',
    english: 'ئینگلیزی',
    kurdish: 'کوردی سۆرانی',
  }
};

// إنشاء سياق اللغة
const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  // استخدام اللغة المخزنة في localStorage أو استخدام العربية كلغة افتراضية
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('language');
    return savedLanguage || LANGUAGES.AR;
  });

  // تحديث اللغة في localStorage عند تغييرها
  useEffect(() => {
    localStorage.setItem('language', currentLanguage);

    // تعيين اللغة فقط
    document.documentElement.lang = currentLanguage;

    // الحفاظ على اتجاه LTR دائمًا بغض النظر عن اللغة (حتى مع العربية والكردية)
    document.documentElement.dir = 'ltr';

    // إزالة أي تأثيرات CSS متعلقة بالاتجاه RTL وتطبيق LTR
    document.body.classList.remove('rtl-layout');
    document.body.classList.add('ltr-layout');

    // إضافة CSS لمنع انعكاس الشاشة حتى مع اللغات RTL
    const style = document.createElement('style');
    style.id = 'direction-override';
    style.innerHTML = `
      body, .rs-container, .rs-content, .rs-panel, .rs-drawer, .rs-modal {
        direction: ltr !important;
      }

      /* تطبيق محاذاة النص فقط بناءً على اللغة دون تغيير اتجاه العناصر */
      .ar-text, .ku-text {
        text-align: right;
      }
    `;

    // إزالة أي أنماط سابقة لتجنب التكرار
    const oldStyle = document.getElementById('direction-override');
    if (oldStyle) {
      oldStyle.remove();
    }

    document.head.appendChild(style);

  }, [currentLanguage]);

  // دالة لتغيير اللغة
  const changeLanguage = (language) => {
    if (Object.values(LANGUAGES).includes(language)) {
      setCurrentLanguage(language);
    }
  };

  // الحصول على الترجمات للغة الحالية
  const t = (key) => {
    // البحث عن الترجمة في قاموس الترجمات
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      return translations[currentLanguage][key];
    }

    // إذا كانت اللغة الإنجليزية، استخدم المفتاح كما هو
    if (currentLanguage === LANGUAGES.EN) {
      return key;
    }

    // للغات الأخرى، استخدم ترجمة Google التلقائية
    // ملاحظة: هذا تمثيل رمزي فقط، في التطبيق الحقيقي يمكن استخدام API ترجمة Google
    // أو خدمة ترجمة أخرى لترجمة النصوص تلقائيًا

    // محاكاة ترجمة Google التلقائية (في التطبيق الحقيقي، سيتم استبدال هذا بطلب API)
    const autoTranslations = {
      [LANGUAGES.AR]: {
        // ترجمات تلقائية للعربية - واجهة المستخدم العامة
        'Chat App': 'تطبيق الدردشة',
        'Settings': 'الإعدادات',
        'Language': 'اللغة',
        'Profile': 'الملف الشخصي',
        'Logout': 'تسجيل الخروج',
        'Private Chats': 'الدردشات الخاصة',
        'Wheel Spin': 'عجلة الحظ',
        'Select Language': 'اختر اللغة',
        'Arabic': 'العربية',
        'English': 'الإنجليزية',
        'Kurdish': 'الكردية',
        'Close': 'إغلاق',

        // ترجمات تسجيل الدخول
        'Welcome to Chat App': 'مرحبًا بك في تطبيق الدردشة',
        'Advanced and easy-to-use chat platform': 'منصة دردشة متطورة وسهلة الاستخدام',
        'Sign In': 'تسجيل الدخول',
        'Email': 'البريد الإلكتروني',
        'Enter your email': 'أدخل بريدك الإلكتروني',
        'Password': 'كلمة المرور',
        'Enter your password': 'أدخل كلمة المرور',
        'Don\'t have an account?': 'ليس لديك حساب؟',
        'Sign up': 'التسجيل',
        'Forgot your password?': 'نسيت كلمة المرور؟',
        'You agree to the app\'s policies': 'أنت توافق على سياسات التطبيق',
        'Terms and Conditions': 'الشروط والأحكام',
        'Name': 'الاسم',
        'Enter your name': 'أدخل اسمك',
        'Sign in with Google': 'تسجيل الدخول باستخدام جوجل',

        // ترجمات المجموعات والدردشة
        'Group Name': 'اسم المجموعة',
        'Description': 'الوصف',
        'Room description is optional': 'وصف الغرفة اختياري',
        'Group name is required': 'اسم المجموعة مطلوب',
        'Group Type': 'نوع المجموعة',
        'Private group: only visible to selected users by the admin': 'المجموعة الخاصة: مرئية فقط للمستخدمين المحددين من قبل المشرف',
        'Note: Only the owner can create public rooms': 'ملاحظة: فقط المالك يمكنه إنشاء غرف عامة',
        'Allowed User ID(s)': 'معرفات المستخدمين المسموح بها',
        'Create New Chat Room': 'إنشاء غرفة دردشة جديدة',
        'Available Chat Rooms': 'غرف الدردشة المتاحة',
        'Group Info': 'معلومات المجموعة',
        'This group is restricted. Only the owner can send messages.': 'هذه المجموعة مقيدة. فقط المالك يمكنه إرسال الرسائل.',
        'Member since': 'عضو منذ',
        'Create Private Room': 'إنشاء غرفة خاصة',
        'Total Users': 'إجمالي المستخدمين',
        'Active Users': 'المستخدمين النشطين',
        'Delete Group': 'حذف المجموعة',
        'Confirm group deletion?': 'تأكيد حذف المجموعة؟',
        'Cancel': 'إلغاء',
        'No': 'لا',
        'Yes': 'نعم',
        'Send': 'إرسال',
        'Reply': 'رد',
        'Like': 'إعجاب',
        'Delete': 'حذف',
        'Mute': 'كتم الصوت',
        'Ban': 'حظر',
        'Like Message': 'الإعجاب بالرسالة',
        'Type a new message here...': 'اكتب رسالة جديدة هنا...',
        'Block User': 'حظر المستخدم',
        'Block user in this chat': 'حظر المستخدم في هذه الدردشة',
        'Delete Chat': 'حذف الدردشة',

        // ترجمات الملف الشخصي
        'Nickname': 'الاسم المستعار',
        'You can change the name and photo once per week': 'يمكنك تغيير الاسم والصورة مرة واحدة في الأسبوع',
        'Choose a new profile picture': 'اختيار صورة شخصية جديدة',

        // ترجمات عجلة الحظ
        'Back': 'العودة',
        'Available Tickets': 'التذاكر المتاحة',
        'Spin the Wheel': 'تدوير العجلة',
        'Spinning...': 'جاري التدوير...',
        'Complete tasks to get tickets': 'أكمل المهام للحصول على تذاكر',
        'Daily Tasks': 'المهام اليومية',
        'Complete Task': 'إكمال المهمة',
        'Completed': 'تم الإكمال',
        'tickets': 'تذاكر',
        'Daily Login': 'تسجيل الدخول اليومي',
        'Log in to the app every day': 'قم بتسجيل الدخول إلى التطبيق كل يوم',
        'Ticket': 'تذكرة',

        // ترجمات تحميل الصور
        'Upload Photos': 'رفع صور',
        '* Only files smaller than 5MB are allowed': '* يسمح فقط بالملفات التي حجمها أقل من 5 ميجابايت',
        'Send to Chat': 'إرسال إلى الدردشة',

        // ترجمات أخرى
        'Support': 'الدعم',
        'Contact Support': 'اتصل بالدعم',
        'Terms and Policies': 'الشروط والسياسات',
        'Check Reward Codes': 'فحص أكواد الجوائز',
        'Was the sale successful?': 'هل نجحت عملية البيع؟',
        'Public': 'عام',
        'Private': 'خاص',
        'Create': 'إنشاء',
        'Save': 'حفظ',
        'Edit': 'تعديل',
        'Update': 'تحديث',
        'Loading...': 'جاري التحميل...',
        'Search': 'بحث',
        'Online': 'متصل',
        'Offline': 'غير متصل',
        'Members': 'الأعضاء',
        'Admin': 'مشرف',
        'Owner': 'مالك',
        'User': 'مستخدم',
        'Users': 'المستخدمين',
        'Banned': 'محظور',
        'Muted': 'مكتوم',
        'Unban': 'إلغاء الحظر',
        'Unmute': 'إلغاء الكتم',
        'Confirm': 'تأكيد',
        'Are you sure?': 'هل أنت متأكد؟',
        'Warning': 'تحذير',
        'Error': 'خطأ',
        'Success': 'نجاح',
        'Information': 'معلومات',
        'Notification': 'إشعار',
        'New message': 'رسالة جديدة',
        'New reply': 'رد جديد',
        'Someone replied to your message': 'قام شخص ما بالرد على رسالتك',
        'Someone liked your message': 'أعجب شخص ما برسالتك',
        'You have been banned': 'تم حظرك',
        'You have been muted': 'تم كتم صوتك',
        'Your account has been banned': 'تم حظر حسابك',
        'Your account has been muted': 'تم كتم صوت حسابك',
        'Your ban has been lifted': 'تم رفع الحظر عنك',
        'Your mute has been lifted': 'تم رفع الكتم عنك',
        'Your account has been unbanned': 'تم إلغاء حظر حسابك',
        'Your account has been unmuted': 'تم إلغاء كتم صوت حسابك',

        // ترجمات الصفحة الرئيسية
        'selectChatRoomToStart': 'اختر غرفة دردشة للبدء',
        'availableChatRooms': 'غرف الدردشة المتاحة',
        'loading': 'جاري التحميل...',
        'records': 'سجلات',
        'adminManagement': 'إدارة المشرفين',
        'luckyWheelManagement': 'إدارة عجلة الحظ',
        'checkRewardCodes': 'فحص أكواد الجوائز',
        'hide': 'إخفاء',
        'errorRoomIdNotFound': 'خطأ: معرف الغرفة غير موجود',
        'errorRoomNotFound': 'خطأ: الغرفة غير موجودة',
        'errorDeletingGroup': 'خطأ في حذف المجموعة',
        'groupHiddenFromHomepage': 'تم إخفاء المجموعة من الصفحة الرئيسية',
        'errorHidingGroup': 'خطأ في إخفاء المجموعة',
        'confirmGroupDeletion': 'تأكيد حذف المجموعة',
        'wasTheSaleSuccessful': 'هل نجحت عملية البيع؟',
        'confirmHidingGroup': 'تأكيد إخفاء المجموعة',
        'areYouSureHideGroup': 'هل أنت متأكد من إخفاء المجموعة',
        'fromHomepage': 'من الصفحة الرئيسية',
        'noteShowGroupAgainFromRecords': 'ملاحظة: يمكنك إظهار المجموعة مرة أخرى من صفحة السجلات',
        'groupDeletedSuccessfully': 'تم حذف المجموعة بنجاح',
        'attachedFile': 'ملف مرفق',
        'noMessagesYet': 'لا توجد رسائل بعد...',

        // ترجمات معلومات المجموعة وتعديلها
        'updatedSuccessfully': 'تم التحديث بنجاح',
        'errorUpdatingRoomPrivacy': 'خطأ في تحديث خصوصية الغرفة',
        'allowedUsersListUpdatedSuccessfully': 'تم تحديث قائمة المستخدمين المسموح لهم بنجاح',
        'errorUpdatingUsersList': 'خطأ في تحديث قائمة المستخدمين',
        'errorDeletingChat': 'خطأ في حذف الدردشة',
        'chatDeletedSuccessfully': 'تم حذف الدردشة بنجاح',
        'editRoom': 'تعديل الغرفة',
        'editGroup': 'تعديل المجموعة',
        'roomNameRequired': 'اسم الغرفة لا يمكن أن يكون فارغًا',
        'roomDescriptionRequired': 'وصف الغرفة لا يمكن أن يكون فارغًا',
        'privateRoom': 'غرفة خاصة',
        'publicRoom': 'غرفة عامة',
        'privateRoomDescription': 'الغرفة الخاصة تظهر فقط للمستخدمين المحددين',
        'publicRoomDescription': 'الغرفة العامة تظهر لجميع المستخدمين',
        'allowedUser1': 'المستخدم المسموح له 1',
        'allowedUser2': 'المستخدم المسموح له 2',
        'allowedUser3': 'المستخدم المسموح له 3',
        'allowedUser4': 'المستخدم المسموح له 4',
        'enterUserID': 'أدخل معرف المستخدم (UID)',
        'updateUsersList': 'تحديث قائمة المستخدمين',
        'preferEnterUserID': 'يفضل إدخال معرف المستخدم (UID) للإضافة إلى الغرفة',
        'getUIDFromProfile': 'يمكنك الحصول على معرف المستخدم (UID) من صفحة الملف الشخصي للمستخدم',
        'noDescription': 'لا يوجد وصف',
        'confirmDeleteChat': 'تأكيد حذف الدردشة',
        'areYouSureDeleteChat': 'هل أنت متأكد من حذف هذه الدردشة؟ سيتم حذف جميع الرسائل نهائياً.',
        'errorNoPermissionToDeleteGroup': 'خطأ: ليس لديك صلاحيات لحذف هذه المجموعة',

        // ترجمات سياسات الخصوصية
        'termsOfUseAndPolicies': 'شروط الاستخدام وسياسات التطبيق',
        'allowedAge': 'العمر المسموح',
        'ageRequirement': 'يُشترط أن يكون عمر المستخدم 14 سنة فما فوق لاستخدام التطبيق.',
        'appPurpose': 'الهدف من التطبيق',
        'appPurposeDescription1': 'تم تصميم التطبيق لتسهيل التعامل الجيد والآمن بين البائع والمشتري.',
        'appPurposeDescription2': 'هذا التطبيق لا يتحمل أي مسؤولية في حال حدوث نصب، احتيال، أو سرقة حسابات/أموال خارج نطاق النظام الرسمي للتطبيق.',
        'buyingAndSellingInApp': 'البيع والشراء داخل التطبيق',
        'buyingAndSellingDescription1': 'يمكن للمستخدمين عرض حساباتهم للبيع أو البحث عن حسابات للشراء من خلال الأقسام والمجموعات المتاحة.',
        'buyingAndSellingDescription2': 'يمنع إرسال المال أو معلومات الحساب لأي شخص داخل الدردشة الخاصة دون إشراف من الإدارة.',
        'safeDealingMechanism': 'آلية التعامل الآمن',
        'safeDealingDescription1': 'عندما يتم الاتفاق بين البائع والمشتري، يتم إبلاغ المشرف.',
        'safeDealingDescription2': 'المشرف (الذي يملك علامة زرقاء واسمه يظهر باللون الأزرق) يقوم بإنشاء مجموعة خاصة تضم البائع والمشتري.',
        'safeDealingDescription3': 'يتم إضافة الطرفين إلى هذه المجموعة باستخدام رقم الـ ID الخاص بهم.',
        'safeDealingDescription4': 'داخل هذه المجموعة، يقوم البائع بإرسال معلومات الحساب إلى المشتري بعد أن يؤكد المشرف استلام المبلغ.',
        'paymentMechanism': 'آلية الدفع والتحويل',
        'paymentDescription1': 'يتم إرسال المال من المشتري إلى الإدارة/المشرف.',
        'paymentDescription2': 'بعد استلام المال، يقوم البائع بإرسال معلومات الحساب داخل المجموعة.',
        'paymentDescription3': 'يقوم المشتري بتغيير معلومات الحساب خلال ساعة واحدة.',
        'paymentDescription4': 'بعد التغيير، يتم التحقق من طرف المشرف بأن الحساب أصبح ملكًا للمشتري بشكل كامل.',
        'paymentDescription5': 'بعدها، تقوم الإدارة بتحويل المال للبائع.',
        'problemOccurrence': 'في حال حدوث مشكلة',
        'problemDescription1': 'إذا نسى المشتري تغيير معلومات الحساب، وتم استرجاع الحساب من البائع بعد المعاملة، لا تتحمل الإدارة أي مسؤولية.',
        'problemDescription2': 'في حال كذب أحد الطرفين (مثل ادعاء معلومات مزيفة)، يتم مراجعة الحالة من قبل المالك مباشرة.',
        'problemDescription3': 'إذا ثبتت النية للنصب من أحد الأطراف، يتم حظر حسابه نهائيًا.',
        'problemDescription4': 'إذا لم تستطع الإدارة تحديد من هو المخطئ، يتم تحويل القضية إلى الجهات القانونية.',
        'disputes': 'النزاعات',
        'disputesDescription': 'إذا لم تتمكن إدارة التطبيق من تحديد من هو النصاب:',
        'disputePoint1': 'يتم تجميد المبلغ حتى تنتهي التحقيقات.',
        'disputePoint2': 'يُسمح للإدارة بحجز المبلغ مؤقتًا لضمان حقوق الطرفين.',
        'disputePoint3': 'إذا لم يُحسم الأمر، يتم تحويل القضية للقانون، ويجب على الطرفين اتخاذ الإجراءات القانونية.',
        'chatBehavior': 'السلوك في الدردشة',
        'chatBehaviorDescription1': 'يُمنع تمامًا استخدام كلمات نابية، سب، أو تلاعب داخل الدردشات.',
        'chatBehaviorDescription2': 'أي مخالفة تؤدي إلى حظر دائم أو مؤقت بحسب شدة الحالة.',

        // ترجمات صفحة تأكيد البريد الإلكتروني
        'verified': 'تم التحقق',
        'accountCreatedSuccessfully': 'تم إنشاء الحساب بنجاح!',
        'pleaseCheckInbox': 'يُرجى التوجه إلى <strong>بريدك الوارد</strong> والنقر على رابط التفعيل لإكمال تسجيل الحساب.',
      },
      [LANGUAGES.KU]: {
        // ترجمات تلقائية للكردية - واجهة المستخدم العامة
        'Chat App': 'ئەپی چات',
        'Settings': 'ڕێکخستنەکان',
        'Language': 'زمان',
        'Profile': 'پڕۆفایل',
        'Logout': 'چوونە دەرەوە',
        'Private Chats': 'چاتی تایبەت',
        'Wheel Spin': 'سوڕاندن',
        'Select Language': 'زمان هەڵبژێرە',
        'Arabic': 'عەرەبی',
        'English': 'ئینگلیزی',
        'Kurdish': 'کوردی',
        'Close': 'داخستن',

        // ترجمات تسجيل الدخول
        'Welcome to Chat App': 'بەخێربێیت بۆ ئەپی چات',
        'Advanced and easy-to-use chat platform': 'پلاتفۆرمی چاتی پێشکەوتوو و ئاسان بۆ بەکارهێنان',
        'Sign In': 'چونەژورەوە',
        'Email': 'ئیمێڵ',
        'Enter your email': 'ئیمەیڵ بنوسە',
        'Password': 'پاسبۆرد',
        'Enter your password': 'پاسبۆرد بنوسە',
        'Don\'t have an account?': 'ئەکاونتت نیە؟',
        'Sign up': 'ئێستا دروستی بکە',
        'Forgot your password?': 'پاسبۆردت بیرچووە؟',
        'You agree to the app\'s policies': 'تۆ ڕازی دەبی بە یاسا و مەرجەکانی بەرنامە',
        'Terms and Conditions': 'مەرج و ڕێساکان',
        'Name': 'ناو',
        'Enter your name': 'ناوت بنوسە',
        'Sign in with Google': 'چونەژورەوە لە ڕێگای گۆگل',

        // ترجمات المجموعات والدردشة
        'Group Name': 'ناوی گروپ',
        'Description': 'پێناسە',
        'Room description is optional': 'دەتوانی نەینوسیت گرنگ نیە',
        'Group name is required': 'ناوی گروپ گرنگە',
        'Group Type': 'جۆری گروپ',
        'Private group: only visible to selected users by the admin': 'ئەم گروپە تەنیا لای ئەوانە دیارە کە لەلاین ئادمین زیاد دەکرێنە گروپ',
        'Note: Only the owner can create public rooms': 'تەنیا بەڕێوبەر دەتوانێ گروپێ گشتی دابنێت',
        'Allowed User ID(s)': 'ئایدی کەسەکان لێرە دانێ',
        'Create New Chat Room': 'دروستکردنی گروپی نوێ',
        'Available Chat Rooms': 'ئەو گروپانەی بەردەستن',
        'Group Info': 'زانیاری گروپ',
        'This group is restricted. Only the owner can send messages.': 'ئەم پەیجە تایبەتە بە ڕێکلام و هەواڵەکانی بەرنامە',
        'Member since': 'بەکارهێنەرە لە',
        'Create Private Room': 'ناردنی نامە',
        'Total Users': 'ڕێژەی بەکارهێنەر',
        'Active Users': 'ئۆنڵاین',
        'Delete Group': 'سڕینەوەی گروپ',
        'Confirm group deletion?': 'دڵنیای لە سڕینەوەی گروپ؟',
        'Cancel': 'لابردن',
        'No': 'نەخێر',
        'Yes': 'بەڵێ',
        'Send': 'ناردن',
        'Reply': 'وەڵامدانەوە',
        'Like': 'بەدڵبوون',
        'Delete': 'سڕینەوە',
        'Mute': 'میووت کردن',
        'Ban': 'بلۆک',
        'Like Message': 'بەدڵ بوون',
        'Type a new message here...': 'بنوسە',
        'Block User': 'بلۆک',
        'Block user in this chat': 'بلۆک کردن لەم نامەیە',
        'Delete Chat': 'سڕینەوەی ئەم چاتە',

        // ترجمات الملف الشخصي
        'Nickname': 'ناوی تۆ',
        'You can change the name and photo once per week': 'لە هەفتەیەکدا دەتوانی یەکجار ناو لەگەڵ وێنە بگۆڕیت',
        'Choose a new profile picture': 'وێنەیەک هەڵبژێرە بۆ پڕۆفایل',

        // ترجمات عجلة الحظ
        'Back': 'گەڕانەوە',
        'Available Tickets': 'کارتەکان',
        'Spin the Wheel': 'سوڕاندن',
        'Spinning...': 'چەرخاندن...',
        'Complete tasks to get tickets': 'ئەرکەکان تەواو بکە بۆ وەرگرتنی بلیت',
        'Daily Tasks': 'چاڵنجی ڕۆژانە',
        'Complete Task': 'تەواوکردنی ئەرک',
        'Completed': 'تەواوکرا',
        'tickets': 'بلیت',
        'Daily Login': 'هەموو ڕۆژێک وەرە ناو بەرنامە',
        'Log in to the app every day': 'هەموو ڕۆژ نوێ دەبێتەوە',
        'Ticket': 'کارت',

        // ترجمات تحميل الصور
        'Upload Photos': 'دەست بنێ بە ئەپڵۆد',
        '* Only files smaller than 5MB are allowed': 'نابێت کێشی وێنەکە لە 5MB زیاتر بێت',
        'Send to Chat': 'ناردن',

        // ترجمات أخرى
        'Support': 'پشتگیری',
        'Contact Support': 'پەیوەندی بە پشتگیری',
        'Terms and Policies': 'مەرج و سیاسەتەکان',
        'Check Reward Codes': 'پشکنینی کۆدەکانی خەڵات',
        'Was the sale successful?': 'ئایا مامەڵەکە سەرکەوتوو بوو؟',
        'Public': 'گشتی',
        'Private': 'تایبەت',
        'Create': 'دروستکردن',
        'Save': 'پاشەکەوت',
        'Edit': 'دەستکاری',
        'Update': 'نوێکردنەوە',
        'Loading...': 'چاوەڕێ بکە...',
        'Search': 'گەڕان',
        'Online': 'ئۆنلاین',
        'Offline': 'ئۆفلاین',
        'Members': 'ئەندامان',
        'Admin': 'بەڕێوەبەر',
        'Owner': 'خاوەن',
        'User': 'بەکارهێنەر',
        'Users': 'بەکارهێنەران',
        'Banned': 'بلۆک کراو',
        'Muted': 'بێدەنگ کراو',
        'Unban': 'لابردنی بلۆک',
        'Unmute': 'لابردنی بێدەنگی',
        'Confirm': 'دڵنیاکردنەوە',
        'Are you sure?': 'دڵنیای؟',
        'Warning': 'ئاگاداری',
        'Error': 'هەڵە',
        'Success': 'سەرکەوتوو',
        'Information': 'زانیاری',
        'Notification': 'ئاگادارکردنەوە',
        'New message': 'نامەی نوێ',
        'New reply': 'وەڵامی نوێ',
        'Someone replied to your message': 'کەسێک وەڵامی نامەکەی تۆی داوەتەوە',
        'Someone liked your message': 'کەسێک نامەکەی تۆی بەدڵ بووە',
        'You have been banned': 'تۆ بلۆک کراویت',
        'You have been muted': 'تۆ بێدەنگ کراویت',
        'Your account has been banned': 'ئەکاونتەکەت بلۆک کراوە',
        'Your account has been muted': 'ئەکاونتەکەت بێدەنگ کراوە',
        'Your ban has been lifted': 'بلۆکەکەت لابراوە',
        'Your mute has been lifted': 'بێدەنگیەکەت لابراوە',
        'Your account has been unbanned': 'ئەکاونتەکەت بلۆکی لابراوە',
        'Your account has been unmuted': 'ئەکاونتەکەت بێدەنگی لابراوە',

        // ترجمات الصفحة الرئيسية
        'selectChatRoomToStart': 'گروپێک هەڵبژێرە بۆ دەستپێکردن',
        'availableChatRooms': 'گروپەکانی بەردەست',
        'loading': 'چاوەڕێ بکە...',
        'records': 'تۆمارەکان',
        'adminManagement': 'بەڕێوەبردنی ئەدمینەکان',
        'luckyWheelManagement': 'بەڕێوەبردنی چەرخی بەخت',
        'checkRewardCodes': 'پشکنینی کۆدەکانی خەڵات',
        'hide': 'شاردنەوە',
        'errorRoomIdNotFound': 'هەڵە: ناسنامەی ژوور نەدۆزرایەوە',
        'errorRoomNotFound': 'هەڵە: ژوور نەدۆزرایەوە',
        'errorDeletingGroup': 'هەڵە لە سڕینەوەی گروپ',
        'groupHiddenFromHomepage': 'گروپەکە لە لاپەڕەی سەرەکی شاردرایەوە',
        'errorHidingGroup': 'هەڵە لە شاردنەوەی گروپ',
        'confirmGroupDeletion': 'دڵنیاکردنەوەی سڕینەوەی گروپ',
        'wasTheSaleSuccessful': 'ئایا فرۆشتنەکە سەرکەوتوو بوو؟',
        'confirmHidingGroup': 'دڵنیاکردنەوەی شاردنەوەی گروپ',
        'areYouSureHideGroup': 'ئایا دڵنیای لە شاردنەوەی گروپی',
        'fromHomepage': 'لە لاپەڕەی سەرەکی',
        'noteShowGroupAgainFromRecords': 'تێبینی: دەتوانی گروپەکە دووبارە لە لاپەڕەی تۆمارەکان نیشان بدەیتەوە',
        'groupDeletedSuccessfully': 'گروپەکە بە سەرکەوتوویی سڕایەوە',
        'attachedFile': 'فایلی هاوپێچ',
        'noMessagesYet': 'هێشتا هیچ نامەیەک نیە...',

        // ترجمات سياسات الخصوصية
        'termsOfUseAndPolicies': 'مەرجەکانی بەکارهێنان و سیاسەتەکانی ئەپلیکەیشن',
        'allowedAge': 'تەمەنی ڕێگەپێدراو',
        'ageRequirement': 'پێویستە تەمەنی بەکارهێنەر ١٤ ساڵ یان زیاتر بێت بۆ بەکارهێنانی ئەپلیکەیشن.',
        'appPurpose': 'ئامانجی ئەپلیکەیشن',
        'appPurposeDescription1': 'ئەم ئەپلیکەیشنە دیزاین کراوە بۆ ئاسانکردنی مامەڵەی باش و سەلامەت لە نێوان فرۆشیار و کڕیار.',
        'appPurposeDescription2': 'ئەم ئەپلیکەیشنە هیچ بەرپرسیارێتییەک ناگرێتە ئەستۆ لە کاتی ڕوودانی فێڵ، ساختەکاری، یان دزینی ئەکاونت/پارە لە دەرەوەی سیستەمی فەرمی ئەپلیکەیشن.',
        'buyingAndSellingInApp': 'کڕین و فرۆشتن لە ناو ئەپلیکەیشن',
        'buyingAndSellingDescription1': 'بەکارهێنەران دەتوانن ئەکاونتەکانیان بۆ فرۆشتن پیشان بدەن یان بگەڕێن بۆ ئەکاونت بۆ کڕین لە ڕێگەی بەشەکان و گروپە بەردەستەکان.',
        'buyingAndSellingDescription2': 'قەدەغەیە ناردنی پارە یان زانیاری ئەکاونت بۆ هەر کەسێک لە ناو چاتی تایبەت بەبێ چاودێری بەڕێوەبەرایەتی.',
        'safeDealingMechanism': 'میکانیزمی مامەڵەی سەلامەت',
        'safeDealingDescription1': 'کاتێک ڕێککەوتن دەکرێت لە نێوان فرۆشیار و کڕیار، سەرپەرشتیار ئاگادار دەکرێتەوە.',
        'safeDealingDescription2': 'سەرپەرشتیار (کە نیشانەی شین هەیە و ناوی بە ڕەنگی شین دەردەکەوێت) گروپێکی تایبەت دروست دەکات کە فرۆشیار و کڕیار لەخۆ دەگرێت.',
        'safeDealingDescription3': 'هەردوو لایەن زیاد دەکرێن بۆ ئەم گروپە بە بەکارهێنانی ژمارەی ID تایبەت بە خۆیان.',
        'safeDealingDescription4': 'لە ناو ئەم گروپە، فرۆشیار زانیاری ئەکاونت دەنێرێت بۆ کڕیار دوای ئەوەی سەرپەرشتیار پشتڕاستی دەکاتەوە کە بڕی پارەکە وەرگیراوە.',
        'paymentMechanism': 'میکانیزمی پارەدان و گواستنەوە',
        'paymentDescription1': 'پارە لە کڕیارەوە دەنێردرێت بۆ بەڕێوەبەرایەتی/سەرپەرشتیار.',
        'paymentDescription2': 'دوای وەرگرتنی پارە، فرۆشیار زانیاری ئەکاونت دەنێرێت لە ناو گروپەکە.',
        'paymentDescription3': 'کڕیار زانیاری ئەکاونت دەگۆڕێت لە ماوەی یەک کاتژمێردا.',
        'paymentDescription4': 'دوای گۆڕانکاری، پشتڕاست دەکرێتەوە لە لایەن سەرپەرشتیارەوە کە ئەکاونتەکە بە تەواوی بووە بە موڵکی کڕیار.',
        'paymentDescription5': 'دواتر، بەڕێوەبەرایەتی پارەکە دەگوازێتەوە بۆ فرۆشیار.',
        'problemOccurrence': 'لە کاتی ڕوودانی کێشە',
        'problemDescription1': 'ئەگەر کڕیار لەبیری بچێت زانیاری ئەکاونت بگۆڕێت، و ئەکاونتەکە لە لایەن فرۆشیارەوە گەڕێندرایەوە دوای مامەڵەکە، بەڕێوەبەرایەتی هیچ بەرپرسیارێتییەک ناگرێتە ئەستۆ.',
        'problemDescription2': 'لە حاڵەتی درۆکردنی یەکێک لە لایەنەکان (وەک ئیدیعاکردنی زانیاری ساختە)، حاڵەتەکە ڕاستەوخۆ لە لایەن خاوەنەوە پێداچوونەوەی بۆ دەکرێت.',
        'problemDescription3': 'ئەگەر نیازی فێڵکردن لە یەکێک لە لایەنەکان سەلمێندرا، ئەکاونتەکەی بە یەکجاری قەدەغە دەکرێت.',
        'problemDescription4': 'ئەگەر بەڕێوەبەرایەتی نەیتوانی دیاری بکات کێ هەڵەیە، کێشەکە دەگوازرێتەوە بۆ لایەنە یاساییەکان.',
        'disputes': 'ناکۆکییەکان',
        'disputesDescription': 'ئەگەر بەڕێوەبەرایەتی ئەپلیکەیشن نەیتوانی دیاری بکات کێ فێڵبازە:',
        'disputePoint1': 'بڕی پارەکە بەستراو دەبێت هەتا لێکۆڵینەوەکان تەواو دەبن.',
        'disputePoint2': 'ڕێگە دەدرێت بە بەڕێوەبەرایەتی کە بڕی پارەکە بە کاتی بگرێت بۆ دڵنیابوون لە مافەکانی هەردوو لا.',
        'disputePoint3': 'ئەگەر کێشەکە چارەسەر نەکرا، کێشەکە دەگوازرێتەوە بۆ یاسا، و پێویستە لەسەر هەردوو لا ڕێکارە یاساییەکان بگرنە بەر.',
        'chatBehavior': 'ڕەفتار لە چات',
        'chatBehaviorDescription1': 'بە تەواوی قەدەغەیە بەکارهێنانی وشەی ناشیرین، جنێودان، یان فێڵکردن لە ناو چاتەکان.',
        'chatBehaviorDescription2': 'هەر پێشێلکارییەک دەبێتە هۆی قەدەغەکردنی هەمیشەیی یان کاتی بەپێی توندی حاڵەتەکە.',

        // ترجمات صفحة تأكيد البريد الإلكتروني
        'verified': 'پشتڕاست کرایەوە',
        'accountCreatedSuccessfully': 'ئەکاونت بە سەرکەوتوویی دروست کرا!',
        'pleaseCheckInbox': 'تکایە سەردانی <strong>ئینبۆکسی ئیمەیڵەکەت</strong> بکە و کلیک لەسەر لینکی چالاککردن بکە بۆ تەواوکردنی تۆمارکردنی ئەکاونت.',

        // ترجمات معلومات المجموعة وتعديلها
        'updatedSuccessfully': 'بە سەرکەوتوویی نوێکرایەوە',
        'errorUpdatingRoomPrivacy': 'هەڵە لە نوێکردنەوەی تایبەتمەندی ژوور',
        'allowedUsersListUpdatedSuccessfully': 'لیستی بەکارهێنەرە ڕێگەپێدراوەکان بە سەرکەوتوویی نوێکرایەوە',
        'errorUpdatingUsersList': 'هەڵە لە نوێکردنەوەی لیستی بەکارهێنەران',
        'errorDeletingChat': 'هەڵە لە سڕینەوەی چات',
        'chatDeletedSuccessfully': 'چات بە سەرکەوتوویی سڕایەوە',
        'editRoom': 'دەستکاری ژوور',
        'editGroup': 'دەستکاری گروپ',
        'roomNameRequired': 'ناوی ژوور پێویستە',
        'roomDescriptionRequired': 'وەسفی ژوور پێویستە',
        'privateRoom': 'ژووری تایبەت',
        'publicRoom': 'ژووری گشتی',
        'privateRoomDescription': 'ژووری تایبەت تەنها بۆ بەکارهێنەرە دیاریکراوەکان دەردەکەوێت',
        'publicRoomDescription': 'ژووری گشتی بۆ هەموو بەکارهێنەران دەردەکەوێت',
        'allowedUser1': 'بەکارهێنەری ڕێگەپێدراو 1',
        'allowedUser2': 'بەکارهێنەری ڕێگەپێدراو 2',
        'allowedUser3': 'بەکارهێنەری ڕێگەپێدراو 3',
        'allowedUser4': 'بەکارهێنەری ڕێگەپێدراو 4',
        'enterUserID': 'ناسنامەی بەکارهێنەر (UID) بنووسە',
        'updateUsersList': 'نوێکردنەوەی لیستی بەکارهێنەران',
        'preferEnterUserID': 'باشترە ناسنامەی بەکارهێنەر (UID) بنووسیت بۆ زیادکردن بۆ ژوور',
        'getUIDFromProfile': 'دەتوانیت ناسنامەی بەکارهێنەر (UID) لە پڕۆفایلی بەکارهێنەرەوە بەدەست بهێنیت',
        'noDescription': 'وەسف نییە',
        'confirmDeleteChat': 'دڵنیاکردنەوەی سڕینەوەی چات',
        'areYouSureDeleteChat': 'ئایا دڵنیای لە سڕینەوەی ئەم چاتە؟ هەموو نامەکان بە یەکجاری دەسڕدرێنەوە.',
        'errorNoPermissionToDeleteGroup': 'هەڵە: مافی سڕینەوەی ئەم گروپەت نییە',
      }
    };

    // محاولة العثور على ترجمة تلقائية
    if (autoTranslations[currentLanguage] && autoTranslations[currentLanguage][key]) {
      return autoTranslations[currentLanguage][key];
    }

    // إذا لم يتم العثور على ترجمة، إرجاع المفتاح نفسه
    return key;
  };

  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage, t, LANGUAGES }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook لاستخدام سياق اللغة
export const useLanguage = () => useContext(LanguageContext);
