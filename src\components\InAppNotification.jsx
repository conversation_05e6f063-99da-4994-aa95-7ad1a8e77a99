import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router';
import { Animation, Message } from 'rsuite';
import TranslatedText from './TranslatedText';
import { ref, set, update } from 'firebase/database';
import { database } from '../misc/firebase.config';
import { useProfile } from '../context/profile.context';

/**
 * مكون الإشعارات داخل التطبيق
 * يعرض إشعارًا في أعلى الشاشة لمدة محددة ثم يختفي تلقائيًا
 * يمكن النقر على الإشعار للانتقال إلى الصفحة المرتبطة به
 */
const InAppNotification = ({ notification, onClose }) => {
  const history = useHistory();
  const { profile } = useProfile();
  const [show, setShow] = useState(true);
  const [saved, setSaved] = useState(false);

  // دالة لحفظ الإشعار في صفحة الإشعارات
  const saveNotificationToHistory = useCallback(async () => {
    if (!profile || !profile.uid || !notification || saved) return;

    try {
      console.log('حفظ الإشعار في صفحة الإشعارات:', notification.type);

      // إنشاء معرف فريد للإشعار
      const notificationId = `notification_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // إضافة حقول إضافية للإشعار
      const currentTime = Date.now();
      const notificationToSave = {
        ...notification,
        id: notificationId,
        read: false,
        createdAt: currentTime,
        timestamp: currentTime
      };

      // قائمة المسارات المحتملة للإشعارات
      const notificationPaths = [
        `notifications/${profile.uid}/${notificationId}`,
        `user_notifications/${profile.uid}/${notificationId}`,
        `user_data/${profile.uid}/notifications/${notificationId}`
      ];

      // محاولة حفظ الإشعار في جميع المسارات المحتملة
      let success = false;

      for (const path of notificationPaths) {
        try {
          await set(ref(database, path), notificationToSave);
          console.log('تم حفظ الإشعار في المسار:', path);
          success = true;
          break; // الخروج من الحلقة بعد النجاح
        } catch (error) {
          console.warn('فشل في حفظ الإشعار في المسار:', path, error);
        }
      }

      if (!success) {
        // محاولة أخيرة باستخدام update
        try {
          const updates = {};
          updates[`notifications/${profile.uid}/${notificationId}`] = notificationToSave;
          await update(ref(database), updates);
          console.log('تم حفظ الإشعار باستخدام update');
          success = true;
        } catch (error) {
          console.error('فشل في جميع محاولات حفظ الإشعار:', error);
        }
      }

      if (success) {
        setSaved(true);
        console.log('تم حفظ الإشعار بنجاح في صفحة الإشعارات');
      }
    } catch (error) {
      console.error('خطأ في حفظ الإشعار في صفحة الإشعارات:', error);
    }
  }, [profile, notification, saved, setSaved]);

  // تأثير لإخفاء الإشعار بعد مدة محددة وحفظه في صفحة الإشعارات
  useEffect(() => {
    if (!notification) return;

    console.log('عرض إشعار جديد:', notification.type, 'في:', new Date().toLocaleTimeString());

    // حفظ الإشعار في صفحة الإشعارات فور ظهوره
    saveNotificationToHistory();

    // إنشاء مؤقت لإخفاء الإشعار بعد 5 ثوانٍ بالضبط
    const hideTimer = setTimeout(() => {
      console.log('بدء إخفاء الإشعار بعد 5 ثوانٍ:', new Date().toLocaleTimeString());

      // بدء حركة الاختفاء التدريجي
      setShow(false);
    }, 5000); // 5 ثوانٍ بالضبط

    // إنشاء مؤقت منفصل لإغلاق الإشعار بعد انتهاء حركة الاختفاء
    const closeTimer = setTimeout(() => {
      console.log('إغلاق الإشعار بعد انتهاء الرسوم المتحركة:', new Date().toLocaleTimeString());

      // التأكد من حفظ الإشعار في صفحة الإشعارات قبل إغلاقه
      if (!saved) {
        saveNotificationToHistory().then(() => {
          onClose();
        }).catch(() => {
          onClose();
        });
      } else {
        onClose();
      }
    }, 6000); // 5 ثوانٍ + 1 ثانية للحركة

    // إلغاء المؤقتات عند إلغاء تحميل المكون
    return () => {
      console.log('إلغاء مؤقتات الإشعار');
      clearTimeout(hideTimer);
      clearTimeout(closeTimer);
    };
  }, [notification, onClose, saved, saveNotificationToHistory]);

  // إذا لم يكن هناك إشعار، لا تعرض شيئًا
  if (!notification) return null;

  // معالجة النقر على الإشعار
  const handleClick = async () => {
    console.log('تم النقر على الإشعار:', notification.type, 'في:', new Date().toLocaleTimeString());

    // التأكد من حفظ الإشعار في صفحة الإشعارات قبل الانتقال
    if (!saved) {
      await saveNotificationToHistory();
    }

    // الانتقال إلى الرابط المرتبط بالإشعار
    if (notification.link) {
      console.log('الانتقال إلى الرابط:', notification.link);
      history.push(notification.link);
    }

    // بدء حركة الاختفاء التدريجي
    setShow(false);

    // إغلاق الإشعار بعد انتهاء الرسوم المتحركة (1 ثانية)
    setTimeout(() => {
      console.log('إغلاق الإشعار بعد النقر عليه:', new Date().toLocaleTimeString());
      onClose();
    }, 1000);
  };

  // تحديد نوع الإشعار (نجاح، معلومات، تحذير، خطأ)
  const getNotificationType = () => {
    switch (notification.type) {
      case 'reply':
        return 'info';
      case 'privateMessage':
        return 'success';
      case 'ownerMessage':
        return 'warning';
      default:
        return 'info';
    }
  };

  // تحديد أيقونة الإشعار
  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'reply':
        return <i className="fa-solid fa-reply"></i>;
      case 'privateMessage':
        return <i className="fa-solid fa-envelope"></i>;
      case 'ownerMessage':
        return <i className="fa-solid fa-bullhorn"></i>;
      default:
        return <i className="fa-solid fa-bell"></i>;
    }
  };

  return (
    <Animation.Slide in={show} placement="top" timeout={1000}>
      <div className="in-app-notification-container">
        <Message
          showIcon
          type={getNotificationType()}
          closable
          onClick={handleClick}
          onClose={async (e) => {
            // إيقاف انتشار الحدث لمنع تنفيذ handleClick
            e.stopPropagation();
            console.log('تم النقر على زر الإغلاق في:', new Date().toLocaleTimeString());

            // التأكد من حفظ الإشعار في صفحة الإشعارات قبل إغلاقه
            if (!saved) {
              await saveNotificationToHistory();
            }

            // بدء حركة الاختفاء التدريجي
            setShow(false);

            // إغلاق الإشعار بعد انتهاء الرسوم المتحركة (1 ثانية)
            setTimeout(() => {
              console.log('إغلاق الإشعار بعد النقر على زر الإغلاق:', new Date().toLocaleTimeString());
              onClose();
            }, 1000);
          }}
          className={`in-app-notification ${show ? 'show' : 'hide'}`}
        >
          <div className="notification-content">
            <div className="notification-icon">
              {getNotificationIcon()}
            </div>
            <div className="notification-text">
              <div className="notification-title">
                {notification.title || <TranslatedText text={`${notification.type}Notification`} />}
              </div>
              <div className="notification-body">
                {notification.body}
              </div>
            </div>
          </div>
        </Message>
      </div>
    </Animation.Slide>
  );
};

export default InAppNotification;
