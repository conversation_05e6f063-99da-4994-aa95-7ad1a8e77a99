import React, { useEffect, useState } from 'react';
import { Container, Content, Header, List, Avatar, Loader, Button } from 'rsuite';
import { useHistory } from 'react-router-dom';
import { database, auth } from '../misc/firebase.config';
import { ref, onValue, get, query, orderByChild, equalTo } from 'firebase/database';
import ProfileAvatar from '../components/ProfileAvatar';
import { transformToArr } from '../misc/helpers';
import TimeAgo from 'timeago-react';
import { useProfile } from '../context/profile.context';
import ArrowLeftLine from '@rsuite/icons/ArrowLeftLine';

const PrivateChats = () => {
  const history = useHistory();
  const { profile } = useProfile();
  const [privateChats, setPrivateChats] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const currentUserId = auth.currentUser.uid;
      const roomsRef = ref(database, 'rooms');

      // استمع للتغييرات في المجموعات
      const unsubscribe = onValue(roomsRef, async snapshot => {
        try {
          const chatsArray = [];

          if (snapshot.exists()) {
            const roomsData = snapshot.val();

            // تحويل البيانات إلى مصفوفة
            for (const roomId in roomsData) {
              const room = roomsData[roomId];

              // تحقق مما إذا كانت المجموعة خاصة وخاصة بين شخصين
              if (room.isDirectMessage && room.members && room.members[currentUserId]) {
                // احصل على معرف المستخدم الآخر
                const otherUserId = Object.keys(room.members).find(id => id !== currentUserId);

                if (otherUserId) {
                  try {
                    // احصل على بيانات المستخدم الآخر
                    const userRef = ref(database, `users/${otherUserId}`);
                    const userSnapshot = await get(userRef);

                    if (userSnapshot.exists()) {
                      const userData = userSnapshot.val();

                      // احصل على آخر رسالة
                      let lastMessage = 'لا توجد رسائل';
                      let lastMessageTime = room.createdAt || null;

                      // احصل على آخر رسالة من المجموعة
                      const messagesRef = ref(database, `messages/${roomId}`);
                      const messagesSnapshot = await get(messagesRef);

                      if (messagesSnapshot.exists()) {
                        const messagesData = messagesSnapshot.val();
                        const messagesArray = Object.values(messagesData);

                        // ترتيب الرسائل حسب وقت الإنشاء
                        messagesArray.sort((a, b) => {
                          const timeA = a.createdAt || 0;
                          const timeB = b.createdAt || 0;
                          return timeB - timeA; // ترتيب تنازلي
                        });

                        if (messagesArray.length > 0) {
                          const latestMessage = messagesArray[0];
                          lastMessage = latestMessage.content;
                          lastMessageTime = latestMessage.createdAt;
                        }
                      }

                      chatsArray.push({
                        id: roomId,
                        name: userData.name,
                        avatar: userData.avatar,
                        lastMessage,
                        lastMessageTime,
                        userId: otherUserId,
                        isRoom: true
                      });
                    }
                  } catch (userError) {
                    console.error('Error fetching user data:', userError);
                  }
                }
              }
            }

            // ترتيب الدردشات حسب وقت آخر رسالة
            chatsArray.sort((a, b) => {
              if (!a.lastMessageTime) return 1;
              if (!b.lastMessageTime) return -1;
              return b.lastMessageTime - a.lastMessageTime;
            });
          }

          setPrivateChats(chatsArray);
          setIsLoading(false);
        } catch (error) {
          console.error('Error processing chats data:', error);
          setIsLoading(false);
        }
      }, error => {
        console.error('Error fetching private chats:', error);
        setIsLoading(false);
      });

      return () => {
        unsubscribe();
      };
    } catch (error) {
      console.error('Error setting up private chats listener:', error);
      setIsLoading(false);
    }
  }, []);

  const handleChatSelect = chat => {
    // دائماً استخدم مسار الدردشة العادية للدردشات الخاصة
    // هذا يضمن أن الدردشات الخاصة تعمل بشكل صحيح
    history.push(`/chat/${chat.id}`);
  };

  const handleBack = () => {
    // التحقق من وجود صفحة سابقة في تاريخ التصفح
    if (window.history.length > 1) {
      // إذا كان هناك صفحة سابقة، استخدم history.goBack()
      history.goBack();
    } else {
      // إذا لم يكن هناك صفحة سابقة، انتقل إلى الصفحة الرئيسية
      history.push('/');
    }
  };

  return (
    <Container className="dark-container">
      <Header className="dark-header">
        <div className="header-container">
          <Button
            appearance="subtle"
            className="back-btn"
            onClick={handleBack}
          >
            <ArrowLeftLine />
          </Button>
          <h4 className="header-title">الدردشات الخاصة</h4>
        </div>
      </Header>
      <Content className="dark-content">
        {isLoading ? (
          <div className="loader-container">
            <Loader center content="جاري تحميل الدردشات..." />
          </div>
        ) : (
          <div className="private-chats-container">
            {privateChats.length === 0 ? (
              <div className="no-chats-message">
                <p>لا توجد دردشات خاصة</p>
                <p className="no-chats-hint">يمكنك بدء دردشة خاصة من خلال الضغط على زر "إرسال رسالة" في الملف الشخصي لأي مستخدم</p>
              </div>
            ) : (
              <List hover>
                {privateChats.map(chat => (
                  <List.Item key={chat.id} onClick={() => handleChatSelect(chat)} className="private-chat-item">
                    <div className="private-chat-avatar">
                      <ProfileAvatar
                        src={chat.avatar}
                        name={chat.name}
                        className="private-chat-profile-avatar"
                      />
                    </div>
                    <div className="private-chat-info">
                      <div className="private-chat-name">
                        {chat.name}
                      </div>
                      <div className="private-chat-last-message">
                        <div className="last-message-text">
                          {chat.lastMessage === 'لا توجد رسائل' ? 'ابدأ محادثة جديدة الآن' : chat.lastMessage}
                        </div>
                        {chat.lastMessageTime && (
                          <span className="private-chat-time">
                            <TimeAgo datetime={new Date(chat.lastMessageTime)} locale="ar" />
                          </span>
                        )}
                      </div>
                    </div>
                  </List.Item>
                ))}
              </List>
            )}
          </div>
        )}
      </Content>
    </Container>
  );
};

export default PrivateChats;
